.flex-cc {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* start--文本行数限制--start */
.line-1 {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.line-2 {
  -webkit-line-clamp: 2;
}

.line-3 {
  -webkit-line-clamp: 3;
}

.line-4 {
  -webkit-line-clamp: 4;
}

.line-5 {
  -webkit-line-clamp: 5;
}

.line-2,
.line-3,
.line-4,
.line-5 {
  flex: 1;
  overflow: hidden;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box; // 弹性伸缩盒
  -webkit-box-orient: vertical; // 设置伸缩盒子元素排列方式
}

@mixin base-scrollbar {
  &::-webkit-scrollbar {
    width: 12px;
    height: 12px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: #c0c0c0;
    background-clip: padding-box;
    border: 3px solid transparent;
    border-radius: 7px;
  }
  &::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.5);
  }
  &::-webkit-scrollbar-track {
    background-color: transparent;
  }
  &::-webkit-scrollbar-track:hover {
    background-color: #f8fafc;
  }
  &::-webkit-scrollbar-corner {
    background: transparent; //解决有横向滚动条时右下角有白色方块
  }
}

* {
  box-sizing: border-box;
  @include base-scrollbar;
}

.no-bar {
  &::-webkit-scrollbar {
    display: none;
  }
}

.mathjax-p {
  p {
    white-space: break-spaces;
  }
}

.AlimamaShuHeiTi {
  font-family: AlimamaShuHeiTi;
}

.D-DIN-Bold {
  font-family: D-DIN-Bold;
}
