<template>
  <div>
    <Practice
      v-if="type == 2"
      :type="type"
      :sysTextbooksCatalogId="sysTextbooksCatalogId"
    ></Practice>
    <Test
      v-else
      :type="type"
      :sysTextbooksCatalogId="sysTextbooksCatalogId"
    ></Test>
  </div>
</template>
<script setup lang="ts">
import Practice from './components/Practice.vue'
import Test from './components/Test.vue'

const route = useRoute()
const type = $computed(() => {
  return route.query.type ? Number(route.query.type) : 1
})

const sysTextbooksCatalogId = $computed(() => {
  return route.query.sysTextbooksCatalogId
    ? Number(route.query.sysTextbooksCatalogId)
    : 185429
})
</script>
