/**
 * AI API交互服务
 * 基于@cjh0/fetch-event-source封装SSE流式请求
 */

import { fetchEventSource } from '@cjh0/fetch-event-source'
import netConfig from '@/config/net.config'
import { useUserStore } from '@/stores/modules/user'

/** AI消息类型 */
interface IAIMessage {
  /** 角色 */
  role: 'system' | 'user' | 'assistant'
  /** 消息内容 */
  content: string
}

/** AI聊天请求参数 */
interface IAIChatRequest {
  /** 对话ID */
  conversationId: string
  /** 消息内容 */
  content: string
}

/** 流式响应回调函数类型 */
interface IAIStreamCallbacks {
  /** 接收到数据时的回调 */
  onMessage?: (content: string) => void
  /** 流式结束时的回调 */
  onComplete?: () => void
  /** 错误时的回调 */
  onError?: (error: any) => void
  /** 连接打开时的回调 */
  onOpen?: () => void
}

/** AI服务类 */
class AIService {
  // API基础URL
  private baseURL: string

  /**
   * 构造函数
   */
  constructor() {
    this.baseURL = netConfig.baseURL || ''
  }

  /**
   * 发送流式聊天请求
   * @param request 聊天请求参数
   * @param callbacks 流式响应回调函数
   * @returns AbortController 用于取消请求
   */
  async streamChat(
    request: IAIChatRequest,
    callbacks: IAIStreamCallbacks = {},
  ): Promise<AbortController> {
    const { onMessage, onComplete, onError, onOpen } = callbacks
    const controller = new AbortController()

    // 获取用户store中的token
    const userStore = useUserStore()
    const { jztToken, version, platform } = storeToRefs(userStore)

    try {
      await fetchEventSource(
        `${this.baseURL}/aienglish/user/chat/conversation`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Accept: 'text/event-stream',
            'Cache-Control': 'no-cache',
            Connection: 'keep-alive',
            // 防止代理缓冲
            'X-Accel-Buffering': 'no',
            'X-Proxy-Buffering': 'no',
            token: jztToken.value || '',
            version: version.value || '1.2.2',
            platform: platform.value || 'YUNXIAO_STUDENT_H5',
          },
          body: JSON.stringify({
            conversationId: request.conversationId,
            content: request.content,
          }),
          signal: controller.signal,

          /** 连接打开回调 */
          async onopen(response) {
            if (response.ok) {
              console.log('SSE连接已建立')
              onOpen?.()
              return // 正常流式响应
            } else if (
              response.status >= 400 &&
              response.status < 500 &&
              response.status !== 429
            ) {
              // 客户端错误，不重试
              throw new Error(`HTTP ${response.status}`)
            } else {
              // 其他错误，可能重试
              throw new Error(`HTTP ${response.status}`)
            }
          },

          /** 接收消息回调 */
          onmessage(event: any) {
            console.log(`⚡[ event ] >`, event)

            try {
              // 组装所有调试数据
              const debugData: any = {
                timestamp: new Date().toISOString(),
                eventOriginal: {
                  data: event.data,
                  type: event.type,
                  lastEventId: event.lastEventId,
                  origin: event.origin,
                  source: event.source,
                  ports: event.ports,
                },
                processing: {},
              }

              // 获取原始数据
              let data = event.data
              debugData.processing.originalData = data
              debugData.processing.originalDataType = typeof data
              debugData.processing.originalDataLength = data?.length || 0

              // 检查data:前缀
              debugData.processing.hasDataPrefix = data.startsWith('data:')

              if (data.startsWith('data:')) {
                data = data.substring(5).trim()
                debugData.processing.afterRemovePrefix = data
              }

              // 数据有效性检查
              debugData.processing.isDataValid = !!(data && data.trim() !== '')
              debugData.processing.trimmedData = data?.trim()
              debugData.processing.trimmedLength = data?.trim()?.length || 0

              // 尝试解析JSON
              let jsonData: any = null
              let parseSuccess = false
              try {
                if (data && data.trim() !== '') {
                  jsonData = JSON.parse(data)
                  parseSuccess = true
                  debugData.processing.jsonParseSuccess = true
                  debugData.processing.parsedJson = jsonData
                } else {
                  debugData.processing.jsonParseSuccess = false
                  debugData.processing.skipReason = 'data为空或trim后为空'
                }
              } catch (parseError: any) {
                debugData.processing.jsonParseSuccess = false
                debugData.processing.parseError = parseError.message
                debugData.processing.parsedJson = null
              }

              // 分析text字段
              if (parseSuccess && jsonData) {
                debugData.processing.textAnalysis = {
                  hasTextProperty: 'text' in jsonData,
                  textValue: jsonData.text,
                  textType: typeof jsonData.text,
                  textIsTruthy: !!jsonData.text,
                  textIsString: typeof jsonData.text === 'string',
                  textTrimmed:
                    typeof jsonData.text === 'string'
                      ? jsonData.text.trim()
                      : null,
                  textTrimmedNotEmpty:
                    typeof jsonData.text === 'string'
                      ? jsonData.text.trim() !== ''
                      : false,
                  allConditionsMet: !!(
                    jsonData.text &&
                    typeof jsonData.text === 'string' &&
                    jsonData.text.trim() !== ''
                  ),
                }

                // 分析finished字段
                debugData.processing.finishedAnalysis = {
                  hasFinishedProperty: 'finished' in jsonData,
                  finishedValue: jsonData.finished,
                  finishedType: typeof jsonData.finished,
                  finishedIsTrue: jsonData.finished === true,
                }
              }

              // 回调触发分析
              debugData.processing.callbackTrigger = {
                willTriggerOnMessage: false,
                willTriggerOnComplete: false,
                onMessageContent: null,
              }

              // 打印完整调试对象
              console.log('🔍 完整SSE调试数据:', debugData)

              // 根据后端返回的数据结构解析
              if (parseSuccess && jsonData) {
                // 检查onMessage触发条件
                if (
                  jsonData.text &&
                  typeof jsonData.text === 'string' &&
                  jsonData.text.trim() !== ''
                ) {
                  debugData.processing.callbackTrigger.willTriggerOnMessage =
                    true
                  debugData.processing.callbackTrigger.onMessageContent =
                    jsonData.text

                  console.log('✅ 即将触发onMessage:', {
                    content: jsonData.text,
                    contentLength: jsonData.text.length,
                    timestamp: new Date().toISOString(),
                  })

                  onMessage?.(jsonData.text)

                  console.log('✅ onMessage已触发完成')
                } else {
                  console.log(
                    '❌ 不满足onMessage触发条件:',
                    debugData.processing.textAnalysis,
                  )
                }

                // 检查是否完成
                if (jsonData.finished === true) {
                  debugData.processing.callbackTrigger.willTriggerOnComplete =
                    true

                  console.log('✅ 即将触发onComplete:', {
                    finished: jsonData.finished,
                    timestamp: new Date().toISOString(),
                  })

                  onComplete?.()

                  console.log('✅ onComplete已触发完成')
                  return
                }
              }

              // 最终状态打印
              console.log(
                '🔍 最终处理状态:',
                debugData.processing.callbackTrigger,
              )
            } catch (error: any) {
              console.error('💥 处理SSE消息时发生错误:', {
                error: error.message,
                stack: error.stack,
                event: event,
                timestamp: new Date().toISOString(),
              })
            }
          },

          /** 错误回调 */
          onerror(error) {
            console.error('SSE连接错误:', error)
            onError?.(error)
            throw error // 停止重连
          },

          /** 关闭回调 */
          onclose() {
            console.log('SSE连接已关闭')
          },
        },
      )
    } catch (error) {
      console.error('流式请求失败:', error)
      onError?.(error)
    }

    return controller
  }
}

// 创建默认AI服务实例
export const aiService = new AIService()

// 导出类型和服务
export {
  AIService,
  type IAIMessage,
  type IAIChatRequest,
  type IAIStreamCallbacks,
}
