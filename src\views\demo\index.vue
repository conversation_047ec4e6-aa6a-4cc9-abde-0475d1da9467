<template>
  <div>
    <g-chart class="w-350px h-250px" :option="chartOption" />
  </div>
</template>

<script setup lang="ts">
const chartOption = reactive({
  tooltip: {
    formatter: '{a} <br/>{b} : {c}%',
  },
  series: [
    {
      name: 'Pressure',
      type: 'gauge',
      detail: {
        formatter: '{value}',
      },
      data: [
        {
          value: 50,
          name: 'SCORE',
        },
      ],
    },
  ],
})
</script>

<style lang="scss" scoped></style>
