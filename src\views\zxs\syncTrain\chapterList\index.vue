<template>
  <div>
    <g-navbar customTitle="校本同步" class="wt-bar" />
    <div
      class="h-38px w-full flex items-end pb-4px bg-white px-16px text-15px text-[#323233]"
    >
      <div
        class="flex items-center mr-48px arrow-btm van-haptics-feedback"
        @click="showPanel(true)"
      >
        <div class="max-w-150px truncate">
          {{ currentTextbook?.text || '请选择教材' }}
        </div>
      </div>
      <div
        class="flex items-center arrow-btm van-haptics-feedback"
        @click="showPanel(false)"
      >
        <div class="max-w-150px truncate">
          {{ currentVersion?.text || '请选择版本' }}
        </div>
      </div>
    </div>
    <div class="w-full h-44px bg-white overflow-hidden px-15px">
      <van-tabs
        v-model:active="activeSubject"
        class="sync-train-tab"
        @change="getTextbookListApi"
      >
        <van-tab
          v-for="item in subjectList"
          :key="item.sysSubjectId"
          :name="item.sysSubjectId"
          :title="item.sysSubjectName"
        ></van-tab>
      </van-tabs>
    </div>
    <div class="mt-16px px-16px box-h overflow-y-auto no-bar">
      <g-loading v-if="showLoading"></g-loading>
      <template v-else>
        <van-collapse
          v-if="chapterList.length"
          v-model="activeName"
          class="sync-train-collapse"
          accordion
        >
          <van-collapse-item
            v-for="item in chapterList"
            :key="item.sysTextbooksCatalogId"
            :name="item.sysTextbooksCatalogId"
            :is-link="false"
          >
            <template #title>
              <div
                class="flex pt-16px pb-12px items-start border-b border-solid border-[#F6F6F6]"
                @click.stop="setActiveName(item.sysTextbooksCatalogId)"
              >
                <div class="pt-4px">
                  <div
                    class="w-4px h-16px rounded-[0_16px_16px_0] bg-[#217DFB]"
                  ></div>
                </div>
                <div class="ml-12px">
                  <div class="flex items-center mb-4px">
                    <div
                      class="text-18px text-[#333] mr-8px h-25px leading-[25px]"
                    >
                      {{ item.chapterName }}
                    </div>
                    <img
                      src="@/assets/img/syncTrain/arrow-btm.png"
                      alt="arrow icon"
                      class="w-18px h-18px -translate-y-2px"
                      :class="{
                        'rotate-180': activeName === item.sysTextbooksCatalogId,
                      }"
                    />
                  </div>
                  <div
                    v-if="item.chapterDesc"
                    class="text-12px text-[#979797] h-17px leading-[17px]"
                  >
                    {{ item.chapterDesc }}
                  </div>
                </div>
              </div>
            </template>
            <div class="px-16px py-20px">
              <div
                v-for="(sub, idx) in item.children"
                :key="sub.sysTextbooksCatalogId"
                :class="{
                  'mt-20px': idx !== 0,
                }"
              >
                <div
                  class="w-full text-15px text-[#333333] truncate"
                  @click="toDetail"
                >
                  {{ sub.sysTextbooksCatalogNameAlias }}
                </div>
                <div class="flex items-center flex-wrap">
                  <div
                    v-for="week in sub.weekList"
                    :key="week.weekPhaseIndex"
                    class="min-w-40px px-5px mt-5px h-18px leading-[18px] text-center rounded-[2px] bg-[#217DFB1A] text-12px text-[#217DFB] mr-12px"
                    :class="{
                      '!bg-[#217DFB] !text-white':
                        week.schoolPhaseWeekId ===
                        Number(route.query.schoolPhaseWeekId),
                    }"
                    @click="onWeekClick(week)"
                  >
                    {{ week.weekTitle }}
                  </div>
                </div>
              </div>
            </div>
          </van-collapse-item>
        </van-collapse>
        <g-empty v-else></g-empty>
      </template>
    </div>
    <van-popup
      v-model:show="showPop"
      round
      position="bottom"
      class="sync-train-pop"
    >
      <van-picker
        :title="title"
        :columns="columns"
        @confirm="onConfirm"
        @cancel="showPop = false"
      />
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import {
  getWeekTaskList,
  getTextbookList,
  getChapterList,
} from '@/api/syncTrain'
import { useSettingStore } from '@/stores/modules/setting'

const settingStore = useSettingStore()
let showLoading = $ref(true)

let navHeight = $ref('0px')

const route = useRoute()
const router = useRouter()

let activeName = $ref<any>(null)

let showPop = $ref(false)
let isTextBook = $ref(false)
let currentTextbookId = $ref<any>(1)
let textbookList = $ref<any[]>([])
let currentVersionId = $ref<any>(1)
let versionList = $ref<any[]>([])

let activeSubject = $ref<any>(null)
let subjectList = $ref<any[]>([])

let chapterList = $ref<any[]>([])

const currentTextbook = $computed(() => {
  return textbookList.find((v) => v.value === currentTextbookId) || null
})

const currentVersion = $computed(() => {
  return versionList.find((v) => v.value === currentVersionId) || null
})

const columns = $computed(() => {
  return isTextBook ? textbookList : versionList
})
const title = $computed(() => {
  return isTextBook ? '选择版本' : '选择教材'
})

function onConfirm({ selectedValues: [val] }) {
  if (isTextBook) {
    currentTextbookId = val
  } else {
    currentVersionId = val
  }
  showPop = false
}

function showPanel(val) {
  isTextBook = val
  showPop = true
  nextTick(() => {
    const dom = document.querySelector('.sync-train-pop')
    if (dom) {
      let idx = -1
      const rowList: any = dom.getElementsByClassName('van-picker-column__item')
      if (isTextBook) {
        idx = textbookList.findIndex((v) => v.value === currentTextbookId)
      } else {
        idx = versionList.findIndex((v) => v.value === currentVersionId)
      }
      if (idx !== -1) {
        rowList[idx]?.click()
      }
    }
  })
}

function toDetail() {
  router.push({
    name: 'WeekTask',
    query: {
      schoolPhaseWeekId: route.query.schoolPhaseWeekId,
      sysSubjectId: activeSubject,
      title: `${subjectList.find((v) => v.sysSubjectId === activeSubject)?.sysSubjectName}${route.query.weekTitle}同步训练`,
      beginTime: route.query.beginTime,
      endTime: route.query.endTime,
    },
  })
}

async function getWeekTaskListApi() {
  const data = await getWeekTaskList({
    schoolPhaseWeekId: route.query.schoolPhaseWeekId,
  })
  activeSubject = Number(route.query.sysSubjectId) || 2
  subjectList = data
  if (subjectList) {
    nextTick(() => {
      getTextbookListApi()
    })
  }
}

async function getTextbookListApi() {
  try {
    showLoading = true
    const data = await getTextbookList({
      schoolPhaseId: route.query.schoolPhaseId,
      sysSubjectId: activeSubject,
      schoolPhaseWeekId: route.query.schoolPhaseWeekId,
    })
    if (data.length) {
      currentTextbookId = (data.find((v) => v.checked) || data[0])
        ?.sysTextbookVersionsId
      textbookList = data.map((v) => ({
        text: v.sysTextbookVersionsNameAlias,
        value: v.sysTextbookVersionsId,
        bookList: v.bookList,
      }))
    } else {
      reset()
    }
  } catch (e) {
    console.error(e)
    reset()
  }
}

function reset() {
  showLoading = false
  currentTextbookId = null
  textbookList = []
  currentVersionId = null
  versionList = []
  chapterList = []
}

async function getChapterListApi() {
  try {
    showLoading = true
    const data = await getChapterList({
      schoolPhaseId: route.query.schoolPhaseId,
      sysSubjectId: activeSubject,
      sysTextbooksId: currentVersionId,
      schoolPhaseWeekId: route.query.schoolPhaseWeekId,
    })
    if (data.length) {
      activeName =
        (data.find((v) => v.checked) || data[0])?.sysTextbooksCatalogId || null
      chapterList = data.map((v) => {
        const name = v.sysTextbooksCatalogNameAlias.trim().split(' ')
        return {
          ...v,
          chapterName: name[0] || '',
          chapterDesc: name[1] || '',
        }
      })
    } else {
      activeName = null
      chapterList = []
    }
    showLoading = false
  } catch (e) {
    console.error(e)
    showLoading = false
  }
}

function setActiveName(id) {
  activeName = activeName === id ? null : id
}

async function onWeekClick(week) {
  router.replace({
    query: {
      ...route.query,
      sysSubjectId: activeSubject,
      schoolPhaseWeekId: week.schoolPhaseWeekId,
      weekTitle: week.weekTitle,
      beginTime: week.beginTime,
      endTime: week.endTime,
    },
  })
  await nextTick()
  showLoading = true
  getWeekTaskListApi()
}

onBeforeMount(() => {
  navHeight = settingStore.navigationHeight + 'px'
  getWeekTaskListApi()
})

watch(
  () => currentTextbook,
  (val) => {
    if (val) {
      currentVersionId = (
        val.bookList.find((v) => v.checked) || val.bookList[0]
      )?.sysTextbooksId
      versionList = (val.bookList || []).map((v) => ({
        text: v.sysTextbooksNameAlias,
        value: v.sysTextbooksId,
      }))
    }
  },
)

watch(
  () => currentVersion,
  (val) => {
    if (val) {
      getChapterListApi()
    }
  },
)
</script>

<style lang="scss" scoped>
.arrow-btm {
  &::after {
    display: inline-block;
    content: '';
    border: 5px solid transparent;
    border-top-color: #1f2730;
    margin-left: 4px;
    transform: translateY(2px);
  }
}
.box-h {
  height: calc(100vh - 44px - 38px - 44px - 18px - v-bind('navHeight'));
}
.box-shadow {
  box-shadow: 0px 2 10px 0px rgba(0, 0, 0, 0.05);
}
</style>

<style lang="scss">
.sync-train-tab {
  --van-tabs-bottom-bar-width: 28px;
  --van-padding-sm: 19px;
}

.sync-train-collapse {
  .van-collapse-item {
    margin-bottom: 16px;
    border-radius: 12px;
    box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    & > .van-cell {
      padding: 0;
    }
    .van-collapse-item__content {
      padding: 0;
    }
  }
}
</style>
