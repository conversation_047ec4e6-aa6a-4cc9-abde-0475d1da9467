import config from '@/config/index'
import request from '@/utils/request/index'
const { baseURL, baseURL3, baseURL4 } = config

// 获取oss签名(需要验证该接口)
export function getOssSignature(data) {
  return request.put(baseURL3 + '/tutoring/oss/policy', data)
}

// 登录接口
export function login(data) {
  return request.post(baseURL3 + '/pad/login', data)
}

/*判断题目是否支持对话*/
export async function isSupportAi(data) {
  return request.get(baseURL + '/v3/student/aiChat/question/exists', data)
}

//纠错弹窗提交
export function submitError(data) {
  return request.post(baseURL + '/v3/questionCorrection/submit', data)
}

//纠错弹窗信息回填
export function getErrorInfo(data) {
  return request.get(
    baseURL + '/v3/questionCorrection/questionStageSubject',
    data,
  )
}

/* 个辅跳转 */
export function getJZTKey() {
  return request.post(baseURL + '/v3/student/jzt/login/encrypt')
}

// 智习室兑换金字塔的token
export function getJZTToken(data) {
  return request.post(baseURL4 + '/tutoring/login/zhixishi', data, {
    delay: false,
  })
}

/* 是否开通智习室 */
export async function openZixishi(data) {
  return request.get(baseURL + '/v3/student/home/<USER>/openZixishi', data)
}
