<template>
  <div class="activity">
    <div class="relative">
      <g-icon
        name="svg-activity-back"
        class="w-[8px] h-[14px] absolute left-[16px] top-[49px]"
        @click="goBack"
      ></g-icon>
      <img
        :src="
          isShowH
            ? $g.tool.getFileUrl('activity/haoHeader.png')
            : $g.tool.getFileUrl('activity/header.png')
        "
        class="w-full"
      />
    </div>
    <div
      :style="{ minHeight: 'calc(100vh - 443px)' }"
      class="flex justify-center"
    >
      <div class="bg-[#FFFFFF] mb-[16px] rounded-[14px] w-[355px]">
        <div
          class="rounded-t-[14px] flex items-center bg-[#E7F2FD] py-[11px] px-[11px]"
        >
          <span class="text-[#72767B] text-[16px] flex-shrink-0 mr-[10px]"
            >快速导航：</span
          >
          <div class="flex items-center gap-x-[24px] w-full overflow-x-auto">
            <div
              v-for="(province, index) in provinceList"
              :key="index"
              class="text-[16px] flex-shrink-0 text-[#454647] cursor-pointer"
              @click="onProvinceClick(province)"
            >
              {{ province?.sysAreaName }}
            </div>
          </div>
        </div>
        <g-loading v-if="loading" class="h-200px"></g-loading>
        <div v-else>
          <div v-if="list?.length" class="mt-[16px] px-[10px]">
            <div
              v-for="(listItem, listIndex) in list"
              :key="listIndex"
              class="mb-[24px]"
            >
              <div :id="listItem?.unionActivityExamId">
                <div class="mr-[5px] text-[#393939] text-[16px] font-600">
                  {{ listItem?.unionActivityExamName }}
                </div>
                <div class="text-[12px] text-[#7E7E7E]">
                  适用地区：{{
                    $g._.map(listItem?.unionAreaList, 'sysAreaName').join('、')
                  }}
                </div>
              </div>
              <div class="line my-[15px]"></div>
              <div class="flex flex-wrap gap-[12px]">
                <div
                  v-for="(
                    courseItem, courseIndex
                  ) in listItem?.unionSubjectList"
                  :key="courseIndex"
                  :class="
                    courseItem?.bookId ? 'cursor-pointer' : 'cursor-not-allowed'
                  "
                  class="border-[1px] text-center text-[16px] text-[#333333] border-[#D6D6D6] br-[4px] px-[12px] py-[8px]"
                  @click="toJzt(courseItem)"
                >
                  {{ courseItem?.sysSubjectName }}
                  <div
                    class="text-[14px] font-600"
                    :class="
                      courseItem?.bookId ? 'text-[#097FFF]' : 'text-[#ABABAB]'
                    "
                  >
                    真题-Ai讲解
                  </div>
                </div>
              </div>
            </div>
          </div>
          <g-empty v-else></g-empty>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import {
  getAreaSelectApi,
  getAreaListApi,
  getJztAreaSelectApi,
  getJztAreaListApi,
} from '@/api/activity'
import { getJZTKey } from '@/api/common'
let provinceList = $ref<any>([])
const route = useRoute()
function onProvinceClick(item) {
  document.getElementById(item?.unionActivityExamId)?.scrollIntoView({
    behavior: 'smooth',
  })
}
let isShowH = $computed(() => {
  return route.query?.flag == 'hxnHome' ? true : false
})
let list = $ref<any>([])
let loading = $ref<any>(false)
async function getAreaSelect() {
  const res =
    !isShowH && route.query?.view == '1'
      ? await getJztAreaSelectApi()
      : await getAreaSelectApi()
  provinceList = res || []
}
const goBack: any = $g._.throttle(
  () => {
    console.log('goBack')
    $g.flutter('back')
  },
  2000,
  {
    leading: true,
    trailing: false,
  },
)
async function getAreaList() {
  try {
    loading = true
    const res =
      !isShowH && route.query?.view == '1'
        ? await getJztAreaListApi()
        : await getAreaListApi()
    list = res || []
    loading = false
  } catch (err) {
    loading = false
  }
}
async function toJzt(item) {
  if (!item?.bookId) {
    return
  }
  let key = ''
  if (route.query?.view != '1') {
    key = await getJZTKey()
  }
  if (!$g.inApp) {
    if (!isShowH && route.query?.view == '1') {
      window.location.href = `${
        import.meta.env.VITE_JZT_APP_URL
      }/#/jzt/questionListJzt/main?bookId=${item?.bookId}&bookName=${item?.bookName}`
    } else {
      window.location.href = `${
        import.meta.env.VITE_JZT_APP_URL
      }/#/jzt/questionList/main?bookId=${item?.bookId}&bookName=${item?.bookName}&encryptedStr=${key}`
    }
  } else {
    if (!isShowH && route.query?.view == '1') {
      $g.flutter('launchInNewWebView', {
        url: `${
          import.meta.env.VITE_JZT_APP_URL
        }/#/jzt/questionListJzt/main?bookId=${item?.bookId}&bookName=${item?.bookName}`,
        autoPrefix: false,
        autoClose: false,
      })
    } else {
      $g.flutter('launchInNewWebView', {
        url: `${
          import.meta.env.VITE_JZT_APP_URL
        }/#/jzt/questionList/main?bookId=${item?.bookId}&bookName=${item?.bookName}&encryptedStr=${key}`,
        autoPrefix: false,
        autoClose: false,
      })
    }
  }
  // window.location.href = `${
  //   import.meta.env.VITE_JZT_APP_URL
  // }/#/jzt/questionList/main?bookId=${item?.bookId}&encryptedStr=${key}`
}
onMounted(async () => {
  await getAreaList()
  getAreaSelect()
})
</script>
<style scoped lang="scss">
:deep() {
  /* 修改水平滚动条宽度 */
  ::-webkit-scrollbar:horizontal {
    display: none;
  }
}
.activity {
  background: #1e3daf;
  height: 100%;
  width: 100%;
  overflow-y: auto;
}
.line {
  width: 100%; /* 设置宽度 */
  height: 1px; /* 设置高度 */
  background: repeating-linear-gradient(
    to right,
    /* 渐变方向 */ #fff,
    /* 起始颜色 */ #fff 5px,
    /* 起始颜色结束位置 */ #e0e0e0 5px,
    /* 第二段颜色开始位置 */ #e0e0e0 10px /* 第二段颜色结束位置 */
  );
  border-radius: 5px; /* 圆滑效果 */
}
</style>
