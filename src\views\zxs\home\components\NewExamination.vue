<template>
  <div class="mt-10px">
    <div class="text-16px plain mb-12px font-600">
      <span>启鸣新高考</span>
    </div>
    <div
      class="grid grid-cols-4 justify-center mt-14px br-[12px] bg-white py-13px gap-y-15px"
    >
      <div
        v-for="item in newExam"
        :key="item.id"
        class="van-haptics-feedback flex flex-col items-center"
        @click="emit('jumpJZTApp', item.redirect_url)"
      >
        <div class="w-[24px] h-[28px] mb-8px">
          <img :src="item.image" class="w-[24px] h-auto" alt="" />
        </div>
        <span class="text-12px font-500">{{ item.title }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from '@/stores/modules/user'

const userStore = useUserStore()
const emit = defineEmits(['jumpJZTApp'])

const GRADE_12_ORDER = {
  思维训练: 1,
  专题训练: 2,
  全量真题: 3,
  错题收藏: 4,
  必备知识闯关: 5,
  高考总复习: 6,
  名校模拟卷: 7,
  校本同步训练: 8,
}

const HIGH_SCHOOL_ORDER = {
  校本同步训练: 1,
  错题收藏: 2,
  名校模拟卷: 3,
  必备知识闯关: 4,
  思维训练: 5,
  专题训练: 6,
  全量真题: 7,
  高考总复习: 8,
}

let newExam: any = $ref([
  {
    title: '思维训练',
    redirect_url: `${import.meta.env.VITE_JZT_APP_URL}/#/jzt/thinkingExplanation/main`,
    image: $g.tool.getFileUrl('home/icon-swxl.png'),
  },
  {
    title: '专题训练',
    redirect_url: `${import.meta.env.VITE_JZT_APP_URL}/#/jzt/home/<USER>
    image: $g.tool.getFileUrl('home/icon-ztxl.png'),
  },
  {
    title: '全量真题',
    redirect_url: `${import.meta.env.VITE_JZT_APP_URL}/#/jzt/home/<USER>
    image: $g.tool.getFileUrl('home/icon-qlzt.png'),
  },
  {
    title: '错题收藏',
    redirect_url: `${import.meta.env.VITE_JZT_APP_URL}/#/jzt/errorCollection`,
    image: $g.tool.getFileUrl('home/icon-mistake.png'),
  },
  {
    title: '必备知识闯关',
    redirect_url: `${import.meta.env.VITE_JZT_APP_URL}/#/jzt/home/<USER>
    image: $g.tool.getFileUrl('home/icon-zscg.png'),
  },
  {
    title: '高考总复习',
    redirect_url: `${import.meta.env.VITE_JZT_APP_URL}/#/jzt/revision`,
    image: $g.tool.getFileUrl('home/icon-gkzfx.png'),
  },
  {
    title: '名校模拟卷',
    redirect_url: `${import.meta.env.VITE_JZT_APP_URL}/#/jzt/home/<USER>
    image: $g.tool.getFileUrl('home/icon-mxmnj.png'),
  },
  {
    title: '校本同步训练',
    redirect_url: `${import.meta.env.VITE_JZT_APP_URL}/#/jzt/home/<USER>
    image: $g.tool.getFileUrl('home/icon-tbxl.png'),
  },
])

// 初始化考试数据时设置id
const initExamList = (examList) => {
  let isGrade12 = userStore.userInfo?.studentDetail?.sysGradeId === 12
  const orderMap = isGrade12 ? GRADE_12_ORDER : HIGH_SCHOOL_ORDER
  return examList
    .filter((exam) => orderMap.hasOwnProperty(exam.title))
    .map((exam) => ({
      ...exam,
      id: orderMap[exam.title] || 999, // 使用999作为默认值，确保未知项目排在最后
    }))
    .sort((a, b) => a.id - b.id)
}

newExam = initExamList(newExam)
</script>

<style lang="scss" scoped></style>
