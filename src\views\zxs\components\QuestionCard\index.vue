<template>
  <div class="pb-30px">
    <template v-if="questionList.length">
      <!-- 答题卡标记 -->
      <div
        v-show="showAnswerCardTag"
        class="flex-cc text-12px text-[#999] mb-16px"
      >
        <div class="flex-cc">
          <span class="answer-card-tag correctQuestion"></span>
          <span>正确</span>
        </div>
        <div class="flex-cc ml-[24px]">
          <span class="answer-card-tag errorQuestion"></span>
          <span>错误</span>
        </div>
        <div class="flex-cc ml-[24px]">
          <span class="answer-card-tag canNotQuestion"></span>
          <span>我不会</span>
        </div>
      </div>

      <!-- 题号 -->
      <div class="flex flex-wrap -ml-9px -mr-9px">
        <span
          v-for="(question, index) in questionList"
          :key="index + '序号'"
          class="mx-9px mb-18px text-[14px] w-[38px] h-[38px] rounded-full leading-[38px] text-center"
          :class="setStyle(question)"
          @click="handleQuestionChange(question)"
        >
          {{ question.questionNumber }}
        </span>
      </div>

      <!-- 题目内容 -->
      <QuestionCardItem
        :questionItem="activeQuestionInfo"
        @sub-index-change="curSubQuestionIndex = $event"
      />

      <!-- 作答笔记 -->
      <template v-if="ifNotChoice">
        <div class="answer-title">作答笔记</div>

        <!-- 作答照片空显示 -->
        <div
          v-if="!activeQuestionInfo.noteList?.length"
          class="leading-[22px] text-[12px] text-center text-[#999] mb-12px border border-dashed border-[#979797] py-8px br-[8px]"
        >
          暂未上传作答照片
        </div>

        <!-- 作答照片显示 -->
        <div v-if="activeQuestionInfo.noteList?.length" class="note-list-wrap">
          <van-swipe loop lazy-render class="note-list-container">
            <van-swipe-item
              v-for="(item, index) in activeQuestionInfo.noteList"
              :key="index"
            >
              <g-img
                :current-index="index"
                :src="item?.noteUrl"
                :preview-resource="
                  $g._.map(activeQuestionInfo?.noteList, 'noteUrl')
                "
                :preview="true"
                :affix="false"
                fit="contain"
                width="100%"
                height="100%"
              />
            </van-swipe-item>
          </van-swipe>
        </div>
      </template>

      <!-- 正确答案 -->
      <div class="answer-title">正确答案：</div>
      <g-mathjax
        :text="curSubQuestion?.questionAnswer"
        class="pl-10px overflow-x-auto overflow-y-hidden text-[#666]"
      />

      <!-- 你的答案 -->
      <div class="answer-title">你的答案：</div>
      <div class="pl-10px text-[#666]">
        {{ curSubQuestion?.studentAnswer || '我不会' }}
      </div>

      <!-- 答案解析 -->
      <div class="answer-title">答案解析：</div>
      <g-mathjax
        :text="curSubQuestion?.questionParse"
        class="pl-10px overflow-x-auto overflow-y-hidden text-[#666]"
      />
    </template>
    <g-empty v-else></g-empty>
  </div>
</template>

<!-- 答题报告中的带题号和题目解析的组件 -->
<script setup lang="ts">
import type { PropType } from 'vue'
import QuestionCardItem from './QuestionCardItem.vue'

const props = defineProps({
  questionList: {
    type: Array as PropType<any[]>,
    default: () => [],
  },
  // 是否展示正确，错误，我不会的标记
  showAnswerCardTag: {
    type: Boolean,
    default: true,
  },
})

// 当前选中的题目的题号
let activeQuestionNumber = $ref(0)
// 当前选中题目的全部信息
let activeQuestionInfo = $ref<any>({})
// 当前选中的子题的索引
let curSubQuestionIndex = $ref(0)
// 当前选中的子题
const curSubQuestion = $computed(() => {
  return activeQuestionInfo?.subQuestionList[curSubQuestionIndex]
})
// 判断大题是否是主观题
const ifNotChoice = $computed(
  () =>
    !['单选题', '多选题', '判断题', '选择题'].includes(
      activeQuestionInfo.jkNewQuestionTypeName,
    ),
)

// 默认选中第一个题目
watch(
  () => props.questionList,
  (list) => list.length && handleQuestionChange(list[0]),
  { immediate: true },
)

// 样式格式化
function setStyle(item) {
  if (item.questionNumber == activeQuestionNumber) {
    if (item.isCorrect == 2) return 'bg-[#2DD367] text-white'
    if (item.isJump == 2) return 'bg-[#999] text-white'
    if (item.isCorrect == 1) return 'bg-[#FF4646] text-white'
  } else {
    if (item.isCorrect == 2) return 'correctQuestion'
    if (item.isCorrect == 1 && item.isJump != 2) return 'errorQuestion'
    if (item.isJump == 2) return 'canNotQuestion'
  }
}

// 点击题目选中
function handleQuestionChange(question) {
  activeQuestionNumber = question.questionNumber
  let chooseQuestion = $g._.cloneDeep(question)
  // 处理判断题和选择题
  chooseQuestion?.subQuestionList.forEach((item) => {
    if (item.jkNewQuestionTypeId == 3) {
      item.options = [
        {
          id: 1,
          name: '√',
          content: null,
        },
        {
          id: 2,
          name: '×',
          content: null,
        },
      ]
    } else if ([1, 2].includes(item.jkNewQuestionTypeId)) {
      item.options = Object.keys(item)
        .filter((key) => key.includes('option') && key !== 'optionNumbers')
        .map((realKey) => {
          return {
            name: realKey.replace('option', '').toUpperCase(),
            content: item[realKey],
          }
        })
        .filter((v) => v.content)
        .slice(0, item.optionNumbers)
    }
  })

  activeQuestionInfo = chooseQuestion
  nextTick($g.tool.renderMathjax)
}
</script>

<style lang="scss" scoped>
.answer-card-tag {
  @apply w-8px h-8px rounded-full  mr-[4px];
}
// 正确选项
.correctQuestion {
  @apply bg-[#2DD36717] border border-[#2dd367] text-[#2dd367];
}
// 错误选项
.errorQuestion {
  @apply bg-[#FF46460F] border border-[#ff4646] text-[#ff4646];
}
// 我不会选项
.canNotQuestion {
  @apply bg-[#fff] border border-[#999] text-[#999];
}

.answer-title {
  @apply text-15px leading-[22px] font-500 py-10px relative ml-12px;

  &::after {
    content: '';
    @apply inline-block w-4px h-14px bg-[#217dfb] br-[3px] absolute -left-10px top-1/2 -translate-y-1/2;
  }
}

// 笔记容器保持宽高比
.note-list-wrap {
  @apply border-[2px] border-[#f3f3f3] mb-12px br-[8px] pb-[50%] relative overflow-hidden;
  .note-list-container {
    @apply absolute left-0 right-0 top-0 bottom-0 overflow-hidden;
  }
}
</style>
