/**
 * 视频播放器控制栏显示逻辑 Hook
 * @param player - 播放器实例
 * @returns 控制栏相关方法和状态
 */
export function useVideoControls(player: any) {
  const isControlsVisible = ref(true)

  let touchTimer: any = null
  let once = false

  const progressHandler = {
    isProgressDragging: false,
    handleStart: () => {
      progressHandler.isProgressDragging = true
      if (touchTimer) {
        clearTimeout(touchTimer)
        touchTimer = null
      }
      debouncedShowControls()
    },
    handleDrag: () => {}, // 拖动时保持控制栏显示
    handleEnd: () => {
      progressHandler.isProgressDragging = false
      debouncedHideControls()
    },
  }

  function handleTouchStart(e: TouchEvent) {
    if (progressHandler.isProgressDragging) return
    e.preventDefault()
    debouncedShowControls()
  }

  function handleTouchEnd() {
    if (progressHandler.isProgressDragging) return
    if (!player?.value) return
    debouncedHideControls()
  }

  function showControls() {
    if (!once) {
      once = true
      isControlsVisible.value = false
      updateControlsVisibility(false)
      return
    }
    isControlsVisible.value = true
    updateControlsVisibility()

    if (touchTimer) {
      clearTimeout(touchTimer)
      touchTimer = null
    }
  }

  function hideControls() {
    if (progressHandler.isProgressDragging) return
    isControlsVisible.value = false
    updateControlsVisibility()
  }

  const debouncedShowControls = $g._.debounce(showControls, 300)
  const debouncedHideControls = $g._.debounce(hideControls, 3000)

  function updateControlsVisibility(flag?: boolean) {
    const controls = player.value?.root.querySelector('.xgplayer-controls')
    if (!controls) return

    if (flag || isControlsVisible.value) {
      controls.classList.add('show')
      controls.style.opacity = '1'
      controls.style.visibility = 'visible'
    } else {
      controls.classList.remove('show')
      controls.style.opacity = '0'
      controls.style.visibility = 'hidden'
    }
  }

  function setupEventListeners() {
    if (!player?.value?.root || !$g.tool.isIOS()) return

    // 播放器容器的触摸事件
    const container = player.value.root
    container.addEventListener('touchstart', handleTouchStart)
    container.addEventListener('touchend', handleTouchEnd)

    // 进度条的触摸事件
    const progressBar = container.querySelector('.xgplayer-progress')
    if (progressBar) {
      progressBar.addEventListener('touchstart', progressHandler.handleStart)
      progressBar.addEventListener('touchmove', progressHandler.handleDrag)
      progressBar.addEventListener('touchend', progressHandler.handleEnd)
    }
  }

  function removeEventListeners() {
    if (!player?.value?.root || !$g.tool.isIOS()) return

    // 移除播放器容器的触摸事件
    const container = player.value.root
    container.removeEventListener('touchstart', handleTouchStart)
    container.removeEventListener('touchend', handleTouchEnd)

    // 移除进度条的触摸事件
    const progressBar = container.querySelector('.xgplayer-progress')
    if (progressBar) {
      progressBar.removeEventListener('touchstart', progressHandler.handleStart)
      progressBar.removeEventListener('touchmove', progressHandler.handleDrag)
      progressBar.removeEventListener('touchend', progressHandler.handleEnd)
    }
  }

  // 只在 iOS 设备下监听
  if ($g.tool.isIOS()) {
    watch(
      () => player.value,
      (newPlayer) => {
        if (newPlayer) {
          setupEventListeners()
        }
      },
      { immediate: true },
    )

    onUnmounted(() => {
      removeEventListeners()
      if (touchTimer) {
        clearTimeout(touchTimer)
        touchTimer = null
      }
    })
  }
}
