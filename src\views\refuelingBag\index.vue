<template>
  <div class="w-full">
    <div class="relative">
      <img
        src="@/assets/img/refuelingBag/back.png"
        class="w-[30px] h-[30px] absolute left-[8px] top-[41px]"
        @click="goBack"
      />
      <img
        src="@/assets/img/refuelingBag/bg-top.png"
        class="object-cover w-[100%]"
      />
      <div class="w-full px-[16px] absolute bottom-0">
        <div class="bg-[#FFFFFF] w-full br-[4px] border-[2px] border-[#FFB351]">
          <van-swipe autoplay="1000" loop style="height: 30px" vertical>
            <van-swipe-item v-for="(item, index) in list" :key="index">
              <div class="w-full px-[6px] h-full flex items-center">
                <img
                  :src="$g.tool.getFileUrl('refuelingBag/notification.png')"
                  alt=""
                  class="w-[14px] h-[14px]"
                />
                <div class="w-full line-1 ml-[1px] text-[12px] text-[#000000]">
                  家长 <span class="text-theme-error">{{ item[1] }}</span
                  >,
                  <span class="text-theme-error">{{ item[2] }}</span>
                  扫码领取了 <span class="text-theme-error">助考加油包</span>,
                  {{ item[3] }}
                </div>
              </div></van-swipe-item
            >
          </van-swipe>
        </div>
      </div>
    </div>
    <img
      src="@/assets/img/refuelingBag/bg-bottom.png"
      class="object-cover w-[100%]"
    />
  </div>
</template>
<script lang="ts" setup>
import { getListApi } from '@/api/refuelingBag'
import { onMounted } from 'vue'
let list = $ref<any>([])
const router = useRouter()
async function getList() {
  try {
    const res = await getListApi({ count: 8 })
    list = res || []
  } catch (err) {
    console.log('err', err)
  }
}
onMounted(() => {
  getList()
})
const goBack: any = $g._.throttle(
  () => {
    console.log('goBack')
    $g.flutter('back')
  },
  2000,
  {
    leading: true,
    trailing: false,
  },
)
</script>
<style scoped lang="scss">
:deep() {
  .van-swipe__indicators--vertical {
    display: none;
  }
}
</style>
