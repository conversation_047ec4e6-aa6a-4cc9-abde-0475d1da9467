<template>
  <!-- todo: 0.1 解决滑动页面时，navbar会闪一下的问题 -->
  <van-sticky :key="updateKey" :offset-top="0.1">
    <div
      id="myBar"
      ref="commonNavBarRef"
      class="my-bar"
      :style="{ background: customBackGround, boxShadow: boxShadow }"
    >
      <div
        class="w-full"
        :style="{
          height: settingStore.navigationHeight + 'px',
        }"
      ></div>
      <van-nav-bar :left-arrow="leftArrow" @click-left="goBack()">
        <template v-if="customLeft" #left>
          <slot name="left"></slot>
        </template>
        <template #title>
          <span
            v-if="!formatTitle"
            class="inline-block text-16px max-w-200px truncate h-46px leading-[50px] font-600"
            :style="{
              color: titleColor,
            }"
            :class="[...titleClass]"
            >{{ customTitle || route.meta?.title }}</span
          >
          <slot v-else name="formatTitle"> </slot>
        </template>
        <template v-if="customRight" #right>
          <slot name="csr"> </slot>
        </template>
      </van-nav-bar>
      <slot></slot>
    </div>
  </van-sticky>
</template>

<script setup lang="ts">
import { useSettingStore } from '@/stores/modules/setting'

const props = defineProps({
  // 是否自定义右侧内容
  customRight: {
    type: Boolean,
    default: false,
  },
  // 返回时需要数据处理
  backHandle: {
    type: Boolean,
    default: false,
  },
  customBackGround: {
    type: String,
    default: '#fff',
  },
  //  是否需要左侧返回按钮
  leftArrow: {
    type: Boolean,
    default: true,
  },
  // 是否需要自定义左侧内容
  customLeft: {
    type: Boolean,
    default: false,
  },
  // 自定义title文字
  customTitle: {
    type: String as any,
    default: '',
  },
  // 自定义title格式
  formatTitle: {
    type: Boolean,
    default: false,
  },
  // 阴影
  boxShadow: {
    type: String as any,
    default: '',
  },
  //title颜色
  titleColor: {
    type: String as any,
    default: '#333333',
  },
  titleClass: {
    type: Array as any,
    default: () => [],
  },
  // 返回触发原生刷新红点
  homeIconRefresh: {
    type: String as any,
    default: '',
  },
})

const emit = defineEmits(['changeStudent', 'back'])
const settingStore = useSettingStore()
let updateKey = $ref(0)
const route = useRoute()
const router = useRouter()

let commonNavBarRef = ref()

const goBack = $g._.throttle(
  () => {
    if (props.backHandle) {
      emit('back')
      return
    }
    if (router.options.history.state.back) {
      router.back()
    } else {
      $g.flutter('back')
      if ($g.tool.isTrue(props.homeIconRefresh)) {
        $g.flutter(props.homeIconRefresh)
      }
    }
  },
  2000,
  {
    leading: true,
    trailing: false,
  },
)

function onResize() {
  settingStore.navBarTotalHeight = $g.tool.pxConversionAdaptedPx(46, false)
}
// 计算整个bar的高度（包含手机状态栏高度）
onMounted(async () => {
  window.addEventListener('resize', onResize)
  await nextTick()
})
onBeforeUnmount(() => {
  window.removeEventListener('resize', onResize)
})
function changeUpdateKey() {
  updateKey++
}
defineExpose({
  changeUpdateKey,
})
</script>

<style lang="scss" scoped>
.my-bar {
  :deep() {
    .van-nav-bar {
      background: transparent;
    }
    .van-nav-bar__content {
      height: 46px;
    }
  }
}
</style>
