<template>
  <div class="sync-train-bg bg-[#F9F9F9]">
    <g-navbar customBackGround="transparent" />
    <g-loading v-if="loading"></g-loading>

    <div
      v-if="!loading"
      class="px-16px overflow-y-auto no-bar pb-20px"
      :style="{
        height: `calc(100vh - ${useSettingStore().navBarTotalHeight}px - ${useSettingStore().navigationHeight}px)`,
      }"
    >
      <div
        class="container !mt-20px pt-15px flex flex-col items-center pb-24px"
      >
        <CircleProgress
          :percent="percent"
          :target-score="targetScore"
        ></CircleProgress>
        <div
          v-if="!isEdit"
          class="flex items-center w-198px justify-center br-[4px] h-36px mt-13px text-[#FE6B47] font-500"
          style="background: rgba(254, 107, 71, 0.1)"
        >
          <span class="h-20px lh-[20px]">目标成绩及选课组合</span>
          <img
            :src="$g.tool.getFileUrl('aiStudentPartner/editScore.png')"
            alt=""
            class="w-16px h-16px ml-16px"
            @click="isEdit = true"
          />
        </div>
      </div>
      <div class="container p-12px pt-10px">
        <div class="myTitle">
          <img
            v-if="isEdit"
            :src="$g.tool.getFileUrl('aiStudentPartner/star.png')"
            alt=""
            class="w-14px h-14px mb-5px ml-[-3px]"
          />
          <span class="relative z-10">目标成绩</span>
        </div>
        <div class="flex items-center mt-13px">
          <van-field
            v-model="formObj.score"
            class="myInput w-100px"
            :border="false"
            type="digit"
            :min="300"
            :max="750"
            :disabled="!isEdit"
          />
          <span class="text-[#999999]">分</span>
          <span v-if="isEdit" class="text-[#FF0000] text-12px ml-10px"
            >请输入300分及以上分数</span
          >
        </div>
      </div>
      <div class="container p-12px pt-10px">
        <div class="myTitle">
          <img
            v-if="isEdit"
            :src="$g.tool.getFileUrl('aiStudentPartner/star.png')"
            alt=""
            class="w-14px h-14px mb-5px ml-[-3px]"
          />
          <span class="relative z-10">选课组合</span>
        </div>
        <div>
          <div v-if="isEdit" class="text-[#666666] text-12px mb-10px mt-8px">
            选择3个科目的组合，不同的组合对应不同的报考专业推荐，请认真填写
          </div>
          <div class="flex flex-wrap gap-[12px] text-12px mt-12px">
            <div
              v-for="item in courseList"
              :key="item.sysSubjectId"
              class="h-30px w-97px flex-cc bg-[#F4F4F4] br-[15px]"
              :class="{
                activeItem: formObj.subjectIds.includes(item.sysSubjectId),
              }"
              @click="change(item)"
            >
              {{ item.sysSubjectName }}
            </div>
          </div>
        </div>
      </div>
      <div class="container p-12px pt-10px">
        <div class="myTitle">
          <img
            v-if="isEdit"
            :src="$g.tool.getFileUrl('aiStudentPartner/star.png')"
            alt=""
            class="w-14px h-14px mb-5px ml-[-3px]"
          />
          <span class="relative z-10">所在省份</span>
        </div>
        <div
          class="w-120px h-36px bg-[#F4F4F4] br-[4px] flex items-center justify-between px-12px mt-15px"
          @click="
            () => {
              if (!isEdit) return
              showPop = true
            }
          "
        >
          <span>{{ formObj.cityInfo.text }}</span>
          <img
            :src="$g.tool.getFileUrl('home/down.png')"
            alt=""
            class="w-12px h-12px"
          />
        </div>
      </div>

      <div v-if="isEdit" class="mt-24px flex-cc">
        <van-button
          class="w-260px h-40px br-[24px]"
          color="#FE6B47"
          :loading="confirmLoading"
          @click="submit"
          >确认修改</van-button
        >
      </div>
    </div>

    <van-popup
      v-model:show="showPop"
      :close-on-popstate="true"
      round
      teleport="#app"
      duration="0"
    >
      <div>
        <van-picker
          v-show="showPop"
          :columns="provinceList"
          class="w-250px"
          title="所在省份"
          @cancel="showPop = false"
          @confirm="handleSelect"
        />
      </div>
    </van-popup>
  </div>
</template>
<script setup lang="ts">
import {
  getGradeDetail,
  getProvinceList,
  getSubjectList,
  submitTargetGrade,
} from '@/api/aiStudentPartner'
import CircleProgress from './components/CircleProgress.vue'
import { useSettingStore } from '@/stores/modules/setting'
let percent = $ref(0)
let targetScore = $ref(0)
let formObj = $ref<any>({
  score: null,
  cityInfo: {},
  subjectIds: [],
})
let isEdit = $ref(false)
let courseList = $ref<any>([])
let showPop = $ref(false)
let provinceList = $ref<any>([])
let loading = $ref(false)
let confirmLoading = $ref(false)

const inputColor = $computed(() => {
  return isEdit ? '#333333' : '#999999'
})
function change(item) {
  if (!isEdit) return
  let index = formObj.subjectIds.indexOf(item.sysSubjectId)
  if (index > -1) {
    formObj.subjectIds.splice(index, 1)
  } else {
    if (formObj.subjectIds.length === 3) {
      $g.showToast('最多选择3门科目')
      return
    }
    formObj.subjectIds.push(item.sysSubjectId)
  }
}

function handleSelect(action) {
  formObj.cityInfo = action.selectedOptions[0]
  showPop = false
}

async function getCourse() {
  let res = await getSubjectList()
  courseList = res || []
}

async function getProvince() {
  let res = await getProvinceList()
  if (!res) {
    provinceList = []
    return
  }
  provinceList =
    res.cityList?.reduce((arr, next) => {
      next.areaList.forEach((v) => {
        arr.push({
          text: v.name,
          value: v.areaCode,
          areaName: v.areaName,
        })
      })
      return arr
    }, []) || []
  if (res.ipCity) {
    formObj.cityInfo = provinceList.find((v) => v.value == res.ipCity)
  }
}
async function init() {
  try {
    let res = await getGradeDetail()
    if (res) {
      formObj.score = res.score ? res.score : 750
      formObj.cityInfo = {
        text: res.shortCityName,
        value: res.cityCode,
        areaName: res.cityName,
      }
      formObj.subjectIds = res.subjectList?.map((v) => v.sys_subject_id) || []
      targetScore = formObj.score
      percent = $g.math(formObj.score).divide(750).toFixed(2).value()
    }
    loading = false
  } catch {
    loading = false
  }
}

onBeforeMount(async () => {
  init()
  getProvince()
  getCourse()
})

async function submit() {
  if (!formObj.score || formObj.score > 750 || formObj.score < 300) {
    $g.showToast('请输入300-750之间的整数')
    return
  }
  if (formObj.subjectIds?.length !== 3) {
    $g.showToast('请选择3个学科')
    return
  }
  if (!formObj.cityInfo.value) {
    $g.showToast('请选择地区')
    return
  }
  try {
    confirmLoading = true
    let { score, cityInfo, subjectIds } = formObj
    await submitTargetGrade({
      score,
      sys_subject_ids: subjectIds,
      city_code: cityInfo.value,
      city_name: cityInfo.areaName,
    })
    targetScore = formObj.score
    percent = $g.math(formObj.score).divide(750).toFixed(2).value()
    isEdit = false
    confirmLoading = false
  } catch (e) {
    confirmLoading = false
    console.log(e)
  }
}
</script>
<style scoped lang="scss">
.sync-train-bg {
  background: url(@/assets/img/home/<USER>/ 100% auto
    no-repeat;
}

.container {
  @apply bg-white br-[12px] mt-16px;
}

.myTitle {
  font-weight: 600;
  font-size: 14px;
  color: #333333;
  position: relative;
  display: flex;
  align-items: center;

  &::after {
    content: '';
    position: absolute;
    width: 64px;
    height: 5px;
    background: linear-gradient(
      0.25turn,
      #fe6b47,
      20%,
      rgba(255, 182, 164, 0.21)
    );
    border-radius: 3px;
    left: 0;
    bottom: 3px;
  }
}

.myInput {
  border: none;
  margin-right: 8px;
  border-radius: 4px;
  background: #f4f4f4;
  padding: 6px 12px;
  font-weight: 500;
}

.activeItem {
  background: #fe6b47 !important;
  color: #ffffff;
  font-weight: 500;
}
:deep() {
  .van-field__control {
    color: v-bind(inputColor) !important;
    -webkit-text-fill-color: v-bind(inputColor) !important;
  }
}
</style>
