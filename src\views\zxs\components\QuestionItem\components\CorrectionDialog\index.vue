<template>
  <div class="text-theme-primary text-12px">
    <span
      v-if="!questionItem[correctKey]"
      class="flex-cc"
      @click="handleCorrection"
    >
      <g-icon name="svg-common-pen" size="12" class="mr-4px" />
      <span>我要纠错</span>
    </span>

    <span v-else class="flex-cc text-[#999]">
      <g-icon name="svg-common-pushpin" size="12" class="mr-4px" />
      <span>已纠错</span>
    </span>
  </div>

  <van-popup
    v-model:show="showDialog"
    round
    safe-area-inset-bottom
    :lock-scroll="false"
    position="bottom"
    class="max-h-[90vh] min-h-[40vh] overflow-hidden flex flex-col px-16px"
    :close-on-click-overlay="!questionItem[correctKey]"
    @closed="handlePopupClosed"
  >
    <template v-if="!questionItem[correctKey]">
      <div class="flex-cc py-16px text-16px leading-[22px] font-500">
        试题纠错
      </div>

      <!-- 题目信息 -->
      <div
        class="text-center text-12px leading-[22px] text-[#141414] rounded-[2px] bg-[#f3f3f3] mb-16px"
      >
        试题ID：{{ questionInfo.paperQuestionId }} | 所属学科：{{
          questionInfo.jkPhaseName + questionInfo.jkSubjectName
        }}
      </div>

      <!-- 表单区域 -->
      <van-form
        ref="formRef"
        class="flex-1 overflow-x-hidden overflow-y-auto pb-20px"
        label-width="90"
        @submit="submit"
      >
        <van-field
          required
          :border="false"
          name="错误类型"
          label="错误类型"
          class="form-error-types"
          :rules="[
            {
              required: true,
              message: '错误类型不能为空',
              trigger: ['onChange', 'onSubmit'],
            },
          ]"
        >
          <template #input>
            <van-checkbox-group
              v-model="formOptions.data.errorTypes"
              direction="horizontal"
            >
              <van-checkbox
                v-for="item in formOptions.errorTypesOptions"
                :key="item.value"
                :name="item.value"
                icon-size="16px"
                >{{ item.label }}
              </van-checkbox>
            </van-checkbox-group>
          </template>
        </van-field>
        <van-field
          v-model.trim="formOptions.data.errorDesc"
          required
          type="textarea"
          name="错误描述"
          label="错误描述"
          placeholder="请在此处具体描述试题中的错误"
          show-word-limit
          maxlength="200"
          :border="false"
          class="form-error-desc"
          :autosize="{ maxHeight: 106, minHeight: 106 }"
          :rules="[
            {
              required: true,
              message: '错误描述不能为空',
              trigger: ['onBlur', 'onSubmit'],
            },
          ]"
        />
        <van-field name="uploader">
          <template #input>
            <div class="min-h-[140px]">
              <Upload
                v-if="showDialog"
                :form-options="formOptions"
                tip="友情提示：您可以把错误点在图片上圈出来噢~单张图片大小不超过3M，支持JPG和PNG格式的文件"
                :maxCount="6"
                :fileSize="3"
              ></Upload>
            </div>
          </template>
        </van-field>
      </van-form>

      <!-- 提交按钮 -->
      <van-button
        type="primary"
        class="w-full h-[40px] my-10px flex-shrink-0"
        round
        @click="submit"
      >
        提交
      </van-button>
    </template>

    <!-- 纠错完成以后的状态反馈 -->
    <template v-else>
      <img
        :src="$g.tool.getFileUrl('common/correctSuccess.png')"
        class="w-100px mx-auto mt-40px flex-shrink-0"
      />
      <div
        class="text-center text-18px leading-[22px] font-600 my-12px text-[#52C451]"
      >
        纠错成功
      </div>
      <div class="text-center text-[#666] mb-90px">
        系统已收到你的反馈，会及时对此题进行修正
      </div>

      <div class="text-center text-[#c3c3c3] pb-50px">
        {{ countDownTime }}秒后自动跳转下一题
      </div>
    </template>
  </van-popup>
</template>
<script setup lang="ts">
import { getErrorInfo, submitError } from '@/api/common'
import Upload from './Upload.vue'

const props = defineProps({
  //题目数据
  questionItem: {
    type: Object,
    required: true,
    default: () => ({}),
  },
  //使用纠错功能的页面来源
  correctionSource: {
    type: Number,
  },
  // 查询和保存题目信息所需的额外参数
  extraParams: {
    type: Object,
    default: () => ({}),
  },
})
const emit = defineEmits(['correctedAndNext'])

const formRef = $ref<any>(null)

let countDownTime = $ref(2)
// 弹窗是否显示
let showDialog = $ref(false)
// 试题信息
let questionInfo = $ref<any>({})
// 是否已纠错字段，兼容两种变量名称
const correctKey = $computed(() => {
  return (
    ['is_correction', 'isCorrection'].find((key) =>
      Reflect.has(props.questionItem, key),
    ) || 'is_correction'
  )
})
// 题目ID字段，兼容多个变量名称
const idKey = $computed(() => {
  return (
    ['paper_question_id', 'paperQuestionId', 'questionId'].find((key) =>
      Reflect.has(props.questionItem, key),
    ) || ''
  )
})

const formOptions = reactive<any>({
  errorTypesOptions: [
    { label: '题干', value: 1 },
    { label: '答案', value: 2 },
    { label: '解析', value: 3 },
    { label: '知识点/难度', value: 4 },
    { label: '其他', value: 5 },
  ],
  data: {
    errorTypes: [],
    errorDesc: '',
    list: [],
  },
})

// 获取试题信息
async function fetchQuestionInfo() {
  questionInfo = {}
  let flag = false

  try {
    let params = {
      questionId: props.questionItem[idKey],
      questionSource: Number(props.correctionSource),
      ...props.extraParams,
    }
    questionInfo = (await getErrorInfo(params)) || {}
    flag = true
  } catch (err: any) {
    if (err.data?.code === 514002) {
      $g.showToast('该题目已被纠错，正在处理中')
    }
  }

  return flag
}

// 提交错误信息
async function submit() {
  try {
    await formRef?.validate()
    let params = {
      questionId: props.questionItem[idKey],
      source: Number(props.correctionSource),
      ...props.extraParams,
      ...formOptions.data,
      list: formOptions.data.list.map((h: any) => ({
        suffix: $g.tool.getExt(h.name),
        imgTitle: h.name,
        imgUrl: h.resource_url,
      })),
    }
    await submitError(params)
    props.questionItem[correctKey] = true
    countDown()
  } catch (err) {
    console.log('提交错误信息出错', err)
  }
}

// 点击我要纠错按钮
async function handleCorrection() {
  countDownTime = 2
  formRef?.resetValidation()
  let res = await fetchQuestionInfo()
  res && (showDialog = true)
}

// 弹窗关闭以后，重置数据
async function handlePopupClosed() {
  formOptions.data.errorDesc = null
  formOptions.data.list = []
  formOptions.data.errorTypes = []
}

// 纠错完毕跳转下一题
function toNextQuestion() {
  emit('correctedAndNext', props.questionItem)
  showDialog = false
}

// 倒计时
function countDown() {
  setTimeout(() => {
    countDownTime--
    if (countDownTime > 0) {
      countDown()
    } else {
      toNextQuestion()
    }
  }, 1000)
}
</script>

<style scoped lang="scss">
:deep() {
  .van-cell {
    padding: 10px 0;
    flex-direction: column;
    &.form-error-types {
      padding-bottom: 0px;
    }
    .van-field__label {
      margin-bottom: 8px;
    }

    // 选项间距
    .van-checkbox-group--horizontal > div {
      margin-right: 10px;
      margin-bottom: 10px;
    }

    .van-checkbox__label {
      color: #666;
    }
    .van-checkbox[aria-checked='true'] .van-checkbox__label {
      color: #217dfbff;
    }
  }
}

.form-error-desc {
  :deep() {
    .van-field__body {
      border: 1px solid #dfdfdf;
      padding: 10px;
      padding-bottom: 25px;
      border-radius: 8px;
      margin-bottom: -25px;
    }
    .van-field__word-limit {
      margin-right: 5px;
    }
  }
}
</style>
