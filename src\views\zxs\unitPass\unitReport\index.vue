<template>
  <div ref="reportContainer" class="report-bg h-[100vh] overflow-auto no-bar">
    <g-navbar
      :customTitle="title + '报告'"
      customBackGround="transparent"
      :class="['report-navbar', scrollDistance > 10 && 'report-navbar-white']"
    />
    <g-loading v-if="loading" class="h-[200px] mt-[calc((100vh-300px)/2)]" />

    <g-empty
      v-if="!loading && !reportInfo"
      class="mt-[calc((100vh-300px)/2)]"
    />

    <template v-if="!loading && reportInfo">
      <!-- 结果汇总 -->
      <div
        class="mx-16px mt-10px mb-16px h-[224px] px-[22px] pt-24px bg-white br-[8px] relative"
        style="box-shadow: 0px 2px 8px 0px rgba(107, 133, 167, 0.08)"
      >
        <img
          :src="$g.tool.getFileUrl('unitPass/reportBg2.png')"
          class="w-50px absolute -right-8px -top-13px"
        />

        <div class="flex">
          <!-- 诊断结果 -->
          <div class="flex-1 flex flex-col items-start justify-center pl-10px">
            <div class="tag-title">{{ title }}结果</div>
            <div
              class="tag-result"
              :style="{ '--result-color': resultLevel?.color }"
            >
              {{ resultLevel?.title }}
            </div>
          </div>

          <!-- 正确率 -->
          <div class="flex-shrink-0 relative">
            <img class="w-[156px] h-[78px]" :src="resultLevel?.defaultPic" />
            <div class="absolute top-0 left-0">
              <ReportProgress
                :percent="Number(reportInfo.accuracy || 0)"
                :start-color="resultLevel.startColor"
                :stop-color="resultLevel.stopColor"
              />
            </div>
            <div
              class="absolute bottom-0 left-1/2 -translate-x-1/2 flex flex-col items-center"
            >
              <span class="text-16px text-[#262628] leading-[19px] font-600">
                {{ reportInfo.accuracy }}%
              </span>
              <span class="text-12px text-[#999] leading-[17px]">
                答题正确率
              </span>
            </div>
          </div>
        </div>

        <div class="my-14px text-12px leading-[17px] text-[#999]">
          * 答题结果分为优秀、良好、合格、待合格
        </div>

        <div class="border-b border-[#A0BCE326] mb-[17px]"></div>

        <div class="flex flex-wrap whitespace-nowrap text-[#666666]">
          <div class="w-3/5 flex items-center">
            <g-icon
              name="svg-unitPass-user"
              size="12"
              class="leading-[0] mr-6px"
            />
            <span class="mr-6px">答题人：</span>
            <span>{{ reportInfo?.studentName }}</span>
          </div>

          <div
            v-if="reportInfo?.testTime"
            class="w-2/5 flex items-center justify-end"
          >
            <g-icon
              name="svg-unitPass-hourglass"
              size="12"
              class="leading-[0] mr-6px"
            />
            <span class="mr-6px">总耗时：</span>
            <span>{{ formatTime }}</span>
          </div>

          <div class="w-full flex items-center mt-8px">
            <g-icon
              name="svg-unitPass-time"
              size="12"
              class="leading-[0] mr-6px"
            />
            <span class="mr-6px">答题时间：</span>
            <span>{{ reportInfo?.createTime }}</span>
          </div>
        </div>
      </div>

      <!-- 答题卡 -->
      <div class="mx-16px mb-18px p-12px bg-white br-[8px]">
        <div class="flex items-center justify-between mb-16px">
          <div class="flex-cc">
            <g-icon
              name="svg-unitPass-answerCard"
              size="14"
              class="leading-[0] mr-8px"
            />
            <span class="text-16px font-600 leading-[22px]">答题卡</span>
          </div>

          <!-- 维度切换按钮 -->
          <div
            v-show="showKnowledgePointBtn"
            class="flex-cc leading-[20px] text-theme-primary"
            @click="
              curDimension =
                curDimension === 'testQuestions'
                  ? 'knowledgePoint'
                  : 'testQuestions'
            "
          >
            <span>
              {{ curDimension === 'testQuestions' ? '知识点维度' : '试题维度' }}
            </span>
            <g-icon
              name="svg-unitPass-checkout"
              size="12"
              class="leading-[0] ml-4px"
            />
          </div>
        </div>

        <!-- 答题结果：试题维度 -->
        <template v-if="curDimension === 'testQuestions'">
          <QuestionCard :question-list="reportInfo?.questionList" />
        </template>

        <!-- 答题结果：知识点维度 -->
        <template v-if="curDimension === 'knowledgePoint'">
          <!-- 薄弱知识点 -->
          <KnowledgePoints
            v-show="weaknessKnowledgeQuestionList.length"
            :type="0"
            :table-data="
              reportInfo?.weaknessKnowledgeReport?.knowledgeList || []
            "
          />
          <!-- 良好知识点 -->
          <KnowledgePoints
            v-show="goodKnowledgeQuestionList.length"
            :type="1"
            :table-data="reportInfo?.goodKnowledgeReport?.knowledgeList || []"
          />
          <!-- 优秀知识点 -->
          <KnowledgePoints
            v-show="excellentKnowledgeQuestionList.length"
            :type="2"
            :table-data="
              reportInfo?.excellentKnowledgeReport?.knowledgeList || []
            "
          />
        </template>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import ReportProgress from '@/views/zxs/components/ReportProgress.vue'
import QuestionCard from '@/views/zxs/components/QuestionCard/index.vue'
import KnowledgePoints from '@/views/zxs/components/KnowledgePoints/index.vue'
import { getReportDetail } from '@/api/unitPass'

const reportContainer = ref(null)

const { y: scrollDistance } = useScroll(reportContainer)

const route = useRoute()
// 请求报告信息loading
let loading = $ref(true)
// 报告信息
let reportInfo: any = $ref(null)
// 当前选择的维度,默认试题维度
let curDimension = $ref('testQuestions')

const weaknessKnowledgeQuestionList = $computed(
  () => reportInfo?.weaknessKnowledgeReport?.questionList || [],
)
const goodKnowledgeQuestionList = $computed(
  () => reportInfo?.goodKnowledgeReport?.questionList || [],
)
const excellentKnowledgeQuestionList = $computed(
  () => reportInfo?.excellentKnowledgeReport?.questionList || [],
)
// 是否展示维度切换按钮
const showKnowledgePointBtn = $computed(
  () =>
    weaknessKnowledgeQuestionList.length ||
    goodKnowledgeQuestionList.length ||
    excellentKnowledgeQuestionList.length,
)

// 格式化做题耗时显示
const formatTime = $computed(() => {
  if (!reportInfo?.testTime) return '--'
  let m = Math.floor(reportInfo.testTime / 60)
  let s = reportInfo.testTime % 60
  return `${m >= 10 ? m : '0' + m}'${s >= 10 ? s : '0' + s}''`
})

// 诊断等级
const resultMap = {
  WAIT_PASS: {
    title: '待合格',
    defaultPic: $g.tool.getFileUrl('unitPass/reportWaitPass.png'),
    color: '#F57592FF',
    startColor: '#FB758E',
    stopColor: '#FFA7A7',
  },
  PASS: {
    title: '合格',
    defaultPic: $g.tool.getFileUrl('unitPass/reportPass.png'),
    color: '#7483FEFF',
    startColor: '#7383FF',
    stopColor: '#C2ACFA',
  },
  GOOD: {
    title: '良好',
    defaultPic: $g.tool.getFileUrl('unitPass/reportGood.png'),
    color: '#0073FF42',
    startColor: '#2D7CFF',
    stopColor: '#6FBFFF',
  },
  EXCELLENT: {
    title: '优秀',
    defaultPic: $g.tool.getFileUrl('unitPass/reportExcellent.png'),
    color: '#6DDA998A',
    startColor: '#6FDB96',
    stopColor: '#88EF84',
  },
}
// 本次测验结果等级
const resultLevel = $computed(() => resultMap[reportInfo?.gradeStatus] || {})

// 格式化标题文字显示
const title = $computed(() => {
  let type: any = route.query.type
  return type == 1 ? '诊断' : type == 2 ? '练习' : type == 3 ? '测评' : ''
})

// 获取报告详情
async function fetchReportDetail() {
  try {
    loading = true
    curDimension = 'testQuestions'

    reportInfo =
      (await getReportDetail({
        type: route.query.type,
        sysTextbooksCatalogId: route.query.sysTextbooksCatalogId ?? '',
      })) || {}
  } catch (err) {
    console.log('获取报告详情出错', err)
  } finally {
    loading = false
  }
}

onBeforeMount(fetchReportDetail)
</script>

<style lang="scss" scoped>
.report-navbar {
  :deep() {
    #myBar {
      transition: background-color 0.1s linear;
    }
  }
  &.report-navbar-white {
    :deep() {
      #myBar {
        background-color: white !important;
      }
    }
  }
}

.report-bg {
  background: url(@/assets/img/unitPass/reportBg.png) top center / cover
    no-repeat;
}

.tag-title {
  @apply text-white text-[12px] leading-[18px] px-6px bg-theme-primary rounded-[4px] rounded-br-none mb-8px;
}

.tag-result {
  @apply text-[24px] leading-[34px] font-600 relative z-[1];

  &::after {
    @apply inline-block w-full h-[10px] absolute bottom-[5px] left-0 z-[-1];
    content: '';
    background: linear-gradient(
      90deg,
      #fff 0%,
      var(--result-color) 55%,
      #fff 100%
    );
  }
}
</style>
