{"extends": "@tsconfig/node20/tsconfig.json", "include": ["vite.config.*", "vitest.config.*", "cypress.config.*", "nightwatch.conf.*", "playwright.config.*", "vite/**/*.ts", "types/**/*", "src/**/*"], "compilerOptions": {"composite": true, "noEmit": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.node.tsbuildinfo", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "types": ["node"], "noImplicitAny": false, "lib": ["ESNext", "DOM"], "paths": {"@/*": ["./src/*"], "/#/*": ["./types/*"]}}}