<template>
  <div>
    <div class="flex flex-wrap pb-14px">
      <div
        v-for="(imgItem, index) in formOptions.data.list"
        :key="imgItem.resource_url"
        class="mr-8px mb-4px relative border border-dashed border-[#dfdfdf] br-[8px]"
        style="width: 85px; height: 85px"
      >
        <div class="shape" @click.stop="deletItem(index)">
          <g-icon name="svg-common-close" size="10" color="#fff" />
        </div>
        <g-img
          :src="imgItem.resource_url"
          preview
          fit="contain"
          :affix="false"
          width="85"
          height="85"
          :preview-resource="$g._.map(formOptions.data.list, 'resource_url')"
          :current-index="index"
        ></g-img>
      </div>
      <div
        v-if="formOptions.data.list.length < (maxCount || 1)"
        class="flex-cc w-[80px] h-[80px] border border-dashed border-[#dfdfdf] br-[8px]"
        @click="takePhoto"
      >
        <g-icon name="svg-common-plus" size="46" />
      </div>
    </div>
    <!-- 提示信息 -->
    <div v-if="tip" class="text-[#999999] text-12px">
      <g-icon name="svg-common-tip" size="14" class="inline-block mr-4px" />
      {{ tip }}
    </div>
  </div>
</template>
<script setup lang="ts">
const props = defineProps({
  formOptions: {
    type: Object,
    required: true,
  },
  maxCount: {
    type: Number,
    default: 1,
  },
  //单位m
  fileSize: {
    type: Number,
    default: 3,
  },
  tip: {
    type: String,
    default: '',
  },
})

onBeforeMount(() => {
  window.onFileChoose = onFileChoose
})

function onFileChoose(params) {
  let data = ($g.tool.isTrue(params) && JSON.parse(params)) || []
  // 过滤掉不合格图片，断网时选择图片后，数据仍然会发送过来
  let chooseFiles = data
  if (!chooseFiles.length || !chooseFiles[0].host) return
  // 当前已经上传的图片数量
  let count = props.formOptions.data.list.length
  // 还能上传多少张图片
  let leftCount = props.maxCount - count

  if (leftCount <= 0) return

  chooseFiles = chooseFiles.splice(0, leftCount)

  chooseFiles.forEach((file) => {
    props.formOptions.data.list.push({
      ...file,
      name: file.filePath,
      resource_url: file.host + '/' + file.filePath,
    })
  })
}

function takePhoto() {
  $g.flutter('chooseMedia', {
    singlePhotoSize: props.fileSize, //单张照片最大size，单位M
    takeVideo: false, //是否可以拍视频
    takePicture: true, //是否可以拍照片
    pickVideo: false, //是否可以选视频
    pickPicture: true, //是否可以选照片
    picDoc: false, //是否可以选文件
    enableScanDocument: false, //是否可以使用扫一扫
    applicationParam: 'license',
    enableCrop: false,
    maxPhotoLength: props.maxCount,
  })
}

function deletItem(idx) {
  props.formOptions.data.list.splice(idx, 1)
}
</script>
<style scoped lang="scss">
.shape {
  position: absolute;
  right: -4px;
  top: -4px;
  width: 16px;
  height: 16px;
  background: black;
  border-radius: 50%;
  z-index: 100;
  line-height: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
