import tool from './tool'
import router from '@/router'

// 文档地址： https://szr8eglbrn.feishu.cn/docx/ZSiMdVWysomxSCxg82Sc5ubhnwc

const FnFormat = {
  // ==============Get Start============== //

  /**
   * @description version - 获取app版本
   */
  version: { method: 'Get', code: 'version' },

  /**
   * @description token - 获取token
   */
  token: { method: 'Get', code: 'token' },

  /**
   * @description jztToken - 获取金字塔Token
   */
  jztToken: { method: 'Get', code: 'jztToken' },

  /**
   * @description userInfo - 获取登录的用户信息
   */
  userInfo: { method: 'Get', code: 'userInfo' },

  /**
   * @description platform - 获取当前使用平台
   * @returns {string}   "CTXX": 成天学习  "DRJS"：达人教师   "QMYX"：启鸣云校
   */
  platform: { method: 'Get', code: 'platform' },

  /**
   * @description viewInset - 系统UI遮挡（如键盘高度）
   * @returns {array} [10,20,30,40]，方向分别为左上右下， 但第4个是键盘高度
   */
  viewInset: { method: 'Get', code: 'viewInset' },

  /**
   * @description padding - 获取屏幕边缘间距
   * @returns {array} [10,20,30,40]，方向分别为左上右下
   */
  padding: { method: 'Get', code: 'padding' },

  /**
   * @description role - 获取当前角色
   * @returns {string}   "student": 学生  "parents"：家长
   */
  role: { method: 'Get', code: 'role' },

  // ==============Get End============== //

  // ==============Set Start============== //

  /**
   * @description fullPage - 告诉原生隐藏手机状态栏
   * @param {boolean}
   */
  fullPage: { method: 'Set', code: 'fullPage' },

  /**
   * @description statusBarDark - 修改状态栏字体颜色
   * @param {object}  {statusBarDark:boolean}
   * true 黑色字体
   * false 白色字体
   */
  statusBarDark: { method: 'Set', code: 'statusBarDark' },

  /**
   * @description WebView调用 fullWithStatusBar方法修改界面起始位置
   * @param {boolean}  | true | false
   * True 界面从最顶端开始，
   * False 界面从状态栏下方开始
   */
  fullWithStatusBar: { method: 'Set', code: 'fullWithStatusBar' },

  /** keyboardResize
   * @description 键盘弹起后，是否重新计算布局高度，默认true 就是键盘顶起会重置界面，false 就是不会
   * @param {boolean}，必传
   */
  keyboardResize: { method: 'Set', code: 'keyboardResize' },

  /**
   * @description 控制显示隐藏底部tabBar
   * @param {boolean}
   */
  showBottomBar: { method: 'Set', code: 'showBottomBar' },

  /**
   * @description loadError-网页加载失败
   */
  loadError: { method: 'Set', code: 'loadError' },

  /**
   * @description clearCache - 清除缓存
   * @param {object} {reload:true} true: 需要重载，false：不需要重载
   */
  clearCache: { method: 'Set', code: 'clearCache' },

  /**
   * @description enableWebPageBack - 是否允许系统返回，默认true
   */
  enableWebPageBack: { method: 'Set', code: 'enableWebPageBack' },

  // ==============Set End============== //

  // ==============Operation Start============== //

  /**
   * @description login - 调用后会清除本地登录信息，并进入登录界面
   */
  login: { method: 'Operation', code: 'login' },

  /** back
   * @description {name: "back",data:{force:true }}
   * @param {object}  force默认为false,优先webview返回。
   * 若页面无法返回则关闭Webview界面，force为true则强制退出此webview
   */
  back: { method: 'Operation', code: 'private' },

  /**
   * @description reload - 重载webview
   */
  reload: { method: 'Operation', code: 'reload' },

  /**
   * @description launchInBrowser - 手机浏览器打开网页
   */
  launchInBrowser: { method: 'Operation', code: 'launchInBrowser' },

  /** 
   * @description launchInNewWebView - 新WebView打开网页
   * @param {object} 
   * {
        url:"", // 链接地址
        autoPrefix:true, // 如果没有带baseUrl，是否自动拼接
        hasToolbar:false, // 是否有标题栏
        toolbarText:"" // 标题栏文字
        autoClose: false, // 是否自动关闭本Webview（适用于中间页打开微信公众号界面）
        action: { // 右上角操作功能
          actionLabel: "右上角label",
          actionUrl: "前端路由相对路径"
        }
    }
   */
  launchInNewWebView: { method: 'Operation', code: 'launchInNewWebView' },

  /** 
   * @description launchInNewWebView2 - 新WebView打开网页--可以用于跳转横屏
   * @param {object} 
    * {
    "name": "launchInNewWebView2",
    "data": {
        "url": "http://www.qimingdaren.com",
        默认为false,若为tue，会调用h5挂载在window的refreshPage();
        "refreshCallJs": false,
        即将打开的新webview的安全区域设置
        inSafeArea: {
          top: false,
          left: true,
          bottom: false,
          right: false,
        }, 
        返回回到当前界面时的电池栏状态与屏幕方向，不传或null不做任何改变。
        "beforeEnter": { 
          "orientation": {
              "portraitUp": false,
              "portraitDown": false,
              "landscapeLeft": false,
              "landscapeRight": false
          },
          "fullPage": false
        },
        进入新界面时的电池栏状态与屏幕方向，不传或null不做任何改变
        "afterEnter": {
          "orientation": {
              "portraitUp": false,
              "portraitDown": false,
              "landscapeLeft": false,
              "landscapeRight": false
          },
          "fullPage": false
          }
        }
      }
   */
  launchInNewWebView2: { method: 'Operation', code: 'launchInNewWebView2' },

  /** 
   * @description protocol - 打开协议界面
   * @param {object} {type: 1,title: ''}
   * @type  1-注册协议,
            2-服务条款(家长端),
            3-天立阅卷增值服务协议,
            4-注销协议,
            5-注销说明,
            6-隐私协议(家长端),
            7-启鸣课后服务协议,
            8-启鸣课后隐私协议,
            9-第三方SDK目录,
            10-启鸣课后儿童隐私协议,
            11-关于天立,
            12-服务条款(教师端),
            13-隐私协议(教师端)
   */
  protocol: { method: 'Operation', code: 'protocol' },

  /**
   * @description nativeRoute - 跳转原生路由
   * @param {object}
   * { name: '', params: {}, backCallJsRefresh: boolean-返回后要不要刷新 }
   */
  nativeRoute: { method: 'Operation', code: 'nativeRoute' },

  /**
   * @description previewImage - 图片预览
   * @param {object} { urls: ['url','url'],index: number ,canShare: false ,isShowDown: false}
   * @param {string[]} data.urls 图片url数组
   * @param {string|number} data.index 当前图片索引
   * @param {boolean} data.canShare 是否需要分享
   * @param {boolean} data.isShowDown 是否需要下载
   */
  previewImage: {
    method: 'Operation',
    code: 'previewImage',
    format: (data) => {
      data.data = data.data.map((item) => {
        if (tool.typeOf(item) == 'object') {
          return item
        } else if (tool.typeOf(item) == 'string') {
          return {
            title: '',
            resource_url: item,
          }
        }
      })
      return data
    },
  },

  /**
   * @description playVideo - 播放视频
   * @param {object} { title: '',resource_url: '',}
   */
  playVideo: { method: 'Operation', code: 'playVideo' },

  /**
   * previewMedia - 同时预览图片、视频、文件
   * @param {object}
   * {
        "index":0,
        "data":[
            {
                "type":1, // 1-图片，2-视频，3-音频，9-其他（文件）
                "coverUrl":"" // 封面图,
                "contentUrl": "", // 预览的资源地址
                "showDownload": false, // 是否显示下载btn
                "supportSoftCode": false, // 视频才有效，默认false为flutter自带播放器，true是软解码ijk播放器
                "title":"测试视频",
                "hintTextString": "预览时的描述性文字，默认在预览下方居中"
            },
        ]
   * }
   */
  previewMedia: { method: 'Operation', code: 'previewMedia' },

  /**
   *  @description bindChild - 唤起原生绑定学生弹框
   */
  bindChild: { method: 'Operation', code: 'bindChild' },

  /**
   *  @description share - 分享
   *  @param {object}
   * {
        type: 'webPage', // webPage-网页 image-图片
        thumbnail: '', // 微信分享出去的小图
        linkUrl: '', // 路由地址
        title: '', // 标题
        description: '', // 描述

        // 需要分享的渠道
        wxChat: true, //微信
        wxMoment: true, //朋友圈
        wxFavorite: false, //收藏
   * }
   */
  share: { method: 'Operation', code: 'share' },

  /**
   * @description download - 媒体下载: 支持格式 jpg、png、gif、heic、heif、mp4、mov
   * @param {object} { url: '' }
   */
  download: { method: 'Operation', code: 'download' },

  /**
   * @description chooseMedia - 媒体选择，需前端提供onFileChoose方法
   * @param {object}
   * {
   *   "maxPhotoLength":9,  //最多几张照片
   *   "maxVideoLength":1, //最多几个视频
   *   "singlePhotoSize":20, //单张照片最大size，单位M
   *   "singleVideoSize":100, //单个视频最大size，单位M
   *   "takeVideo":true, //是否可以拍视频
   *   "takePicture":true,//是否可以拍照片
   *   "pickVideo":true, //是否可以选视频
   *   "pickPicture":true, //是否可以选照片
   *   "picDoc":false,//是否可以选文件
   *   "enableScanDocument":false, //是否可以使用扫一扫
   *   "applicationParam":"",//上传图片到后台指定文件模块，可为空
   *   "applicationType":"",//上传图片到后台指定文件模块,之后的type
   * }
   * @return {array}, 在onFileChoose(params)方法接收
   * [{filePath: string-资源路径, video: boolean-是否是视频,thumbPath: string-视频封面 }]
   */
  chooseMedia: { method: 'Operation', code: 'chooseMedia' },

  /**
   * @description cropPic - 裁剪
   * @param {object}
   * {
   *  path: '', // 可访问文件路径
   *  aspectRatio: '1/1', // 宽高比
   * }
   * @return {string} 裁剪后的文件路径
   */
  cropPic: { method: 'Operation', code: 'cropPic' },

  /**
   * 音频录制（转文字）- 异步返回，页面使用onAudioRecord接收return
   * @params {object}
   * {
  *      maxAudioDuration:60,  //最大录音时长（秒），默认值60
         translate:true, //要不要转文字，如果不要，返回体没有translateStr字段，默认值true
   * }
      @return
      {
        url: '', // oss音频地址,
        duration: 10, // 音频时长,
        translateStr: '', // 转文字结果
      }
   */
  audioTranslate: { method: 'Operation', code: 'audioTranslate' },

  /**
   * @description 文件下载
   */
  downloadFile: { method: 'Operation', code: 'downloadFile' },

  /**
   * @closeWebView 原生关闭webView
   */
  closeWebView: { method: 'Operation', code: 'closeWebView' },
  /**
   * @closeWebView 切换身份(启鸣云校)
   */

  // ==============Operation End============== //

  // ==============Notify Start============== //
  /**
   * @description
   * homeIconRefresh 金刚区刷新（老版）
   * homeFamilyIconRefresh 家庭
   * homeClsIconRefresh 班级
   * homeSchoolIconRefresh 学校
   * homeMineIconRefresh 我的
   */
  homeIconRefresh: { method: 'Notify', code: 'homeIconRefresh' },
  homeFamilyIconRefresh: { method: 'Notify', code: 'homeFamilyIconRefresh' },
  homeClsIconRefresh: { method: 'Notify', code: 'homeClsIconRefresh' },
  homeSchoolIconRefresh: { method: 'Notify', code: 'homeSchoolIconRefresh' },
  homeMineIconRefresh: { method: 'Notify', code: 'homeMineIconRefresh' },

  /**
   * 随手拍详情删除后通知原生刷新首页
   */
  casualPatRefresh: { method: 'Notify', code: 'casualPatRefresh' },
  /**
   * 指尖重新识别(智习室APP)
   */
  TO_FINGER_CAMERA: { method: 'Notify', code: 'TO_FINGER_CAMERA' },
  /**
   * 拍照重新识别(智习室APP)
   */
  TO_PHOTOGRAPH_CAMERA: { method: 'Notify', code: 'TO_PHOTOGRAPH_CAMERA' },
  /**
   * 拍照重新批改(智习室APP)
   */
  TO_CORRECT_CAMERA: { method: 'Notify', code: 'TO_CORRECT_CAMERA' },

  // ==============Notify End============== //

  // ==============前端自定义方法============== //
  /**
   * @description setStatusBarColor - 设置状态栏颜色  在路由离开后都会重置颜色
   */
  setStatusBarColor: { code: 'private' },
}

/**
 * 描述 flutter暴露方法
 * @date 2022-11-16
 * @param {any} fnName:string
 * @param {any} data:any
 * @returns {any}
 */

export default async function flutter(
  fnName: string,
  data?: any,
): Promise<any> {
  function hasCallHandler() {
    return new Promise((resolve, reject) => {
      window?.flutter_inappwebview?.callHandler ? resolve(true) : reject(false)
    })
  }
  function hasFlutter() {
    return new Promise((resolve, reject) => {
      window?.flutter_inappwebview ? resolve(true) : reject(false)
    })
  }
  try {
    // 私有方法
    if (FnFormat[fnName].code == 'private') {
      customMethod(fnName, data)
      return
    }

    let params
    let name
    if (typeof FnFormat[fnName].code === 'number') {
      name = 'jsCallNative'
      params = JSON.stringify({
        code: FnFormat[fnName].code,
        data: FnFormat[fnName].format ? FnFormat[fnName].format(data) : data,
      })
    } else {
      name = fnName
      params = data
    }

    await retry(hasFlutter, 5, 100)
    if (window.flutter_inappwebview) {
      if (window?.flutter_inappwebview?.callHandler) {
        // return window?.flutter_inappwebview?.callHandler(name, params)
        return window?.flutter_inappwebview?.callHandler(
          FnFormat[fnName].method,
          {
            name,
            data: params,
          },
        )
      } else {
        await retry(hasCallHandler, 5, 100)
        // return window?.flutter_inappwebview?.callHandler(name, params)
        return window?.flutter_inappwebview?.callHandler(
          FnFormat[fnName].method,
          {
            name,
            data: params,
          },
        )
      }
    }
  } catch (error) {}
}

function customMethod(fnName, data) {
  const customMethodObj = {
    setStatusBarColor(data) {
      const TopAdaptationDistance = document.getElementById(
        'TopAdaptationDistance',
      ) as HTMLElement
      TopAdaptationDistance.style.background = data
    },
    back(data) {
      $g.inApp
        ? window.flutter_inappwebview.callHandler('Operation', {
            name: 'back',
            data: { force: false, ...data },
          })
        : router.back()
    },
  }
  customMethodObj[fnName](data)
}

function retry(fn, times, delay) {
  return new Promise(function (resolve, reject) {
    const tFn = function () {
      fn(times)
        .then(resolve)
        .catch((e) => {
          if (times-- > 0) {
            setTimeout(tFn, delay)
          } else {
            reject(e)
          }
        })
    }
    return tFn()
  })
}
