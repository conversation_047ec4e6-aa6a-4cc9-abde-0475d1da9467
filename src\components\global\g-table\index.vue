<template>
  <div class="table-wrapper">
    <table
      class="my-table"
      :class="{
        'border-table': hasBorder,
        'table-radius': <PERSON><PERSON><PERSON>(tableRadius),
      }"
    >
      <thead v-show="showHeader" class="thead">
        <tr :style="headerStyle">
          <th
            v-for="item in tableOption.column"
            :key="item.prop"
            class="py-6px px-4px font-400"
            :class="[
              { 'fixed-item': item.fixed },
              'fixed-item-' + item.fixed?.direction,
            ]"
            v-bind="item.attr"
            :style="[
              {
                left:
                  item.fixed?.direction == 'left' && item.fixed?.gap
                    ? item.fixed?.gap + 'px'
                    : 0,
                right:
                  item.fixed?.direction == 'right' && item.fixed?.gap
                    ? item.fixed?.gap + 'px'
                    : 0,
                width: item.width || '',
                minWidth: item.minWidth || '',
              },
              item.customStyle,
            ]"
          >
            <slot :item="item" :name="item.prop + 'Header'">
              <div
                class="w-full h-full"
                :class="item.tooltip ? 'flex items-center justify-center' : ''"
              >
                {{ item.label }}
                <g-icon
                  v-if="item.tooltip"
                  v-dialog-tips="{
                    title: item.label,
                    message: item.tipContent,
                    type: 'onlyCheck',
                  }"
                  name="svg-headMaster-tips"
                  size="12"
                  class="text-12px ml-2px"
                />
              </div>
            </slot>
          </th>
        </tr>
      </thead>
      <tbody v-if="tableOption?.data?.length > 0">
        <tr
          v-for="(row, index) in tableOption.data"
          :key="index"
          class="tr-row"
          :class="[{ stripe }, rowClassName ? rowClassName(row, index) : '']"
        >
          <td
            v-for="(col, index2) in tableOption.column"
            :key="col.prop"
            class="py-6px px-4px"
            :class="[
              { 'fixed-item': col.fixed },
              'fixed-item-' + col.fixed?.direction,
            ]"
            :style="{
              left:
                col.fixed?.direction == 'left' && col.fixed?.gap
                  ? col.fixed?.gap + 'px'
                  : 0,
              right:
                col.fixed?.direction == 'right' && col.fixed?.gap
                  ? col.fixed?.gap + 'px'
                  : 0,
              width: col.width || '',
              minWidth: col.minWidth || '80px',
              ...cellStyle(row, index, col, index2),
            }"
            @click="cellClick(row, index, col, index2)"
          >
            <slot :row="row" :index="index" :col="col" :name="col.prop">
              <div v-if="col?.format" class="w-full h-full">
                {{ col?.format(row) }}
              </div>
              <div v-else class="w-full h-full">
                <!-- {{ row[col.prop] }} -->
                {{
                  col.type == 'index' ? index + 1 : formatEmpty(row[col.prop])
                }}
              </div>
            </slot>
          </td>
        </tr>
      </tbody>
      <tbody v-else>
        <tr>
          <td
            :colspan="tableOption?.column?.length"
            class="py-10px text-[#666]"
          >
            <g-empty></g-empty>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup lang="ts">
defineProps({
  tableOption: {
    type: Object,
    default() {
      return {
        column: [],
        data: [],
      }
    },
  },
  stripe: {
    type: Boolean,
    default: true,
  },
  description: {
    type: String,
    default: '暂无数据',
  },
  showHeader: {
    type: Boolean,
    default: true,
  },
  headerStyle: {
    type: Object,
    default() {
      return { background: '#fff', color: '#666' }
    },
  },
  // 行 根据条件添加类名
  rowClassName: {
    type: Function,
  },
  cellStyle: {
    type: Function,
    default() {
      return {}
    },
  },
  // 是否需要border
  hasBorder: {
    type: Boolean,
    default: false,
  },
  tableRadius: {
    type: [Boolean, String],
    default: false,
  },
  // 是否需要提示
  tooltip: {
    type: Boolean,
    default: false,
  },
  //提示内容
  tipContent: {
    type: [Number, String],
    default: '',
  },
})

const emit = defineEmits(['cellClick'])

function formatEmpty(cellValue) {
  let value = cellValue ?? '-'
  value = cellValue === '' ? '-' : value
  return value
}

function cellClick(row, index, col, index2) {
  emit('cellClick', { row, index, col, index2 })
}
</script>

<style lang="scss" scoped>
.table-wrapper {
  overflow-x: scroll;
  border-radius: v-bind(tableRadius);
  .my-table {
    width: 100%;
    border-collapse: collapse;
    text-align: center;
    font-size: 12px;
    .thead {
      background: rgba($color: #fff, $alpha: 0.4);
      font-size: 12px;
    }
    // 固定列
    .fixed-item {
      position: sticky;
      background-color: rgba(243, 247, 251, 0.5);
      // background: linear-gradient(
      //   -90deg,
      //   rgba(243, 247, 251, 0.1) 0%,
      //   #fff 23%
      // );
      backdrop-filter: blur(10px);
      z-index: 1;
      text-align: center;

      outline-color: #eee;
      outline-style: solid;
      outline-width: 1px;
      // TODO 暂时往左（往右）边移1px，解决定位后，滑动表格右侧（左侧）数据会显示出来
      &-left {
        transform: translateX(-1px);
      }
      &-right {
        transform: translateX(1px);
      }
    }
  }
  .border-table {
    tr,
    th,
    td {
      border: 1px solid #eee;
    }
  }
}

.tr-row {
  position: relative !important;
}
.tr-row.stripe {
  &:nth-child(odd) {
    background: #f3f7fb;
  }
}

.table-radius {
  border-collapse: collapse;
  border-radius: v-bind(tableRadius);
  // overflow: hidden;

  tr:first-child th:first-child {
    border-top-left-radius: v-bind(tableRadius);
  }

  tr:first-child th:last-child,
  tr:first-child td:last-child {
    border-top-right-radius: v-bind(tableRadius);
  }

  tr:last-child td:first-child {
    border-bottom-left-radius: v-bind(tableRadius);
  }

  tr:last-child td:last-child {
    border-bottom-right-radius: v-bind(tableRadius);
  }
}
</style>
