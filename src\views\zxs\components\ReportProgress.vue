<template>
  <div class="relative w-[156px] h-[78px]">
    <svg viewBox="0 0 156 78" x-mlns="http://www.w3.org/200/svg">
      <defs>
        <linearGradient id="gradient">
          <stop offset="0%" :style="{ 'stop-color': startColor }" />
          <stop offset="100%" :style="{ 'stop-color': stopColor }" />
        </linearGradient>
      </defs>

      <circle
        r="67"
        cx="78"
        cy="83"
        fill="none"
        :stroke="'#f0f0f0'"
        :stroke-width="12"
        stroke-linecap="round"
        :stroke-dasharray="`${bottomArc},10000`"
        transform-origin="bottom"
        transform="rotate(180)"
      />
      <circle
        class="progress-circle"
        r="67"
        cx="78"
        cy="83"
        fill="none"
        :stroke="'url(#gradient)'"
        :stroke-width="12"
        stroke-linecap="round"
        :stroke-dasharray="`${topArc},100000`"
        transform-origin="bottom"
        transform="rotate(180)"
      />
    </svg>

    <div
      class="circle-container"
      :style="{ transform: `rotate(${rotateDeg}deg)` }"
    >
      <div class="relative">
        <div
          class="circle"
          :style="{
            background: startColor,
            'box-shadow': `0px 2px 4px 0px ${stopColor}`,
          }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  percent: {
    type: Number,
    default: 0,
  },
  startColor: {
    type: String,
    default: '#fb758e',
  },
  stopColor: {
    type: String,
    default: '#ffa7a7',
  },
})

// l = πrα/180° π 是圆周率，r 表示半径，α 表示夹角

// 底部灰色半圆的弧长
let bottomArc = (Math.PI * 67 * 180) / 180

let topArc = $ref(0)

let rotateDeg = $ref(0)

watch(
  () => props.percent,
  () => {
    setTimeout(() => {
      // 根据百分比计算显示的弧长
      topArc = (Math.PI * 67 * (props.percent / 100) * 180) / 180
      // // 根据百分比计算小圆圈的旋转角度
      rotateDeg = (props.percent / 100) * 180
    }, 100)
  },
  { immediate: true },
)
</script>

<style lang="scss" scoped>
.circle-container {
  width: 50%;
  height: 1px;
  position: absolute;
  left: 0;
  bottom: 5px;
  line-height: 0;
  transform-origin: right center;
}

.circle {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 3px solid #fff;
  margin: 0;
  border-radius: 50%;
  position: absolute;
  left: 4px;
  top: 50%;
  transform: translateY(-50%) translateX(-1px);
}

.progress-circle {
  transition:
    stroke-dasharray 0.4s linear,
    stroke 0.3s;
}

.circle-container {
  transition: transform 0.4s linear;
}
</style>
