/**
 * @description 全局主题变量配置
 */
//颜色配置
:root {
  --van-blue: #217dfb !important;
  --van-green: #26c251 !important;
  --van-orange: #ffc613 !important;
  --van-red: #dd4848 !important;
  --van-padding-md: 12px !important;
  /* navbar */
  --van-nav-bar-height: 44px;
  --van-nav-bar-background: #fff !important;
  /* button */
  // --van-button-mini-height: 16px !important;
  // --van-button-mini-font-size: 10px !important;
  /* picker */
  --van-picker-confirm-action-color: #3496fa !important;
  /* tabs */

  /* 遮盖层 */
  --van-overlay-background: rgba(0, 0, 0, 0.5);
  /*视频*/
  --van-overlay-z-index: 3000;

  /* ---------tailwindcss---------- */
  --t-colors-white: #ffffff;
  --t-colors-black: #000;
  --t-colors-transparent: transparent;
  --t-colors-gray-lightest: #f7f8fa;
  --t-colors-gray-light: #dcdee2;
  --t-colors-gray-default: #999;
  --t-colors-gray-dark: #666;
  --t-colors-gray-darkest: #333;
  --t-colors-red-lightest: #ff9b97;
  --t-colors-red-dark: #d0494e;
  --t-colors-yellow-light: #ffe000;
  --t-colors-yellow-dark: #e79902;
  --t-colors-green-light: #02e487;
  --t-colors-green-dark: #409925;
  --t-colors-blue-lightest: #eaf5ff;
  --t-colors-blue-dark: #177ce1;

  // element
  --el-color-primary: var(--van-blue) !important;
  --el-color-info: var(--t-colors-gray-default) !important;
  --el-color-success: var(--van-green) !important;
  --el-color-warning: var(--van-orange) !important;
  --el-color-danger: var(--van-red) !important;
  --el-color-error: var(--van-red) !important;
}
