<template>
  <div>
    <div
      v-if="iconList.length"
      class="grid grid-cols-4 gap-y-[10px] justify-between mt-16px br-[12px] bg-white py-12px"
    >
      <div
        v-for="item in iconList"
        :key="item.id"
        class="van-haptics-feedback flex flex-col items-center"
        @click="androidJump({ data: item, callback: updateMajor })"
      >
        <img :src="item.image" class="w-[30px] h-31px m-auto" alt="" />
        <div class="w-full text-center text-12px mt-6px font-500 line-1 px-4px">
          {{ item.title }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from '@/stores/modules/user'
import { getIcon } from '@/api/home'
import androidJump from '../androidJump'

const userStore: any = useUserStore()
const router = useRouter()
let iconList: any = $ref([])

onBeforeMount(() => {
  getIconApi()
})

//跳转横屏项目
const landscapeList = ['guidance/guidanceMain', 'student/errorBook/main']
//跳转其他项目后回到页面调用函数更新数据
const updateDataList = ['career/main']

//获取图标
async function getIconApi() {
  try {
    let res = await getIcon({
      location: 4,
      version: userStore.version || '1.5.8',
    })
    iconList = res.filter((item) => item.type == 2)[0]?.list || []
    //增加安卓跳转参数，两个都不匹配的则默认为当前项目跳转
    iconList.forEach((item) => {
      const redirectUrl = item.redirect_url
      if (updateDataList.some((path) => redirectUrl.includes(path))) {
        item.updateData = true
        item.landscape = false
      }
      if (landscapeList.some((path) => redirectUrl.includes(path))) {
        item.landscape = true
      }
    })
  } catch (error) {
    console.log(`⚡[ error ] >`, error)
  }
}

//更新智推专业数据
function updateMajor() {
  $g.bus.emit('openMajorPolling')
}
</script>

<style lang="scss" scoped></style>
