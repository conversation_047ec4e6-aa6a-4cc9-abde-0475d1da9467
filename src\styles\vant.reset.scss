/* button按钮 */
.van-button {
  &.van-button--primary {
    background: var(--van-button-primary-background);
  }
  &.van-button--success {
    background: var(--van-button-success-background);
  }
  &.van-button--warning {
    background: var(--van-button-warning-background);
  }
  &.van-button--danger {
    background: var(--van-button-danger-background);
  }
  &.van-button--plain {
    background: var(--van-white);
  }
}

/* 导航栏 */
.van-nav-bar {
  .van-nav-bar__left {
    .van-icon-arrow-left {
      color: #333333 !important;
      font-size: 20px;
    }
  }
  &.van-hairline--bottom:after {
    display: none !important;
  }
}

/* 选择器picker */
.van-picker {
  --van-border-color: #e8e8e8;
  .van-picker__title {
    font-size: 14px !important;
    font-weight: 400;
  }
  .van-picker__columns {
    // height: 200px !important;
  }
  .van-picker__toolbar {
    height: 55px;
    button {
      padding: 20px 30px;
      height: 55px;
      line-height: 15px;
    }
  }
  .van-picker-column__item {
    padding: 0 24px;
    font-size: 14px;
    line-height: 18px;
    color: #666666;
  }
}
