<template>
  <div class="mdEditor-container">
    <!-- 普通预览 -->
    <v-md-preview
      v-if="mode === 'preview'"
      class="custom-md-preview"
      :text="text"
      :height="height"
      v-bind="$attrs"
    ></v-md-preview>
    <!-- 流式预览 -->
    <v-md-preview-stream
      v-else-if="mode === 'stream'"
      class="custom-md-preview"
      :text="text"
      :show-cursor="cursor"
      :height="height"
      v-bind="$attrs"
    >
    </v-md-preview-stream>
  </div>
</template>

<script setup lang="ts" inheritAttrs="false">
defineProps({
  mode: {
    type: String,
    default: 'edit', // edit-读写 preview-只读
  },
  height: {
    type: String,
    default: '600px',
  },
  cursor: {
    type: Boolean,
    default: false,
  },
})

// 富文本内容
let text = defineModel({ type: String, default: '' })

// 插入GPT文字块的弹窗是否可见
let dialogVisible = $ref(false)
</script>

<style lang="scss" scoped>
.loading-box {
  :deep() {
    .el-loading-mask {
      .el-loading-spinner {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

:deep() {
  .custom-md-preview .github-markdown-body {
    padding: 5px;
    font-size: 14px;
  }
  .github-markdown-body p {
    margin-bottom: 0;
  }
  .v-md-plugin-tip,
  tip {
    padding-bottom: 3.6vw;
  }
}
</style>
<style>
.qm-chat-cursor {
  background-image: url(@/assets/img/aiTeacher/aiLoading.gif);
  background-size: cover;
  display: inline-block;
  height: 12px;
  margin-left: 4px;
  position: relative;
  top: 1px;
  width: 12px;
}
</style>
