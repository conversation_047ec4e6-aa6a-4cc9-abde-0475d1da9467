<template>
  <router-view v-slot="{ Component }">
    <!-- <transition name="animation" mode="out-in"> -->
    <keep-alive :include="keepAliveArr">
      <component :is="Component" :key="$route.name" />
    </keep-alive>
    <!-- </transition> -->
  </router-view>
</template>

<script lang="ts" setup>
import { useRouterStore } from '@/stores/modules/router'
const routerStore = useRouterStore()
const { keepAliveArr } = storeToRefs(routerStore)
</script>

<style>
/* 轻微放大缩小动画配置代码 */
.animation-enter-from,
.animation-leave-to {
  transform: scale(0.95);
  opacity: 0;
}

.animation-enter-to,
.animation-leave-from {
  transform: scale(1);
  opacity: 1;
}

.animation-enter-active {
  transition: all 0.1s ease-out;
}

.animation-leave-active {
  transition: all 0.1s ease-in;
}
</style>
