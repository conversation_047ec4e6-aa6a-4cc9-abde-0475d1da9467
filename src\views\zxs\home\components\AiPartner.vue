<template>
  <div class="bg-white br-[12px] pt-19px pb-16px mt-16px">
    <ExaminationArea
      v-if="userStore?.userInfo?.studentDetail?.sysGradeId == 12"
    ></ExaminationArea>
    <div>
      <g-loading v-if="loading" class="h-200px"></g-loading>

      <div v-else>
        <div class="ml-16px flex items-center">
          <img
            :src="$g.tool.getFileUrl('home/AI.png')"
            alt=""
            class="w-21px h-15px"
          />
          <span class="AlimamaShuHeiTi font-bold text-16px">学伴</span>
        </div>
        <van-loading
          v-if="updateLoading"
          class="h-180px flex-cc"
          vertical
          color="#1989fa"
        >
          加载中...
        </van-loading>
        <div v-else>
          <!-- 目标成绩 -->
          <div
            class="ml-13px mr-12px mt-12px h-44px br-[4px] bg-[#F7F8FA] flex items-center justify-between relative"
          >
            <div class="flex-1 flex-cc">
              <span class="font-500 text-14px mr-6px">目标成绩</span>
              <span class="font-500 text-24px text-[#FF7303]"
                >{{ ifFirst ? 750 : aiPartnerInfo.score
                }}<span class="text-14px">分</span></span
              >
            </div>
            <img
              :src="$g.tool.getFileUrl('home/edit.png')"
              alt=""
              class="mr-14px h-16px w-16px flex-shrink-0"
              @click="showSetScore = true"
            />
            <g-lottie
              v-if="ifFirst"
              :options="lottieOptions"
              class="absolute -right-10px -bottom-15px"
              :height="50"
              :width="50"
              @anim-created="animCreated"
              @click="showSetScore = true"
            >
            </g-lottie>
          </div>

          <!-- 智推院校 -->
          <div
            class="ml-13px mr-11px mt-14px flex justify-between items-center"
          >
            <span class="font-600 text-14px">智推院校</span>
            <div class="flex items-center">
              <span
                class="text-12px text-[#666666] lh-[16px] h-16px"
                @click="goUniversity"
                >查看全部</span
              >
              <g-icon
                name="svg-home-arrowRight"
                size="12"
                color="#666666"
                class="mb-2px ml-4px mr-7px"
              />
            </div>
          </div>

          <div
            class="ml-12px mr-10px mt-11px flex h-88px relative bg-container"
          >
            <!-- 国内院校 -->
            <div
              class="text-white py-10px relative flex-1 w-0"
              @click="goToUniversity(1)"
            >
              <div class="ml-7px font-600 text-15px mb-13px">国内院校</div>
              <div class="ml-4px flex w-full">
                <img
                  v-if="aiPartnerInfo.targetScoreCollege?.logo"
                  :src="aiPartnerInfo.targetScoreCollege?.logo"
                  alt=""
                  class="w-32px h-32px mr-5px mt-3px flex-shrink-0"
                />
                <div class="flex-1 w-0">
                  <div class="font-500 text-14px mb-2px line-1">
                    {{ aiPartnerInfo.targetScoreCollege?.universityName }}
                  </div>
                  <div class="text-9px line-1" style="letter-spacing: 0.2em">
                    {{ aiPartnerInfo.targetScoreCollege?.tags?.join('|') }}
                  </div>
                </div>
              </div>

              <img
                :src="$g.tool.getFileUrl('home/wen.png')"
                alt=""
                class="absolute top-[-2px] right-0 w-21px h-22px"
              />
            </div>

            <!-- 国际院校 -->
            <div
              class="flex-1 text-white py-10px flex flex-col items-end w-0 pl-20px"
              @click="goToUniversity(2)"
            >
              <div class="ml-7px font-600 text-15px mb-13px mr-10px">
                国际院校
              </div>
              <div class="ml-4px flex mr-9px w-full pl-16px">
                <img
                  v-if="aiPartnerInfo.internationalSchool?.schoolBadge"
                  :src="aiPartnerInfo.internationalSchool?.schoolBadge"
                  alt=""
                  class="w-32px h-32px mr-5px mt-3px flex-shrink-0"
                />
                <div class="flex-1 w-0">
                  <div class="font-500 text-14px mb-2px line-1">
                    {{ aiPartnerInfo.internationalSchool?.schoolName }}
                  </div>
                  <div class="text-9px line-1">
                    {{ aiPartnerInfo.internationalSchool?.tags }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 智推专业 -->
        <div class="ml-13px mt-10px flex items-center">
          <div class="font-600 text-14px">智推专业</div>
          <div v-if="ifShowMajor" class="text-12px text-[#999999] ml-7px">
            根据职业兴趣测评结果推荐
          </div>
        </div>
        <div
          v-if="ifShowMajor"
          class="ml-14px mr-12px mt-12px major py-10px pr-9px pl-8px h-128px"
        >
          <div class="flex justify-between mb-11px items-center">
            <div class="font-600 text-12px ml-2px">推荐专业</div>
            <div class="flex items-center">
              <span
                class="text-12px text-[#666666]"
                @click="
                  $router.push({
                    name: 'EvaluationReport',
                    query: { student_report_id },
                  })
                "
              >
                完整报告</span
              >
              <g-icon
                name="svg-home-arrowRight"
                color="#666666"
                size="12"
                class="ml-4px mb-2px"
              />
            </div>
          </div>
          <div class="flex flex-wrap gap-6px h-54px">
            <div
              v-for="item in majorList.slice(0, 6)"
              :key="item"
              class="w-96px h-24px line-1 text-12px bg-white br-[4px] text-center lh-[24px] px-6px"
              @click="showProfession(item)"
            >
              {{ item.professional_name }}
            </div>
          </div>
          <div class="flex items-center mt-6px">
            <img
              :src="$g.tool.getFileUrl('home/info.png')"
              alt=""
              class="w-13px h-13px mb-2px mr-2px"
            />
            <span class="text-[#999999] text-12px"
              >点击可查看专业介绍及包含专业</span
            >
          </div>
        </div>
        <div v-else class="relative">
          <van-loading
            v-show="careerLoading"
            class="h-full absolute w-full flex-cc"
          ></van-loading>
          <img
            :src="$g.tool.getFileUrl('home/zhiyexingqu.png')"
            alt=""
            class="px-13px mt-10px w-full"
            :class="[careerLoading ? 'opacity-[0.5]' : '']"
            @click="jumpCareer()"
          />
        </div>
      </div>
      <MajorPop v-model:show="showPop" :aiMajorInfo="curProfession"></MajorPop>
      <SetScore
        v-model:show="showSetScore"
        :info="aiPartnerInfo"
        @has-set="update"
      ></SetScore>
    </div>
  </div>
</template>
<script setup lang="ts">
import MajorPop from './MajorPop.vue'
import SetScore from './SetScore.vue'
import { getAiStudentPartner, getZxMajorList } from '@/api/aiStudentPartner'
import { useJumpCareer } from '../hooks/jumpCareer'
import fingerClick from '../click.json'
import ExaminationArea from './ExaminationArea.vue'
import { useUserStore } from '@/stores/modules/user'

const userStore: any = useUserStore()
let majorList = $ref<any>([])
let showPop = $ref(false)
let showSetScore = $ref(false)
let aiPartnerInfo = $ref<any>({})
let student_report_id = $ref<any>(null)
let curProfession = $ref<any>({})
let loading = $ref(true)
let updateLoading = $ref(false)
const router = useRouter()
const ifShowMajor = $computed(() => {
  return majorList.length > 0
})
const { loading: careerLoading, jumpCareer } = useJumpCareer()
//是否设置过目标成绩
const ifFirst = $computed(() => {
  return !aiPartnerInfo.score
})

let stopPolling: any = $ref(null)

async function init() {
  let res = await getAiStudentPartner()
  aiPartnerInfo = res || {}
}

async function getRecommendedMajor() {
  try {
    let res = await getZxMajorList()
    if (!res || res?.length == 0) {
      majorList = []
      student_report_id = null
      return
    }
    majorList = res.recommend_subjects || []
    student_report_id = res.student_repot_id
  } catch (err) {
    console.log('获取专业失败', err)
  }
}
function goUniversity() {
  if (ifFirst) {
    showSetScore = true
    return
  }
  router.push({
    name: 'AiStudentPartnerMore',
  })
}

function showProfession(item) {
  curProfession = {
    ...item,
    list: item.professional[0].include_professional.split('、'),
  }
  showPop = true
}
onBeforeMount(async () => {
  loading = true
  try {
    await Promise.all([init(), getRecommendedMajor()])
    loading = false
  } catch {
    loading = false
  }
})

const lottieOptions = {
  animationData: fingerClick,
  loop: true,
  renderer: 'svg',
  autoplay: true,
  speed: 20,
}

function animCreated(anim) {
  anim.setSpeed(1.4)
}

async function update() {
  updateLoading = true
  try {
    await init()
    updateLoading = false
  } catch (e) {
    console.log(e)
    updateLoading = false
  }
}

function goToUniversity(type) {
  //type为1是国内 2是国际
  if (type == 1) {
    if (
      !aiPartnerInfo.targetScoreCollege ||
      !aiPartnerInfo.targetScoreCollege.universityId
    )
      return
    $g.flutter('nativeRoute', {
      name: '/collegeDetail',
      backCallJsRefresh: false,
      params: {
        collegeId: aiPartnerInfo.targetScoreCollege.universityId, //注意这里是String
        collegeName: aiPartnerInfo.targetScoreCollege.universityName,
      },
    })
    return
  }

  if (
    !aiPartnerInfo.internationalSchool ||
    !aiPartnerInfo.internationalSchool.internationalSchoolId
  )
    return
  $g.flutter('nativeRoute', {
    name: '/foreignCollegeDetail',
    backCallJsRefresh: false,
    params: {
      internationalSchoolId:
        aiPartnerInfo.internationalSchool.internationalSchoolId,
      internationalSchoolName: aiPartnerInfo.internationalSchool.schoolName,
    },
  })
}

onMounted(() => {
  $g.bus.on('openMajorPolling', refresh)
})

onBeforeUnmount(() => {
  $g.bus.off('openMajorPolling', refresh)
})

function refresh() {
  loading = true
  setTimeout(async () => {
    try {
      await getRecommendedMajor()
      loading = false
    } catch (e) {
      console.log(e)
      loading = false
    }
  }, 500)
}
</script>
<style lang="scss" scoped>
.inland {
  background: url(@/assets/img/home/<USER>/ 100% 100% no-repeat;
  width: calc((100vw - 22px - 21px) / 2 + 2px);
}
.international {
  background: url(@/assets/img/home/<USER>/ 100% 100% no-repeat;
  width: calc((100vw - 22px - 21px) / 2);
}

.major {
  background: url(@/assets/img/home/<USER>/ 100% 100% no-repeat;
}

.bg-container {
  background: url(@/assets/img/home/<USER>/ 100% 100%
    no-repeat;
}
</style>
