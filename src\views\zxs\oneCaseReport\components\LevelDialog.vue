<template>
  <van-popup v-model:show="show" class="level-dialog">
    <g-table
      :tableOption="tableOption"
      :cellStyle="cellStyle"
      :header-style="headerStyleConfig"
    >
      <template #levelName="{ row }">
        <div class="flex items-center">
          <div class="">{{ row.levelName }}</div>
          <div class="text-[#999999]">{{ row.score }}</div>
        </div>
      </template>
    </g-table>
    <button
      class="bg-[#6365FF] text-white w-165px h-36px rounded-[10px] flex-cc mx-auto mt-12px"
      @click="show = false"
    >
      好的
    </button>
  </van-popup>
</template>

<script setup lang="ts">
const show = defineModel<boolean>('show')

const headerStyleConfig = {
  background: '#f0f4ff',
  color: '#333',
  textAlign: 'left', // 如果需要左对齐
}

const tableOption = reactive({
  column: [
    {
      prop: 'levelName',
      label: '等级',
      customStyle: { textAlign: 'left', color: '#333' },
    },
    {
      prop: 'lowestScore',
      label: '能力状态',
      customStyle: { textAlign: 'left', color: '#333' },
    },
  ],
  data: [
    {
      levelName: '差',
      score: '（0-60）',
      lowestScore: '认识基础概念',
    },
    {
      levelName: '较差',
      score: '（61-80）',
      lowestScore: '操作基础工具',
    },
    {
      levelName: '一般',
      score: '（81-110）',
      lowestScore: '独立完成分析任务',
    },
    {
      levelName: '好',
      score: '（111-130）',
      lowestScore: '解决高阶问题',
    },
    {
      levelName: '很好',
      score: '（131-150）',
      lowestScore: '设计数据解决方案',
    },
  ],
})
function cellStyle(row, index, col, index2) {
  return {
    textAlign: col.customStyle?.textAlign,
  }
}
</script>

<style lang="scss" scoped>
.level-dialog {
  width: 298px;
  height: 330px;
  background-color: transparent;
  background-image: url('@/assets/img/oneCaseReport/levelDialogBg.png');
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
  padding-top: 70px;
  padding-left: 12px;
  padding-right: 12px;
  overflow: hidden;
}
</style>
