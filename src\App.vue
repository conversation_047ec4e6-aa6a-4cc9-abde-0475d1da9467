<template>
  <div>
    <van-config-provider>
      <router-view />
    </van-config-provider>
  </div>
</template>

<script lang="ts" setup>
import { useSettingStore } from '@/stores/modules/setting'
const settingStore = useSettingStore()

if ($g.inApp) {
  $g.flutter('padding').then((res) => {
    var TopAdaptationDistance = document.getElementById('TopAdaptationDistance')
    if (TopAdaptationDistance)
      TopAdaptationDistance.style.height = res?.length
        ? `${res?.[1]}px`
        : '48px'
    $g.navigationHeight = res?.[1] || 48
    settingStore.navigationHeight = res?.[1] || 48
  })
}
</script>
