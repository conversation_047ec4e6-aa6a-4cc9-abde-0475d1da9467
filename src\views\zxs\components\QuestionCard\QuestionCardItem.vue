<template>
  <div class="question-card-item">
    <!-- 题目附加数据 -->
    <div class="flex items-center justify-between mb-12px">
      <div
        class="flex-shrink-0 text-[#141414] text-[12px] px-8px br-[2px] bg-[#f3f3f3] leading-[24px]"
      >
        {{ questionItem.jkNewQuestionTypeName }} ID:{{
          questionItem.paperQuestionId
        }}
      </div>
    </div>

    <!-- 主题题干 -->
    <g-mathjax
      :text="questionItem.paperQuestionTitle"
      class="mb-[18px] overflow-y-hidden overflow-x-auto"
    />

    <!-- 多子题切换按钮 -->
    <div
      v-if="questionItem.subQuestionList.length > 1"
      :key="questionItem.paperQuestionId"
      class="flex items-center mb-[10px] pb-6px overflow-x-auto no-bar"
    >
      <div
        v-for="(subQuestion, subIndex) in questionItem.subQuestionList"
        :id="'sub-btn-' + subIndex"
        :key="subIndex"
        class="w-32px h-32px border border-[#DFDFDF] rounded-[8px] flex-cc mr-12px flex-shrink-0"
        :class="{
          'border-theme-primary text-white bg-theme-primary':
            curSubQuestionIndex === subIndex,
        }"
        @click="handleSubQuestionIndexChange(subIndex)"
      >
        {{ subIndex + 1 }}
      </div>
    </div>

    <!-- 子题目 -->
    <template
      v-for="(subQuestion, subIndex) in questionItem.subQuestionList"
      :key="subQuestion.paperSubQuestionId"
    >
      <div v-show="curSubQuestionIndex === subIndex" class="sub-question">
        <!-- 子题标题 -->
        <g-mathjax
          v-if="subQuestion.paperSubQuestionTitle"
          :text="subQuestion.paperSubQuestionTitle"
          class="overflow-x-auto overflow-y-hidden mb-[18px]"
        />

        <!-- 选择题,判断题选项 -->
        <button
          v-for="(optionItem, index) in subQuestion.options"
          :key="index"
          class="choice-option"
          :class="
            ifCorrect(optionItem, subQuestion)
              ? 'option-correct'
              : 'option-error'
          "
        >
          <!-- 选项内容 -->
          <div class="flex-1 overflow-hidden flex items-center">
            <div class="flex-shrink-0 mr-4px text-[#333]">
              {{ optionItem.name && optionItem.name + '.' }}
            </div>

            <g-mathjax
              :text="optionItem.content"
              class="flex-1 overflow-x-auto overflow-y-hidden"
            ></g-mathjax>
          </div>

          <!-- 选择状态反馈 -->
          <div class="flex items-center flex-shrink-0">
            <!-- 我的答案 -->
            <span
              v-if="
                subQuestion.studentAnswer?.split(',').includes(optionItem.name)
              "
              class="my-answer mx-6px"
            >
              我的答案
            </span>
            <!-- 选择正确 -->
            <g-icon
              :name="
                ifCorrect(optionItem, subQuestion)
                  ? 'svg-common-correctOption'
                  : 'svg-common-errorOption'
              "
              size="16"
            />
          </div>
        </button>
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  questionItem: {
    type: Object,
    required: true,
  },
})

const emits = defineEmits(['subIndexChange'])

let curSubQuestionIndex = $ref(0)
// 大题变化以后,重置当前子题的序号
watch(
  () => props.questionItem,
  () => {
    curSubQuestionIndex = 0
    emits('subIndexChange', curSubQuestionIndex)
  },
  { immediate: true },
)

// 该选项是否是正确选项
function ifCorrect(optionItem, subQuestion) {
  return subQuestion.questionAnswer.split('').includes(optionItem.name)
}

// 多个按钮滚动到中间
function scrollIntoCenter() {
  document.getElementById('sub-btn-' + curSubQuestionIndex)?.scrollIntoView({
    behavior: 'smooth',
    block: 'nearest',
    inline: 'center',
  })
}

// 子题切换
async function handleSubQuestionIndexChange(index) {
  curSubQuestionIndex = index
  scrollIntoCenter()
  emits('subIndexChange', curSubQuestionIndex)
  await nextTick()
  $g.tool.renderMathjax()
}
</script>

<style scoped lang="scss">
:deep() {
  .g-mathjax {
    img {
      display: inline;
    }
  }
}

.choice-option {
  @apply w-full min-h-[40px] py-[9px] px-[12px] mb-[10px] br-[8px] border border-[#dfdfdf] text-left text-[14px] text-[#666] flex items-center overflow-hidden;
}

// 正确答案
.option-correct {
  border-color: #79b879;
  .my-answer {
    color: #79b879;
  }
}
//错误答案
.option-error {
  border-color: #f5222d;
  .my-answer {
    color: #f5222d;
  }
}
</style>
