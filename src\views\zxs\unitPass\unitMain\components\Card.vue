<template>
  <div
    class="bg-[#FFFFFF] br-[12px] pt-[14px]"
    :class="{ 'pb-[14px]': !data.expand }"
  >
    <div v-if="isShow" class="flex">
      <div class="flex items-center">
        <div class="chapterTitle flex items-center">
          <g-icon name="svg-unitPass-chapterTitle"></g-icon>
        </div>
        <div class="flex items-center" @click="onParentChange(data)">
          <div
            class="text-[18px] mr-[8px] text-[#333333] ml-[12px] font-600 mx-[9px] select-none flex items-center"
          >
            第{{
              currentSubject == 2 && isHxn ? data?.index : data?.index + 1
            }}章
          </div>
          <g-icon
            :name="
              data.expand ? 'svg-unitPass-expand' : 'svg-unitPass-noExpand'
            "
            size="18"
          ></g-icon>
        </div>
      </div>
      <div class="flex ml-[44px]">
        <div class="mr-[12px] w-[92px] h-[27px]"></div>
        <!-- <img
          v-if="data?.unitDiagnosisIsShow"
          src="@/assets/img/unitPass/diagnosis.png"
          class="mr-[12px] w-[92px] h-[27px]"
          @click="
            $router.push({
              name: 'UnitExercise',
              query: {
                type: 1,
                sysTextbooksCatalogId: data.sys_textbooks_catalog_id,
                jk_new_chapter_name: data.jk_new_chapter_name,
              },
            })
          "
        /> -->
        <img
          v-if="data?.unitTestIsShow"
          class="w-[92px] h-[27px]"
          src="@/assets/img/unitPass/test.png"
          @click="
            $router.push({
              name: 'UnitExercise',
              query: {
                type: 3,
                sysTextbooksCatalogId: data.sys_textbooks_catalog_id,
                jk_new_chapter_name: data.jk_new_chapter_name,
              },
            })
          "
        />
      </div>
    </div>
    <div v-if="isShow" class="text-[12px] text-[#979797] mt-[4px] mx-[16px]">
      {{ $g.tool.unicodeToZH(data?.jk_new_chapter_name) }}
    </div>
    <div
      v-if="data.expand && isShow"
      class="w-[343px] mt-[7px] h-[1px] bg-[#F6F6F6]"
    ></div>
    <div v-if="data.loading" class="flex justify-center py-10px">
      <!-- <img
        src="@/assets/img/aiTeacher/loading.gif"
        class="w-30px h-30px"
        alt=""
      /> -->
      <!-- <van-loading /> -->
      <span class="text-[#999]">加载中…</span>
    </div>
    <div v-else>
      <div v-if="data.expand">
        <div
          v-if="data.children?.length"
          :class="isShow ? 'mt-[20px]' : ''"
          class="pb-[16px] flex flex-col gap-[26px]"
        >
          <div
            v-for="(childrenItem, childrenIndex) in data.children"
            :key="childrenIndex"
            class="ml-[16px]"
          >
            <div>
              <div class="text-[15px] font-500 text-[#333333]">
                {{ $g.tool.unicodeToZH(childrenItem.jk_new_chapter_name) }}
              </div>
              <div class="mt-[12px]">
                <div class="flex relative items-center study mb-[20px]">
                  <span class="text-[12px] text-[#217DFB] mr-[3px]">学:</span
                  ><van-progress
                    :percentage="childrenItem.record_rate || 0"
                    :show-pivot="false"
                    track-color="rgba(33,125,251,0.1)"
                    class="w-[162px] h-[16px]"
                  />
                  <span class="text-[#217DFB] text-[10px] ml-[12px]"
                    >{{ childrenItem.record_rate || 0 }}%</span
                  >
                  <img
                    src="@/assets/img/unitPass/study.png"
                    class="w-[92px] h-[24px] absolute right-[14px]"
                    @click="onSClick(data, childrenItem)"
                  />
                </div>
                <div class="flex relative items-center practice">
                  <span class="text-[12px] text-[#FF8A44] mr-[3px]">练:</span
                  ><van-progress
                    :percentage="childrenItem.exer_rate || 0"
                    track-color="rgba(255,165,71,0.1)"
                    :show-pivot="false"
                    class="w-[162px] h-[16px]"
                  />
                  <span class="text-[#FF8A44] text-[10px] ml-[12px]"
                    >{{ childrenItem.exer_rate || 0 }}%</span
                  >
                  <img
                    src="@/assets/img/unitPass/practice.png"
                    class="w-[92px] h-[24px] absolute right-[14px]"
                    @click="onPClick(data, childrenItem)"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <g-empty v-else class="ml-[16px]"></g-empty>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useUserStore } from '@/stores/modules/user'
let props = defineProps({
  data: {
    type: Object,
    default: {},
  },
  currentSubject: {
    type: Number,
    default: null,
  },
})
const userStore = useUserStore()
let isHxn = $computed(() => {
  return userStore.userInfo?.studentDetail?.schoolId == 44
})
let isShow = $computed(() => {
  if (!isHxn) {
    return true
  }
  return (
    props.currentSubject != 2 ||
    (props.currentSubject == 2 && props.data?.index != 0)
  )
})
let emit = defineEmits<{
  (e: 'onParentChange', value: any): void
  (e: 'onChildChange', value: Record<string, any>): void
}>()
function onParentChange(treeItem) {
  treeItem.expand = !treeItem.expand
  emit('onParentChange', { item: treeItem })
}
const onChildChange = (item) => {
  emit('onChildChange', item)
}
const router = useRouter()
function onPClick(data, childrenItem) {
  onChildChange({
    treeItem: data,
    item: childrenItem,
  })
  router.push({
    name: 'UnitExercise',
    query: {
      type: 2,
      sysTextbooksCatalogId: childrenItem.sys_textbooks_catalog_id,
      jk_new_chapter_name: data.jk_new_chapter_name,
    },
  })
}
function onSClick(data, childrenItem) {
  onChildChange({
    treeItem: data,
    item: childrenItem,
  })
  router.push({
    name: 'UnitStudy',
    query: {
      sysTextbooksCatalogId: childrenItem.sys_textbooks_catalog_id,
    },
  })
}
</script>
<style scoped lang="scss">
:deep() {
  .chapterTitle {
    .iconfont {
      width: 4px !important;
      height: 16px !important;
    }
  }
  .study {
    .van-progress__portion {
      background: repeating-linear-gradient(
        124deg,
        rgba(69, 180, 253, 1) 0,
        rgba(69, 180, 253, 1) 3px,
        rgba(33, 125, 251, 1) 0,
        rgba(33, 125, 251, 1) 6px
      );
      border-radius: 8px;
      height: 10px;
      max-width: calc(100% - 4px) !important;
      left: 2px;
    }
    .van-progress {
      border-radius: 8px;
      padding: 2px;
      border: 1px solid rgba(33, 125, 251, 0.38);
    }
  }
  .practice {
    .van-progress__portion {
      background: repeating-linear-gradient(
        124deg,
        rgba(255, 165, 71, 1) 0,
        rgba(255, 165, 71, 1) 3px,
        rgba(255, 112, 55, 1) 0,
        rgba(255, 112, 55, 1) 6px
      ) !important;
      border-radius: 8px;
      height: 10px;
      max-width: calc(100% - 4px) !important;
      left: 2px;
    }
    .van-progress {
      border-radius: 8px;
      padding: 2px;
      border: 1px solid #fae3b7 !important;
    }
  }
}
</style>
