import config from '@/config/index'
import request from '@/utils/request/index'
const { baseURL, baseURL3 } = config

export function getQuestionList(data) {
  return request.get(baseURL3 + '/pad/homework/record/question/list', data)
}

export function getWeekList(data?) {
  return request.get(baseURL + '/v3/student/teachWeek/weekList', data)
}

export function getWeekTaskList(data?) {
  return request.get(baseURL + '/v3/student/teachWeek/study/taskList', data)
}

export function getTextbookList(data?) {
  return request.get(baseURL + '/v3/student/teachWeek/versionAndBookList', data)
}

export function getChapterList(data?) {
  return request.get(baseURL + '/v3/student/teachWeek/catalogList', data)
}

export function getWeekPlan(data?) {
  return request.get(baseURL + '/v3/student/teachWeek/plan', data)
}

export function getTaskListPage(data?) {
  return request.get(baseURL3 + '/pad/homework/page', data)
}

export function submitPaper(data) {
  return request.post(baseURL3 + '/pad/homework/record/submit', data)
}

export function submitAnswer(data) {
  return request.post(baseURL3 + '/pad/homework/record/question/submit', data)
}

//附件查询记录更新
export function setAttachStatus(data) {
  return request.post(baseURL3 + '/pad/homework/attach/update', data, {
    replace: true,
  })
}

//开始/再次训练
export function startTraining(data) {
  return request.post(baseURL3 + '/pad/homework/paper/create', data, {
    replace: true,
  })
}

//开始/再次训练
export function getCourseFileList(data) {
  return request.get(baseURL + '/v3/student/teachWeek/courseFile', data)
}

//资源详情
export async function getResourceDetail(data) {
  return request.get(baseURL + '/v3/resource/chapter/resourceDetail', data)
}

//查看报告-试卷 select
export function getRecordPaperList(data) {
  return request.get(baseURL3 + '/pad/homework/record/paperSelect', data)
}
//查看报告-时间 select
export function getTimeSelect(data) {
  return request.get(baseURL3 + '/pad/homework/record/timeSelect', data)
}
//报告
export function getHomeWorkReport(data) {
  return request.get(baseURL3 + '/pad/homework/record/report', data)
}
