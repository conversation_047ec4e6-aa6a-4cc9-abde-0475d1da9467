<template>
  <div class="h-screen">
    <g-navbar customTitle="导学案" />
    <g-loading v-if="showLoading" class="h-200px"></g-loading>
    <div v-else class="h-full">
      <div v-if="fileType === 'video'">
        <g-video
          :url="fileInfo.fileAbsoluteUrl"
          :config="{ fluid: false, poster: fileInfo.videoCoverUrl }"
          class="!w-full !h-[calc(100vh-269px)]"
        />
      </div>
      <div v-else-if="fileType === 'file'" class="h-full">
        <File v-if="fileInfo.fileAbsoluteUrl" :src="fileInfo.fileAbsoluteUrl" />
      </div>
      <g-empty v-else description="暂不支持该文件预览"></g-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getResourceDetail } from '@/api/syncTrain'
import File from '../components/File.vue'

const route = useRoute()

let showLoading = $ref<any>(true)
let fileType = $ref<any>('')
let fileInfo = $ref<any>({})

const getResourceDetailApi = async () => {
  let res = await getResourceDetail({
    newChapterResourceId: route.query.newChapterResourceId,
  })
  showLoading = false
  fileInfo = res
  fileType = $g.tool.resourceType(fileInfo.fileAbsoluteUrl)
}

onBeforeMount(() => {
  getResourceDetailApi()
})
</script>

<style lang="scss" scoped></style>
