<template>
  <van-popup
    v-model:show="show"
    round
    safe-area-inset-bottom
    :lock-scroll="false"
    position="bottom"
    class="max-h-[80vh] min-h-[50vh] overflow-hidden flex flex-col px-16px"
    :click-overlay="() => emit('close')"
    @open="$g.tool.renderMathjax"
  >
    <!-- 关闭弹窗按钮 -->
    <div
      class="text-theme-primary flex-cc pt-14px pb-8px"
      @click="() => emit('close')"
    >
      <span class="mr-2px leading-[22px]">收起解析</span>
      <van-icon name="arrow-double-left" class="-rotate-90" />
    </div>

    <!-- 显示答案和解析 -->
    <div class="flex-1 pl-10px overflow-x-hidden overflow-y-auto">
      <!-- 正确答案 -->
      <div class="section-title">正确答案</div>
      <g-mathjax
        class="overflow-x-auto overflow-y-hidden"
        :text="subQuestion[answerKey]"
      />

      <!-- 解析 -->
      <div class="section-title">解析</div>
      <g-mathjax
        class="overflow-x-auto overflow-y-hidden hidden-video"
        :text="subQuestion[parseKey]"
      />
    </div>

    <!-- 答题结果选择按钮，非选择题且没有确定答案时显示 -->
    <div
      v-show="
        ifNotChoice && !subQuestion[correctKey] && subQuestion[jumpKey] !== 2
      "
      class="flex justify-between mt-[16px] mb-6px"
    >
      <div
        class="action-btn action-cant"
        :class="choose === 'cant' && 'active'"
        @click="choose = 'cant'"
      >
        <img
          v-if="choose === 'cant'"
          :src="$g.tool.getFileUrl('common/resultCant.png')"
          class="w-[29px] mr-6px"
        />
        我不会
      </div>

      <div
        class="action-btn action-error"
        :class="choose === 'error' && 'active'"
        @click="choose = 'error'"
      >
        <img
          v-if="choose === 'error'"
          :src="$g.tool.getFileUrl('common/resultError.png')"
          class="w-[27px] mr-6px"
        />
        答错了
      </div>

      <div
        class="action-btn action-correct"
        :class="choose === 'correct' && 'active'"
        @click="choose = 'correct'"
      >
        <img
          v-if="choose === 'correct'"
          :src="$g.tool.getFileUrl('common/resultCorrect.png')"
          class="w-[32px] mr-6px"
        />
        答对了
      </div>
    </div>

    <!-- 已选答案回显，非选择题且答案确定时显示 -->
    <div
      v-show="
        ifNotChoice && (subQuestion[correctKey] || subQuestion[jumpKey] === 2)
      "
      class="w-[165px] h-36px br-[8px] bg-[red] text-white flex-cc mt-16px mb-14px mx-auto"
      :style="{
        background:
          subQuestion[jumpKey] === 2
            ? '#FB9721'
            : subQuestion[correctKey] === 1
              ? '#F5222D'
              : '#51C551',
      }"
    >
      <img
        class="w-[50px] mb-4px self-end"
        :src="
          subQuestion[jumpKey] === 2
            ? $g.tool.getFileUrl('common/resultCantBig.png')
            : subQuestion[correctKey] === 1
              ? $g.tool.getFileUrl('common/resultErrorBig.png')
              : $g.tool.getFileUrl('common/resultCorrectBig.png')
        "
      />
      <span class="mx-14px">丨</span>
      <span class="AlimamaShuHeiTi">
        {{
          subQuestion[jumpKey] === 2
            ? '我不会'
            : subQuestion[correctKey] === 1
              ? '答错了'
              : '答对了'
        }}
      </span>
    </div>

    <!-- 下一题按钮，具体跳转逻辑在父组件中处理,如果是主观题，切没有选择答案之前，禁用此按钮 -->
    <van-button
      type="primary"
      class="w-full h-[40px] my-10px flex-shrink-0"
      round
      :disabled="
        ifNotChoice &&
        !subQuestion[correctKey] &&
        subQuestion[jumpKey] !== 2 &&
        !choose
      "
      @click="handleNext"
    >
      {{ isLastQuestion && isLastSubQuestion ? '完成作答' : '看完了，下一题' }}
    </van-button>
  </van-popup>
</template>

<script setup lang="ts">
const props = defineProps({
  subQuestion: {
    type: Object,
    default: () => ({}),
  },
  // 是否是选择题
  ifChoice: {
    type: Boolean,
    default: false,
  },
  // 是否是最后一个题目，最后一个题目最后一个小问的提交按钮文案会变成 完成作答
  isLastQuestion: {
    type: Boolean,
    default: false,
  },

  // 如果是单个小问或者当前是多个小问的最后一问
  isLastSubQuestion: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['handleButtonClick', 'close', 'handleNext'])

// 做题结果的key，兼容两种变量模式
const correctKey = $computed(
  () =>
    ['is_correct', 'isCorrect'].find((key) =>
      Reflect.has(props.subQuestion, key),
    ) || '',
)
// 结果为我不会的key,兼容两种变量模式
const jumpKey = $computed(
  () =>
    ['is_jump', 'isJump'].find((key) => Reflect.has(props.subQuestion, key)) ||
    '',
)
// 题目答案的key,兼容两种变量模式
const answerKey = $computed(
  () =>
    ['question_answer', 'questionAnswer'].find((key) =>
      Reflect.has(props.subQuestion, key),
    ) || '',
)
// 题目解析的key,兼容两种变量模式
const parseKey = $computed(
  () =>
    ['question_parse', 'questionParse'].find((key) =>
      Reflect.has(props.subQuestion, key),
    ) || '',
)

const show = defineModel<boolean>('show')
// 是否是非选择题
const ifNotChoice = $computed(() => !props.ifChoice)

let choose: any = $ref('')

// 点击看完了下一题按钮处理函数
async function handleNext() {
  choose && emit('handleButtonClick', choose)
  await nextTick()
  emit('close')
  emit('handleNext')
}
</script>

<style lang="scss" scoped>
.section-title {
  @apply text-15px leading-[22px] font-500 py-10px relative;

  &::after {
    content: '';
    @apply inline-block w-4px h-14px bg-[#217dfb] br-[3px] absolute -left-10px top-1/2 -translate-y-1/2;
  }
}

.action-btn {
  &:not(:last-child) {
    margin-right: 18px;
  }
  @apply flex-1 flex items-center justify-center rounded-[8px] h-36px leading-[22px] max-w-[120px];
}

// 解析中的视频隐藏
.hidden-video {
  :deep() {
    video {
      display: none;
    }
  }
}

//我不会按钮
.action-cant {
  color: rgba(251, 151, 33, 1);
  background-color: rgba(251, 151, 33, 0.16);
  &.active {
    color: #fff;
    background-color: #ffa31d;
  }
}

//答错了按钮
.action-error {
  color: rgba(245, 34, 45, 1);
  background-color: rgba(245, 34, 45, 0.16);
  &.active {
    color: #fff;
    background-color: #f5222d;
  }
}

//答对了按钮
.action-correct {
  color: rgba(121, 184, 121, 1);
  background-color: rgba(121, 184, 121, 0.16);
  &.active {
    color: #fff;
    background-color: #51c551;
  }
}
</style>
