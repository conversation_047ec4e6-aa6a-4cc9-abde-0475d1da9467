<template>
  <div class="flex flex-col items-center justify-center py-20px relative">
    <div
      class="relative flex flex-col items-center"
      :style="{ width: size || '200px' }"
    >
      <g-lottie :options="lottieOptions" @anim-created="animCreated">
      </g-lottie>
      <!-- <div class="loading"></div> -->
      <slot>
        <div
          v-show="showText"
          class="loading-text text-14px absolute bottom-[0px] w-100px text-center"
          :style="{ color: textColor }"
        >
          加载中...
        </div>
      </slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
import feiji from './feiji.json'

defineProps({
  textColor: {
    type: String,
    default: '#969799',
  },
  size: {
    type: String,
    default: '200px', // 单位 px
  },
  showText: {
    type: Boolean,
    default: true,
  },
})

const lottieOptions = {
  animationData: feiji,
  // path: 'https://frontend-cdn.qimingdaren.com/cloud-school/exam/feiji.json',
  loop: true,
  renderer: 'svg',
  autoplay: true,
  speed: 20,
}

function animCreated(anim) {
  anim.setSpeed(1.4)
}
</script>

<style lang="scss" scoped></style>
