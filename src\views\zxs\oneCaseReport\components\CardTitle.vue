<template>
  <div class="flex items-center justify-between">
    <div
      class="text-17px text-[#000340] AlimamaShuHeiTi title-with-caidai"
      :class="{ 'title-ellipsis': ellipsis }"
      :style="ellipsis ? { maxWidth: maxWidth + 'px' } : {}"
    >
      {{ title }}
    </div>
  </div>
</template>

<script setup lang="ts">
/** Props类型定义 */
interface IProps {
  /** 标题文本 */
  title?: string
  /** 是否显示省略号 */
  ellipsis?: boolean
  /** 最大宽度（单位：px） */
  maxWidth?: number
}

const props = withDefaults(defineProps<IProps>(), {
  title: '',
  ellipsis: false,
  maxWidth: 180,
})
</script>

<style lang="scss" scoped>
.title-with-caidai {
  position: relative;
  display: inline-block;

  &::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 100%;
    height: 13px;
    background: url('@/assets/img/oneCaseReport/caidai.png') no-repeat center
      center;
    background-size: 100% 100%;
    opacity: 0.7;
  }
}

.title-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
