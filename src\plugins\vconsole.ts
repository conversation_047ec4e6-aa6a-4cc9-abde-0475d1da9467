let info: any = null
export function initVConsole() {
  const VITE_APP_ENV = import.meta.env.VITE_APP_ENV
  // 非pc环境并且非正式环境开启console
  const openConsoleFlag =
    !/Win32|Win64|Mac/i.test(navigator.platform) && VITE_APP_ENV != 'production'
  if (!openConsoleFlag) logBuildTime(VITE_APP_ENV)
  setTimeout(() => {
    if (openConsoleFlag) {
      $g.tool
        .loadJS('https://frontend-cdn.qimingdaren.com/cdn/eruda.js')
        .then((res) => {
          eruda.init({
            defaults: {
              displaySize: 88,
              transparency: 0.9,
              theme: 'Monokai Pro',
            },
          })
          info = eruda.get('info')
          logBuildTime(VITE_APP_ENV)
        })
    }
  }, 1500)
}

//更新开发者工具location地址
export function updateInfo() {
  info?.add('Location', window.location.href)
}

function logBuildTime(VITE_APP_ENV) {
  // 测试环境不打印
  if (VITE_APP_ENV == 'development') return
  // 最后打包时间
  console.log(
    '%c ' +
      `打包时间` +
      ' %c ' +
      $g.dayjs(__APP_VERSION__).format('YYYY-MM-DD HH:mm') +
      ' ' +
      '%c',
    'background:#35495e ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff',
    'background:#41b883 ; padding: 1px; border-radius: 0 3px 3px 0;  color: #fff',
    'background:transparent',
  )
  setTimeout(() => {
    console.log(
      '%c ' +
        `inNum` +
        ' %c ' +
        JSON.parse(localStorage.getItem('user') as string)?.userInfo
          ?.accountName +
        ' ' +
        '%c',
      'background:#35495e ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff',
      'background:#ff9f03 ; padding: 1px; border-radius: 0 3px 3px 0;  color: #fff',
      'background:transparent',
    )
  }, 1500)
}
