---
applyTo: "**"
description: "这是vue3项目开发规范，如果当前页面是.vue类型文件，则需要读取规则"
---

# Vue3 项目开发规范

## 🎯 核心原则

本规范旨在确保 Vue3 项目的代码质量、可维护性和团队协作效率。所有开发必须严格遵循以下规范。

## 📏 代码结构规范

### 文件行数限制

- **单文件行数上限**: 每个文件代码行数不得超过 **500 行**
- **强制拆分原则**: 超过 500 行考虑进行文件拆分重构
- **拆分方式**: 组件拆分到 components、业务逻辑提取到 composables、类型独立文件
- **通用逻辑优先级**: 滚动、拖拽、动画等通用交互逻辑优先抽取为 composables,不需要考虑行数限制

## 📦 工具库使用规范

### Lodash 使用规则

- **全局访问方式**: 项目中通过 `$g._` 访问 lodash 所有方法
- **使用示例**: `$g._.cloneDeep()`, `$g._.debounce()`, `$g._.merge()` 等
- **优先级原则**: 开发前优先检查 lodash 是否已有相应方法
- **避免重复**: 禁止重复实现 lodash 已有的工具函数
- **性能考虑**: 利用 lodash 经过优化的实现，避免自造轮子

### Day.js 时间处理规则

- **全局访问方式**: 项目中通过 `$g.dayjs` 处理所有时间相关操作
- **使用示例**:

  ```typescript
  // 格式化时间
  const formatted = $g.dayjs().format("YYYY-MM-DD HH:mm:ss");
  ```

- **优先原则**: 所有时间处理优先使用 `$g.dayjs`，避免使用原生 Date 对象

### 工具库优先级策略

- **第一优先级**: lodash（通用工具函数）
- **第二优先级**: VueUse（Vue 生态专用工具）
- **第三优先级**: 自定义实现（前两者都无法满足时）
- **选择原则**:
  1. 优先检查 lodash 是否有对应方法
  2. 如果是 Vue 相关功能，检查 VueUse 是否提供
  3. 最后考虑自定义实现

### AutoImport 配置规范

- **禁用原则**: 项目中已手动引入的库，应禁用自动引入功能
- **配置示例**:

  ```typescript
  AutoImport({
    imports: ["vue", "pinia", "vue-router", "@vueuse/core"],
  });
  ```

- **注意事项**: 希望这些可以不自动引入，项目中已经引入了

## 🔧 Vue3 语法规范

### 组件语法

- **语法糖**: 强制使用 `<script setup>` 语法
- **优势**: 更简洁的语法、更好的 TypeScript 支持、更优的性能

### 响应式数据

- **响应式定义**: 优先使用 `$ref` 语法糖定义响应式数据
- **语法示例**: `let count = $ref(0)` 而不是 `const count = ref(0)`
- **访问方式**: 直接使用变量名，无需 `.value`

### 组件参数

- **Props 定义**: 使用 `withDefaults(defineProps<IProps>(), {})` 模式
- **标准格式**:
  ```typescript
  let props = withDefaults(defineProps<IProps>(), {
    showLoading: false,
  });
  ```
- **类型约束**: 必须定义 TypeScript 接口 `IProps`
- **默认值**: 在 `withDefaults` 第二个参数中设置

## 📝 TypeScript 规范

### 类型定义

- **中文注释**: 所有 TypeScript 类型定义必须包含中文注释
- **注释格式**:
  ```typescript
  interface IProps {
    /** 是否显示加载状态 */
    showLoading?: boolean;
    /** 用户信息数据 */
    userInfo: UserInfo;
  }
  ```

## 🎨 样式规范

### TailwindCSS 使用规范

- **优先级**: 优先使用 TailwindCSS 进行样式编写
- **类名数量限制**: 当 TailwindCSS 类名超过 4 个时，使用 `@apply` 提取到 CSS 类中
- **自定义单位**: 优先使用自定义单位，如 `h-[10px]`、`w-[200px]`
- **自定义颜色**: 优先使用自定义颜色值，如 `bg-[#000]`、`text-[#ff6b6b]`

### 颜色值使用约束（强制规范）

- **禁止预设数值**: 严禁使用 TailwindCSS 预设颜色数值写法
  - ❌ 禁用：`bg-gray-200`、`text-blue-500`、`border-red-300` 等
- **允许主题颜色**: 可以使用项目配置的主题颜色系统
  - ✅ 允许：`bg-theme-primary`、`text-theme-success`、`border-theme-warning` 等
  - 支持颜色：`theme-primary`、`theme-success`、`theme-warning`、`theme-danger`、`theme-error`、`theme-info`
- **推荐自定义值**: 优先使用具体的颜色值
  - ✅ 推荐：`bg-[#e5e7eb]`、`text-[#3b82f6]`、`border-[#fca5a5]` 等
- **约束目的**: 确保颜色值明确可控，避免依赖预设色彩系统，保持设计一致性

### TailwindCSS 代码示例

```vue
<!-- 少于5个类名，使用主题颜色和自定义值 -->
<div class="flex items-center justify-center bg-theme-primary">
<div class="flex items-center justify-center bg-[#f5f5f5]">

<!-- 超过4个类名时使用@apply -->
<div class="custom-card">

<style scoped>
.custom-card {
  @apply flex items-center justify-center bg-[#f5f5f5] p-[16px] rounded-[8px] shadow-lg;
}
</style>
```

### Spacing 系统规范

- **单位优先级**: 优先使用 px 单位
- **支持范围**: spacing 系统支持 1-1000px
- **语法简化**: 1000px 以内可以不加 `[]` 括号
- **使用示例**:

  ```vue
  <!-- 1000px以内，直接使用 -->
  <div class="w-100px h-200px p-16px m-24px">

  <!-- 超过1000px，需要使用[]括号 -->
  <div class="w-[1200px] h-[1500px]">
  ```

### SCSS Deep 语法规范

- **深度选择器**: 使用嵌套式 `:deep()` 语法
- **标准格式**:

  ```scss
  :deep() {
    .class {
      // 样式代码
    }

    .another-class {
      color: red;
      font-size: 14px;
    }
  }
  ```

- **应用场景**: 修改子组件样式、第三方组件样式穿透

## 🎯 图标使用规范

### unplugin-icons 图标系统

- **条件判断**: 如果安装了 `vite-plugin-svg-icons` 插件
- **标签格式**: `<svg-图标前缀-图标名称>`
- **使用示例**:
  ```vue
  <svg-ri-account-box-line class="text-40px"></svg-ri-account-box-line>
  ```
- **样式控制**: 通过 TailwindCSS 类名控制大小和颜色

### g-icon 组件

- **适用场景**: 未安装 `vite-plugin-svg-icons` 时使用
- **支持图标库**: remixicon
- **组件属性**:
  - `name`: 图标名称
  - `size`: 图标大小
  - `color`: 图标颜色
- **使用示例**:
  ```vue
  <g-icon name="ri-arrow-down-line" size="18" color="" />
  ```

## ⚡ 函数定义规范

### 顶层函数

- **定义方式**: 使用声明式函数定义
- **标准格式**:

  ```typescript
  // ✅ 推荐：声明式函数
  function handleClick() {
    // 函数逻辑
  }
  ```

- **优势**: 函数提升、更好的调试体验、清晰的函数声明

# 📋 注释标准体系

## Better Comments 核心标记符

| 标记符  | 用途     | 颜色 | 使用原则             |
| ------- | -------- | ---- | -------------------- |
| `!`     | 重要警告 | 红色 | 标记关键业务逻辑     |
| `TODO:` | 待办事项 | 橙色 | 定期清理和处理       |
| `*`     | 功能说明 | 绿色 | 日常功能说明         |
| 无      | 普通注释 | 默认 | 避免过度使用警告标记 |

## 变量注释

```typescript
// 用户认证状态管理
let isAuthenticated = $ref(false);

// 支付金额，直接影响订单处理
let paymentAmount = $ref(0);

// 迁移到 user.composable.ts
let userList = $ref<IUser[]>([]);
```

## 函数注释

### 简单函数
```typescript
/** 切换显示状态 */
function toggleVisible(): void {}

/** ! 清空用户缓存数据 */
function clearUserCache(): void {}

/** TODO: 添加防抖处理 */
function handleClick(): void {}
```

### 复杂函数
```typescript
/**
 * * 处理表单提交逻辑
 * @param form 表单数据对象
 * @param options 提交配置选项
 * @returns 提交结果状态
 * TODO: 添加表单验证
 */
function handleSubmit(form: IFormData, options?: SubmitOptions): SubmitResult {}

/**
 * ! 用户权限验证（影响页面访问）
 * @param userId 用户ID
 * @param resource 资源路径
 * @returns 是否有权限访问
 */
function validatePermission(userId: string, resource: string): boolean {}
```

## 类注释

```typescript
/**
 * ! 用户数据管理核心类
 * TODO: 添加数据缓存机制
 */
class UserManager {}
```

## 模板注释

```html
<!-- * 用户信息展示区域 -->
<div class="user-info">
  <!-- ! 关键数据：用户头像和基本信息 -->
  <div class="user-profile">
    <img :src="user.avatar" alt="用户头像">
    <span>{{ user.name }}</span>
  </div>
  
  <!-- TODO: 添加用户状态指示器 -->
  <div class="user-actions">
    <button @click="editUser">编辑</button>
  </div>
</div>
```

## 行内注释

```typescript
function processData(data: any[]) {
  // * 数据预处理：去重和排序
  const uniqueData = [...new Set(data)];
  
  // ! 关键步骤：数据格式转换
  const transformedData = uniqueData.map(transformItem);
  
  // TODO: 优化过滤逻辑
  const filteredData = transformedData.filter(isValid);
  
  return filteredData;
}
```

## 🌐 网络请求规范

### Axios 封装规则

- **重要说明**: 项目中 axios 已进行封装处理
- **返回值特性**: 接口函数直接返回 `data` 数据，无需手动解构
- **使用方式**:

  ```typescript
  // ✅ 正确：直接使用返回值
  const userData = await getUserInfo(id);

  // ❌ 错误：不需要 .data .msg .code
  const userData = await getUserInfo(id).data;
  ```

- **封装优势**: 简化接口调用，统一错误处理
- **注意事项**: 所有接口函数都遵循此规范，返回的就是业务数据

## 📱 设备判断与消息提示

### 设备环境判断

- **PC 端判断**: 使用 `$g.isPC` 检测是否为 PC 端环境
- **Flutter 环境判断**: 使用 `$g.isFlutter` 检测是否为 Flutter 环境

### 消息提示方法

- **安装了 vant**: 使用 `$g.showToast('消息内容')` 显示提示
- **未安装 vant**: 使用 `$g.msg('消息内容', 类型)` 显示提示
  - 类型可选: `"success" | "error" | "info" | "warning"`

---