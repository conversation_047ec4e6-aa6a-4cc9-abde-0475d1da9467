<template>
  <div class="sync-train-bg w-full h-screen">
    <g-navbar customTitle="校本同步" class="wt-bar" />
    <div class="p-16px">
      <div v-if="showLoading" class="h-150px">
        <g-loading></g-loading>
      </div>
      <template v-else>
        <div class="w-full flex items-center justify-between mb-2px">
          <div class="text-[#333333] text-16px font-600">本周同步学习</div>
          <div
            class="flex items-center arrow-right van-haptics-feedback"
            @click="checkAll()"
          >
            <span class="text-[#666666] text-12px mr-4px">查看全部</span>
          </div>
        </div>
        <div class="flex items-center text-12px text-[#666666] mb-17px h-18px">
          <div class="mr-4px">本周时间范围</div>
          <div>
            {{ currentWeek?.beginTime.replace(/-/g, '/') }}-{{
              currentWeek?.endTime.replace(/-/g, '/')
            }}
          </div>
        </div>
        <template v-if="subjectList?.length">
          <div class="grid grid-cols-3 gap-8px mb-14px">
            <div
              v-for="item in subjectList"
              :key="item.sysSubjectId"
              class="w-full h-128px rounded-[8px] border border-solid border-white px-5px py-5px van-haptics-feedback"
              :class="item.className"
              @click="checkAll(item.sysSubjectId)"
            >
              <div
                class="h-25px leading-[25px] text-18px text-[#333] font-600 mb-11px text-center"
              >
                {{ item.sysSubjectName }}
              </div>
              <div
                class="w-full flex items-center justify-between mb-6px h-34px bg-white rounded-[4px] px-4px"
              >
                <div class="flex items-center">
                  <img
                    src="@/assets/img/syncTrain/ai.png"
                    alt="ai icon"
                    class="w-18px h-13px object-contain"
                  />
                  <div class="text-13px text-[#333] ml-2px translate-y-1px">
                    训练
                  </div>
                </div>
                <div class="flex items-center">
                  <template v-for="star in 2" :key="star">
                    <img
                      v-if="star <= item.finishedAITrainTask"
                      src="@/assets/img/syncTrain/star-light.png"
                      alt="star icon"
                      class="w-20px h-20px object-contain"
                    />
                    <img
                      v-else
                      src="@/assets/img/syncTrain/star-gray.png"
                      alt="star icon"
                      class="w-20px h-20px object-contain"
                    />
                  </template>
                </div>
              </div>
              <div
                class="w-full flex items-center justify-between h-34px bg-white rounded-[4px] px-4px"
              >
                <div class="text-13px text-[#333] translate-y-1px">作业</div>
                <div class="flex items-center">
                  <template v-for="star in 2" :key="star">
                    <img
                      v-if="star <= item.homeworkTaskFinishedNum"
                      src="@/assets/img/syncTrain/star-light.png"
                      alt="star icon"
                      class="w-20px h-20px object-contain"
                    />
                    <img
                      v-else
                      src="@/assets/img/syncTrain/star-gray.png"
                      alt="star icon"
                      class="w-20px h-20px object-contain"
                    />
                  </template>
                </div>
              </div>
            </div>
          </div>
          <div class="flex items-center">
            <img
              src="@/assets/img/syncTrain/star-light.png"
              alt="star icon"
              class="w-16px h-16px object-contain mr-2px"
            />
            <div class="text-12px text-[#999999] mr-23px">已完成</div>
            <img
              src="@/assets/img/syncTrain/star-gray.png"
              alt="star icon"
              class="w-16px h-16px object-contain mr-2px"
            />
            <div class="text-12px text-[#999999]">未完成</div>
          </div>
        </template>
        <g-empty v-else></g-empty>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getWeekList, getWeekTaskList } from '@/api/syncTrain'

const router = useRouter()
let showLoading = $ref(false)
let currentWeek = $ref<any>(null)
let subjectList = $ref<any[]>([])

function checkAll(sysSubjectId?) {
  if (!currentWeek) return
  router.push({
    name: 'ChapterList',
    query: {
      schoolPhaseId: currentWeek.schoolPhaseId,
      sysSubjectId,
      schoolPhaseWeekId: currentWeek.schoolPhaseWeekId,
      weekTitle: currentWeek.weekTitle,
      beginTime: currentWeek.beginTime,
      endTime: currentWeek.endTime,
    },
  })
}

async function getWeekListApi() {
  const data = await getWeekList()
  currentWeek = data?.find((v) => v.currentWeek) || null
}

async function getWeekTaskListApi() {
  if (!currentWeek) return
  const data = await getWeekTaskList({
    schoolPhaseWeekId: currentWeek.schoolPhaseWeekId,
  })
  subjectList =
    data?.map((v) => ({
      ...v,
      className: getClassName(v.sysSubjectName),
    })) || []
}

function getClassName(name) {
  if (name === '语文') return 'bg-[#FFF1E5]'
  if (name === '数学') return 'bg-[#EEFFF9]'
  if (name === '英语') return 'bg-[#E0F0FF]'
  if (name === '政治') return 'bg-[#FCF0CA]'
  if (name === '历史') return 'bg-[#FDF1E8]'
  if (name === '地理') return 'bg-[#F1FCE0]'
  if (name === '物理') return 'bg-[#EBEEFD]'
  if (name === '化学') return 'bg-[#FDF1F8]'
  return 'bg-[#E8F9F9]'
}

onBeforeMount(async () => {
  try {
    showLoading = true
    await getWeekListApi()
    await getWeekTaskListApi()
    showLoading = false
  } catch (e) {
    showLoading = false
    console.error(e)
  }
})
</script>

<style lang="scss" scoped>
.sync-train-bg {
  background:
    url(@/assets/img/syncTrain/sync-train-bg.png) top center / 100% auto
      no-repeat,
    linear-gradient(145deg, #f1f6ff 0%, rgba(244, 247, 254, 0) 100%);
}
.wt-bar {
  :deep() {
    #myBar {
      background: transparent !important;
    }
  }
}
.arrow-right {
  &::after {
    display: inline-block;
    content: '';
    width: 12px;
    height: 12px;
    background: url(@/assets/img/syncTrain/arrow-right.png) top center / contain
      no-repeat;
  }
}
</style>
