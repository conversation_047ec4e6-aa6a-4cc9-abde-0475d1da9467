<!-- TreeNode.vue -->
<template>
  <div class="flex items-center">
    <div class="pb-10px flex items-center flex-1">
      <div class="pl-5px" @click="toggleNode">
        <g-icon
          v-if="node.children?.length"
          name="play"
          size="14"
          color="#666"
          class="duration-300"
          :class="{ 'rotate-45': node.isOpen }"
        />
      </div>
      <div
        v-if="isAllCheck"
        class="flex items-center w-[100%] px-4px py-4px rounded-[4px]"
        :class="node.isChecked == true ? 'text-[#3F8CFF] bg-[#EBEFFD]' : ''"
        @click="checkNode"
      >
        <span
          :class="{ '!pl-23px': !node.children?.length }"
          class="pl-6px #333333 break-all"
          >{{ node?.meta?.title }}</span
        >
      </div>
      <div
        v-else
        class="flex items-center w-[100%] px-4px py-4px rounded-[4px]"
        :class="
          node.isChecked == true && !node.children?.length
            ? 'text-[#3F8CFF] bg-[#EBEFFD]'
            : ''
        "
        @click="checkNode"
      >
        <span
          :class="{ '!pl-23px': !node.children?.length }"
          class="pl-6px #333333 break-all"
          >{{ node?.meta?.title }}</span
        >
      </div>
    </div>
  </div>
  <ul v-show="isOpen && node.children?.length" class="px-10px">
    <div v-for="child in node.children" :key="child.name">
      <Tree
        v-if="!$g.tool.isTrue(child.meta.treeHide)"
        :node="child"
        v-model:is-open="child.isOpen"
        :tree-data="treeData"
        :expandAll="props.expandAll"
        @change="getNodeId"
      />
    </div>
  </ul>
</template>

<script setup lang="ts">
const props = defineProps({
  node: {
    type: Object,
    required: true,
  },
  isOpen: {
    type: Boolean,
    default: false,
  },
  isChecked: {
    type: Boolean,
    default: false,
  },
  treeData: {
    type: Array,
    default: () => [],
  },
  isAllCheck: {
    type: Boolean,
    default: true,
  },
  expandAll: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(['update:isOpen', 'change'])

if (props.expandAll) {
  emit('update:isOpen', true)
}

const toggleNode = () => {
  if (!props.isOpen) {
    closeAllChildren(props.node)
  }
  emit('update:isOpen', !props.isOpen)
}

const closeAllChildren = (node) => {
  if (node.children) {
    node.children.forEach((child) => {
      closeAllChildren(child)
      child.isOpen = false
    })
  }
}

const checkNode = () => {
  if (props.isAllCheck) {
    updateCheckedStatus(props.treeData)
    emit('change', props.node.name, props.node)
  } else {
    if (!props.node.children?.length) {
      updateCheckedStatus(props.treeData)
      emit('change', props.node.name, props.node)
    }
  }
}

function updateCheckedStatus(nodes) {
  for (let i = 0; i < nodes.length; i++) {
    const node = nodes[i]
    if (node.name === props.node.name) {
      node.isChecked = true
    } else {
      node.isChecked = false
    }

    if (node.children && node.children.length > 0) {
      updateCheckedStatus(node.children) // 递归调用更新子节点
    }
  }
}

function getNodeId(name, node) {
  emit('change', name, node)
}
</script>

<style lang="scss" scoped></style>
