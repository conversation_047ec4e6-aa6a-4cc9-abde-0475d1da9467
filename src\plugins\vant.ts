// Toast
import {
  Toast,
  showToast,
  showLoadingToast,
  showSuccessToast,
  showFailToast,
  closeToast,
} from 'vant'
import 'vant/es/toast/style'

// Dialog
import { Dialog, showDialog, showConfirmDialog } from 'vant'
import 'vant/es/dialog/style'

// Notify
import { Notify, showNotify, closeNotify } from 'vant'
import 'vant/es/notify/style'

// Lazyload
import { Lazyload } from 'vant'

const vant = {
  vantComponents: {
    Toast,
    Dialog,
    Notify,
    Lazyload,
  },
  vantFunction: {
    showToast,
    showLoadingToast,
    showSuccessToast,
    showFailToast,
    closeToast,
    showDialog,
    showConfirmDialog,
    showNotify,
    closeNotify,
  },
}

export default vant
