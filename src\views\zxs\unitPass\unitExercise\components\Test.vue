<template>
  <div ref="myContainer">
    <g-navbar
      ref="navBarRef"
      :customTitle="type == 1 ? '单元诊断' : '单元测评'"
    >
      <div v-if="curQuestion">
        <div
          class="flex-cc h-30px font-600 text-22px text-[#217DFB]"
          style="
            background: linear-gradient(
              270deg,
              #f4f9ff 0%,
              #d6e8fc 50%,
              #f3f8ff 100%
            );
          "
        >
          <span> {{ minute >= 10 ? minute : '0' + minute }}:</span>
          <span> {{ second >= 10 ? second : '0' + second }}</span>
        </div>
        <div class="py-17px bg-white pl-16px pr-14px flex-cc">
          <van-progress
            :percentage="percentage"
            stroke-width="12px"
            class="flex-1"
            :showPivot="false"
            style="background: #f3f3f3"
          />
          <div class="flex-shrink-0 ml-16px text-14px">
            <span class="text-[#333333]">{{ hasSelectedNum }}/</span>
            <span class="text-[#999999]">{{ questionList.length }}</span>
          </div>
        </div>
      </div>
    </g-navbar>
    <g-loading v-if="loading"></g-loading>
    <div
      v-else-if="curQuestion"
      class="px-16px pt-14px"
      style="
        background: linear-gradient(
          180deg,
          #e3eeff 0%,
          #ffffff 4%,
          #ffffff 100%
        );
      "
    >
      <QuestionItem
        :question-item="curQuestion"
        :isLastQuestion="curIndex == questionList.length - 1"
        :show-to-correct="false"
        :correction-source="2"
        @update-checked="handleUpdateChecked"
        @next-question="handleNextQuestion"
        @corrected-and-next="handleErrorCorrected"
      >
        <template #bottomAction>
          <AnswerCard
            :question-list="questionList"
            :curIndex="curIndex"
            @change-question="(num) => (curIndex = num)"
            @submit="submitReport"
          ></AnswerCard>
        </template>
      </QuestionItem>
    </div>

    <g-empty v-else></g-empty>
  </div>
</template>
<script setup lang="ts">
import QuestionItem from '@/views/zxs/components/QuestionItem/Underline/QuestionItem.vue'
import AnswerCard from './AnswerCard.vue'
import {
  getQuestionData,
  submitQuestion,
  submitPaper,
  addNote,
} from '@/api/unitPass'
import {
  getIsJump,
  getIsCorrect,
  getIsCorrectSub,
  getSubAnswer,
  getSubIsDone,
} from '@/views/zxs/components/QuestionItem/submit'
import { useUnitStore } from '@/stores/modules/unitPass'
const { active } = storeToRefs(useUnitStore())
const props = defineProps({
  type: {
    type: [Number, String],
    default: 1,
  },
  sysTextbooksCatalogId: {
    type: [Number, String],
    required: true,
  },
})

let curIndex = $ref(-1)
let questionList = $ref<any>([])
let curQuestion = $ref<any>(null)
const navBarRef = $ref<any>(null)
let minute = $ref(0)
let second = $ref(0)
let loading = $ref(true)
let timer = $ref<any>()
const myContainer = $ref<any>(null)
function interval() {
  timer = setTimeout(() => {
    second = second + 1
    if (second == 60) {
      second = 0
      minute += 1
    }
    interval()
  }, 1000)
}

const hasSelectedNum = $computed(
  () => questionList.filter((h) => h.isSelected).length,
)

const percentage = $computed(() =>
  $g
    .math(hasSelectedNum)
    .divide(questionList.length)
    .multiply(100)
    .toFixed(0)
    .value(),
)
watch(
  () => curIndex,
  async () => {
    if (!questionList.length || curIndex >= questionList.length) {
      curQuestion = null
      return
    }
    curQuestion = questionList[curIndex]
    await nextTick()
    myContainer?.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    })
    $g.tool.renderMathjax()
  },
)

// 题目是否是选择题
function ifChoice(item) {
  return [1, 2, 3].includes(item.jk_new_question_type_id)
}

function ifSelected(item) {
  if (ifChoice(item)) {
    return item.answers.length || item.is_jump === 2
  }
  return item.is_jump === 2 || [1, 2].includes(item.is_correct)
}
async function getQuestion() {
  loading = true
  let res = await getQuestionData({
    type: props.type,
    sysTextbooksCatalogId: props.sysTextbooksCatalogId,
  })
  if (!res || !res.length) {
    loading = false
    return
  }
  res.forEach((item, index) => {
    item.index = index + 1
    item.answers = []
    item.is_jump = 1
    item.sub_question?.forEach((item0, i) => {
      item0.is_jump = item0.is_jump ?? 1
      item0.answers =
        [1, 2, 3].includes(item0.jk_new_question_type_id) &&
        item0.student_question_answer &&
        !['我不会', '线下作答'].includes(item0.student_question_answer)
          ? item0.student_question_answer.split(',')
          : []
      item0.is_correct = item0.is_jump == 2 ? null : item0.is_correct
      item0.hasChecked =
        item0.answers.length > 0 || !!item0.student_question_answer
      item0.options =
        item0.jk_new_question_type_id == 3
          ? [
              {
                id: 1,
                name: '√',
                content: null,
                selectId: item0.paper_sub_question_id + '1',
              },
              {
                id: 2,
                name: '×',
                content: null,
                selectId: item0.paper_sub_question_id + '1',
              },
            ]
          : Object.keys(item0)
              .filter(
                (key) => key.includes('option_') && key !== 'option_numbers',
              )
              .map((realKey) => {
                return {
                  name: realKey.replace('option_', '').toUpperCase(),
                  content: item0[realKey],
                }
              })
              .filter((v) => v.content)
    })

    item.isSelected = item.sub_question?.every((subItem) => ifSelected(subItem))
  })
  questionList = res || []
  curIndex = 0
  await nextTick()
  navBarRef?.changeUpdateKey()
  loading = false
  interval()
}

onBeforeMount(() => {
  getQuestion()
})

onBeforeUnmount(() => {
  clearTimeout(timer)
})

function getRealAnswer(item) {
  return item.sub_question.reduce((res, h) => {
    if ([1, 2, 3].includes(h.jk_new_question_type_id)) {
      res += h.structure_number + h.question_answer
    } else {
      res += h.structure_number + '线下作答'
    }
    return res
  }, '')
}

function getAnswer(item) {
  return item.sub_question.reduce((res, h) => {
    if ([1, 2, 3].includes(h.jk_new_question_type_id)) {
      res +=
        h.structure_number +
        ((h.answers.length && h.answers.join?.()) || '我不会')
    } else {
      res +=
        h.is_jump === 2
          ? h.structure_number + '我不会'
          : h.structure_number + '线下作答'
    }
    return res
  }, '')
}
async function handleUpdateChecked(item) {
  let params = {
    sys_textbooks_catalog_id: props.sysTextbooksCatalogId,
    question_id: item.paper_question_id,
    question_answer: getRealAnswer(item),
    student_question_answer: getAnswer(item),
    type: props.type,
    is_jump: getIsJump(item.sub_question),
    is_correct: getIsCorrect(item.sub_question),
    sub_question: item.sub_question?.map((item0) => {
      let isDone = getSubIsDone(item0)
      return {
        paper_sub_question_id: item0.paper_sub_question_id,
        is_jump: isDone ? item0.is_jump : 2,
        is_correct: getIsCorrectSub(item0),
        question_answer: item0.question_answer,
        sub_question_id: item0.paper_sub_question_id,
        is_response: isDone ? 2 : 1,
        student_question_answer: getSubAnswer(item0),
      }
    }),
  }
  await submitQuestion(params)
  active.value = true
  savePhoto(item)
}

async function savePhoto(currentQues) {
  if (!currentQues.noteList || !currentQues.noteList.length) return
  let notes = [
    {
      questionId: currentQues.paper_question_id,
      noteUrls: currentQues.noteList.map((v) => v.noteUrl),
    },
  ]
  let params = {
    exerciseId: currentQues.chapter_exercises_id,
    type: 3,
    notes,
  }
  await addNote(params)
}

function handleNextQuestion() {
  if (curIndex == questionList.length - 1) {
    submitReport()
    return
  }
  curIndex++
}

const submitDisabled = $computed(() => {
  if (!questionList.length) return true
  return questionList.some((item) => !item.isSelected)
})

const router = useRouter()
const route = useRoute()
async function onSubmitClick() {
  submitPaper({
    chapter_exercises_id: questionList[0].chapter_exercises_id,
    time: minute * 60 + second,
  })
    .then(() => {
      active.value = true
      router.replace({
        name: 'UnitReport',
        query: {
          sysTextbooksCatalogId: props.sysTextbooksCatalogId,
          type: props.type,
          jk_new_chapter_name: route.query.jk_new_chapter_name,
        },
      })
    })
    .catch(async () => {
      await getQuestion()
    })
}
function submitReport() {
  if (submitDisabled) {
    $g.showConfirmDialog({
      title: '提示',
      message: '您尚未完成所有试题，交卷后未作答的试题视为不会，是否提前交卷？',
    })
      .then(() => {
        onSubmitClick()
      })
      .catch(() => {})
  } else {
    onSubmitClick()
  }
}

async function handleErrorCorrected(questionItem) {
  questionItem.sub_question?.forEach((h) => {
    if (h.isShow) h.isShow = false
    if (ifChoice(h)) {
      h.answers = h.question_answer.split('')
    } else {
      h.is_correct = 2
    }
    h.hasChecked = true
  })
  await handleUpdateChecked(questionItem)
  handleNextQuestion()
}
</script>
