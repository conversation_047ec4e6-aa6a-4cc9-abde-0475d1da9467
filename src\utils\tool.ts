import ClipboardJS from 'clipboard'
import { format } from 'timeago.js'
import config from '@/config/index'
/* mathjax */
const TypeSet = async function (elClass = 'g-mathjax') {
  if (!window.MathJax) {
    return
  }
  const node = document.getElementsByClassName(elClass)
  window.MathJax.startup.promise = window.MathJax.startup.promise
    .then(() => {
      return window.MathJax.typesetPromise(node)
    })
    .catch((err) => console.log('Typeset failed: ' + err.message))
  return window.MathJax.startup.promise
}

const tool = {
  /**
   * 描述  获取文件后缀名
   * @param {String} filename
   */
  getExt(filename: string) {
    if (typeof filename === 'string') {
      return filename.split('.').pop()?.toLowerCase()
    } else {
      throw new Error('filename must be a string type')
    }
  },

  /**
   * @description 将url请求参数转为json格式1
   * @param url
   */
  paramObj(qs, sep, eq, options) {
    sep = sep || '&'
    eq = eq || '='
    const obj = {}

    if (typeof qs !== 'string' || qs.length === 0) {
      return obj
    }
    if (qs.indexOf('?') != -1) {
      qs = qs.substr(qs.indexOf('?') + 1)
    }
    const regexp = /\+/g
    qs = qs.split(sep)

    let maxKeys = 1000
    if (options && typeof options.maxKeys === 'number') {
      maxKeys = options.maxKeys
    }

    let len = qs.length // maxKeys <= 0 means that we should not limit keys count
    if (maxKeys > 0 && len > maxKeys) {
      len = maxKeys
    }

    for (let i = 0; i < len; ++i) {
      const x = qs[i].replace(regexp, '%20')
      const idx = x.indexOf(eq)
      let kstr, vstr

      if (idx >= 0) {
        kstr = x.substr(0, idx)
        vstr = x.substr(idx + 1)
      } else {
        kstr = x
        vstr = ''
      }

      const k = decodeURIComponent(kstr)
      const v = decodeURIComponent(vstr)

      if (!Object.prototype.hasOwnProperty.call(obj, k)) {
        obj[k] = v
      } else if (Array.isArray(obj[k])) {
        obj[k].push(v)
      } else {
        obj[k] = [obj[k], v]
      }
    }

    return obj
  },

  /**
   * @description 获取随机id
   * @param length
   * @returns {string}
   */
  uuid(length = 32) {
    const num = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890'
    let str = ''
    for (let i = 0; i < length; i++) {
      str += num.charAt(Math.floor(Math.random() * num.length))
    }
    return str
  },

  /**
   * 判断是否为存在
   */
  isTrue(a: any) {
    if (a === 0) return false //检验空字符串
    if (a === '') return false //检验空字符串
    if (a === 'null') return false //检验字符串类型的null
    if (a === 'undefined') return false //检验字符串类型的 undefined
    if (!a && a !== 0 && a !== '') return false //检验 undefined 和 null
    if (Array?.prototype?.isPrototypeOf(a) && a.length === 0) return false //检验空数组
    if (Object?.prototype?.isPrototypeOf(a) && Object.keys(a).length === 0) {
      return false
    } //检验空对象
    return true
  },

  /**
   * 获取静态资源地址
   */
  getFileUrl(url: string) {
    return new URL(`../assets/img/${url}`, import.meta.url).href
  },

  /**
   * 获取静态资源地址
   */
  getOSSUrl(url: string) {
    return config.baseOssImg + url
  },

  /**
   * 判断精准类型
   */
  typeOf(obj) {
    const toString = Object.prototype.toString
    const map = {
      '[object Boolean]': 'boolean',
      '[object Number]': isNaN(obj) ? 'NaN' : 'number',
      '[object String]': 'string',
      '[object Function]': 'function',
      '[object Array]': 'array',
      '[object Date]': 'date',
      '[object RegExp]': 'regExp',
      '[object Undefined]': 'undefined',
      '[object Null]': 'null',
      '[object Object]': 'object',
    }
    return map[toString.call(obj)]
  },

  isAndroid() {
    return window.navigator.userAgent.toLowerCase().indexOf('android') > -1
  },

  isIOS() {
    return (
      [
        'iPad Simulator',
        'iPhone Simulator',
        'iPod Simulator',
        'iPad',
        'iPhone',
        'iPod',
      ].includes(navigator.platform) ||
      (navigator.userAgent.includes('Mac') && 'ontouchend' in document)
    )
  },

  isPC() {
    return /Win32|Win64|Mac/i.test(navigator.platform)
  },

  isPCTest() {
    return tool.isPC() && !(import.meta.env.VITE_APP_ENV == 'production')
  },

  /* 多久之前 */
  timeAgo(val) {
    const time = new Date(val?.replace(/-/g, '/')) //先将接收到的json格式的日期数据转换成可用的js对象日期
    return format(time, 'zh_CN') //转换成类似于几天前的格式
  },

  /* 更新mathjax数据 */
  renderMathjax(elClass?) {
    return new Promise((resolve) => {
      nextTick(() => {
        TypeSet(elClass).then(() => {
          resolve(true)
        })
      })
    })
  },

  /* 将参数放到url上 */
  paramsToUrl(query) {
    const params: any[] = []
    Object.entries(query).forEach(([key, val]) => {
      params.push(key + '=' + val)
    })
    const path = location.hash.split('?')[0] + '?' + params.join('&')
    history.replaceState(history.state, '', path)
  },

  /* 动态加载js */
  loadJS(url: string) {
    return new Promise(function (resolve, reject) {
      const script = document.createElement('script')
      script.type = 'text/javascript'
      script.src = url
      document.body.appendChild(script)
      script.onload = function () {
        resolve(`success: ${url}`)
      }
      script.onerror = function () {
        reject(Error(`${url} load error!`))
      }
    })
  },

  /* 资源类型 */
  resourceType(type) {
    const newType: any = tool.getExt(type)
    const imgType = ['jpg', 'png', 'jpeg', 'heic', 'heif', 'gif', 'webp']
    const videoType = ['mp4', 'mov', 'avi', 'rmvb', 'flv', 'wmv', 'mkv', 'm3u8']
    const audioType = ['mp3', 'ogg', 'wav', 'aac']
    const fileType = ['doc', 'docx', 'xls', 'xlsx', 'pdf', 'ppt', 'pptx']

    if (imgType.includes(newType)) {
      return 'img'
    } else if (videoType.includes(newType)) {
      return 'video'
    } else if (audioType.includes(newType)) {
      return 'audio'
    } else if (fileType.includes(newType)) {
      return 'file'
    }
  },

  /* 下载 */
  downloadFile(url, filename) {
    if (!url) return
    const link = document.createElement('a') //创建a标签
    link.style.display = 'none' //使其隐藏
    link.href = url //赋予文件下载地址
    link.setAttribute('download', filename) //设置下载属性 以及文件名
    document.body.appendChild(link) //a标签插至页面中
    link.click() //强制触发a标签事件
    document.body.removeChild(link)
  },

  /* 通过文件后缀获取对应文件类型缩略图 */
  getFileTypeIcon(suffix) {
    suffix = suffix.toLowerCase()
    const fileTypeIcon = {
      doc: ['doc', 'docx'],
      pdf: ['pdf'],
      xlsx: ['xls', 'xlsx'],
      txt: ['txt'],
      mp3: ['mp3', 'wav', 'wma', 'ape', 'aac'],
      mp4: ['mp4', 'avi', 'rmvb', 'flv', 'wmv', 'mkv', 'mov'],
      img: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg'],
      ppt: ['ppt', 'pptx'],
      zip: ['zip'],
      rar: ['rar'],
    }
    let name = ''
    Object.keys(fileTypeIcon).some((key) => {
      if (fileTypeIcon[key].includes(suffix)) {
        name = key
        return true
      }
    })
    const url = this.getFileUrl(`fileImg/${name.toUpperCase()}.png`)
    return url
  },

  /* 文字编码 */
  unicodeToZH(str) {
    str = str?.replace(/(\\u)(\w{1,4})/gi, function ($0) {
      return String.fromCharCode(
        parseInt(escape($0).replace(/(%5Cu)(\w{1,4})/g, '$2'), 16),
      )
    })
    str = str?.replace(/(&#x)(\w{1,4});/gi, function ($0) {
      return String.fromCharCode(
        parseInt(escape($0).replace(/(%26%23x)(\w{1,4})(%3B)/g, '$2'), 16),
      )
    })
    str = str?.replace(/(&#)(\d{1,6});/gi, function ($0) {
      return String.fromCharCode(
        parseInt(escape($0).replace(/(%26%23)(\d{1,6})(%3B)/g, '$2')),
      )
    })
    return str
  },

  /* 存储单位转换 */
  formatFileSize(fileSize) {
    if (fileSize < 1024) {
      return fileSize + 'B'
    } else if (fileSize < 1024 * 1024) {
      const temp = fileSize / 1024
      return temp.toFixed(0) + 'KB'
    } else if (fileSize < 1024 * 1024 * 1024) {
      const temp = fileSize / (1024 * 1024)
      return temp.toFixed(0) + 'MB'
    } else {
      const temp = fileSize / (1024 * 1024 * 1024)
      return temp.toFixed(2) + 'GB'
    }
  },

  /**
   * 点击复制
   */
  copyData(target, text, msg) {
    const clipboard = new ClipboardJS(target, {
      text() {
        return text
      },
    })
    clipboard.on('success', function () {
      $g.showToast('链接已复制，' + msg)
    })
  },

  /**
   * 长按函数
   */
  longPress(dom, durationInSeconds, callback) {
    let pressTimer

    // 监听 touchstart 事件
    dom?.addEventListener('touchstart', function (event) {
      // 清除计时器
      clearTimeout(pressTimer)

      // 设置计时器，在指定秒数后执行回调函数
      pressTimer = setTimeout(function () {
        if (typeof callback === 'function') {
          callback()
        }
      }, durationInSeconds * 1000)
    })

    // 监听 touchend 或 touchcancel 事件
    dom?.addEventListener('touchend', function (event) {
      // 清除计时器
      clearTimeout(pressTimer)
    })
  },

  /* 获取iframe web365完整地址,适配高清模式 */
  previewUrlHandle(url) {
    let web365Url = `//vip.ow365.cn/?i=28777&ssl=1`
    const suffix: any = this.getExt(url)
    const fileType = {
      doc: {
        suffix: ['doc', 'docx'],
        params: '&n=1',
      },
      pdf: {
        suffix: ['pdf'],
        params: '&n=7',
      },
    }
    for (const [key, value] of Object.entries(fileType)) {
      if (value.suffix.includes(suffix)) {
        web365Url += value.params
        break
      }
    }
    web365Url = web365Url + `&furl=${url}`
    return web365Url
  },

  /**
   * px换算适配后的px
   * @param {number} num - 设计稿中的px值
   * @param {number} [designWidth=375] - 设计稿的宽度，默认375px
   * @param {number} [fixedNum=2] - 保留的小数位数
   * @param {boolean} [needUnit=true] - 是否需要返回带单位的值，默认带px
   * @returns {string | number} - 适配后的px值，如果needUnit为true，返回带单位的字符串，否则返回数字
   */
  pxConversionAdaptedPx(num, needUnit = true, designWidth = 375, fixedNum = 2) {
    // 将窗口宽度限制为最大500px
    const maxWidth = 500
    const currentWidth = Math.min(window.innerWidth, maxWidth)

    const scaleFactor = currentWidth / designWidth
    const adaptedPx = num * scaleFactor

    return needUnit
      ? `${adaptedPx.toFixed(fixedNum)}px`
      : parseFloat(adaptedPx.toFixed(fixedNum))
  },

  //阿拉伯数字转汉字
  numberToChinese(num) {
    const chineseNumbers = [
      '零',
      '一',
      '二',
      '三',
      '四',
      '五',
      '六',
      '七',
      '八',
      '九',
    ]
    const unitNames = ['', '十', '百', '千']
    if (num === 0) {
      return chineseNumbers[0]
    }
    let result = ''
    let unitIndex = 0
    while (num > 0) {
      const digit = num % 10
      if (digit > 0 || result !== '') {
        result = chineseNumbers[digit] + unitNames[unitIndex] + result
      }
      unitIndex++
      num = Math.floor(num / 10)
    }
    return result
  },

  /**
   * 对比版本号之间的大小
   * @param {string} baseVersion - 基础版本号 必须是 "x.x.x" 格式
   * @param {string} targetVersion - 用于比较的版本号。 必须是 "x.x.x" 格式
   * @returns {string } 返回比较targetVersion相对于baseVersion的关系，大于 gt，小于 lt，等于 eq
   */
  compareVersion(baseVersion, targetVersion) {
    const isValidVersion = (version) => /^\d+\.\d+\.\d+$/.test(version)

    if (!isValidVersion(baseVersion) || !isValidVersion(targetVersion)) {
      console.error("Invalid version format. Must be 'x.x.x'")
      return
    }

    const v1 = baseVersion.split('.').map(Number)
    const v2 = targetVersion.split('.').map(Number)

    for (let i = 0; i < 3; i++) {
      if (v1[i] < v2[i]) return 'gt'
      if (v1[i] > v2[i]) return 'lt'
    }
    return 'eq'
  },
}

export default tool
