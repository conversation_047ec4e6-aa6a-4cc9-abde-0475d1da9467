<template>
  <div>
    <svg
      v-if="isIconfont"
      :style="{ color }"
      :class="['iconfont', name, 'iconfont', 'cst-icon-size']"
      aria-hidden="true"
    >
      <use :xlink:href="'#' + name" />
    </svg>
    <van-icon
      v-else
      :name="name"
      :size="size"
      :class="[name, 'cst-icon-size']"
      v-bind="$attrs"
      :color="color"
    />
  </div>
</template>

<script lang="ts" setup name="GIcon">
/**
 * 目前支持iconfont
 */

const props = defineProps({
  name: {
    type: String,
    default: '',
  },
  size: {
    type: String,
    default: '18',
  },
  color: {
    type: String,
  },
})

/* 判断图片是否iconfont,需要修改iconfont图标库前缀为icon- */
const isIconfont = computed(() => {
  return props.name.startsWith('icon-') || /^svg-/.test(props.name)
})
</script>

<style lang="scss" scoped>
.iconify {
  font-size: 20px;
}

.iconfont {
  display: inline-block;
  width: 1em;
  height: 1em;
  overflow: hidden;
  vertical-align: -0.2em;
  fill: currentColor;
  font-size: calc(v-bind(size) * 1px);
}

.cst-icon-size {
  font-size: calc(v-bind(size) * 1px) !important;
}
</style>
