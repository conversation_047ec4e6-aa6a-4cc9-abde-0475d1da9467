<template>
  <div>
    <van-popup
      v-model:show="showDialog"
      position="bottom"
      round
      teleport="#app"
      closeable
      :close-icon="$g.tool.getFileUrl('home/close.png')"
      class="myPop"
    >
      <div class="px-16px pb-32px">
        <div class="flex-cc font-500 text-16px py-11px">
          请设置目标成绩和选课组合
        </div>
        <div>
          <!-- 分数 -->
          <div class="reqiuredLabel mb-15px">目标成绩</div>
          <div class="flex items-center ml-8px">
            <van-field
              v-model="formObj.score"
              class="myInput w-77px"
              :border="false"
              type="digit"
              :min="300"
              :max="750"
            />
            <span class="text-[#999999]">分</span>
            <span class="text-[#ec3131] text-12px ml-10px"
              >请输入300分及以上分数</span
            >
          </div>

          <!-- 组合 -->
          <div class="reqiuredLabel mb-8px mt-20px">选课组合</div>
          <div class="ml-8px">
            <div class="text-[#999999] text-12px mb-8px">
              选择3个科目的组合，不同的组合对应不同的报考专业推荐，请认真填写
            </div>
            <div class="flex flex-wrap gap-x-[20px] gap-y-[12px] text-12px">
              <div
                v-for="item in courseList"
                :key="item.sysSubjectId"
                class="h-28px w-97px flex-cc bg-[#F4F4F4] br-[4px]"
                :class="{
                  activeItem: formObj.subjectIds.includes(item.sysSubjectId),
                }"
                @click="change(item)"
              >
                {{ item.sysSubjectName }}
              </div>
            </div>
          </div>

          <!-- 省份 -->
          <div class="reqiuredLabel mt-20px">所在省份</div>
          <!-- <van-popover
          :actions="provinceList"
          placement="top"
          :show-arrow="false"
          class="myProvince"
          @select="handleSelect"
        >
          <template #reference>
            <div
              class="w-120px h-36px bg-[#F4F4F4] br-[4px] flex items-center justify-between px-12px mt-15px ml-8px"
            >
              <span>{{ formObj.cityInfo.text }}</span>
              <img
                :src="$g.tool.getFileUrl('home/down.png')"
                alt=""
                class="w-12px h-12px"
              />
            </div>
          </template>
        </van-popover> -->
          <div
            class="w-120px h-36px bg-[#F4F4F4] br-[4px] flex items-center justify-between px-12px mt-15px ml-8px"
            @click="showPop = true"
          >
            <span>{{ formObj.cityInfo.text }}</span>
            <img
              :src="$g.tool.getFileUrl('home/down.png')"
              alt=""
              class="w-12px h-12px"
            />
          </div>

          <!-- bottom -->

          <div class="mt-25px flex-cc">
            <van-button
              class="h-40px flex-1 mr-12px br-[100px]"
              @click="showDialog = false"
              >取消</van-button
            >
            <van-button
              class="h-40px flex-1 br-[100px]"
              color="#3398F7"
              @click="submit"
              >提交</van-button
            >
          </div>
        </div>
      </div>
    </van-popup>
    <van-popup
      v-model:show="showPop"
      :close-on-popstate="true"
      round
      teleport="#app"
      duration="0"
    >
      <div>
        <van-picker
          v-show="showPop"
          :columns="provinceList"
          class="w-250px"
          title="所在省份"
          @cancel="showPop = false"
          @confirm="handleSelect"
        />
      </div>
    </van-popup>
  </div>
</template>
<script setup lang="ts">
import {
  getProvinceList,
  getSubjectList,
  submitTargetGrade,
} from '@/api/aiStudentPartner'
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  info: {
    type: Object,
    default: () => ({}),
  },
})
const emit = defineEmits(['update:show', 'hasSet'])
const showDialog = useVModel(props, 'show', emit)
let showPop = $ref(false)
let courseList = $ref<any>([])
let formObj = $ref<any>({
  score: null,
  cityInfo: {},
  subjectIds: [],
})
let provinceList = $ref<any>([])

async function getCourse() {
  let res = await getSubjectList()
  courseList = res || []
}

async function getProvince() {
  let res = await getProvinceList()
  if (!res) {
    provinceList = []
    return
  }
  provinceList =
    res.cityList?.reduce((arr, next) => {
      next.areaList.forEach((v) => {
        arr.push({
          text: v.name,
          value: v.areaCode,
          areaName: v.areaName,
        })
      })
      return arr
    }, []) || []
  if (res.ipCity) {
    formObj.cityInfo = provinceList.find((v) => v.value == res.ipCity)
  }
}

watch(showDialog, (val) => {
  if (val) {
    formObj.score = props.info.score ? props.info.score : 750
    formObj.cityInfo =
      provinceList.find((v) => v.value == props.info.cityCode) || {}
    formObj.subjectIds =
      props.info.subjectList?.map((v) => v.sys_subject_id) || []
  }
})
onBeforeMount(() => {
  getCourse()
  getProvince()
})

function change(item) {
  let index = formObj.subjectIds.indexOf(item.sysSubjectId)
  if (index > -1) {
    formObj.subjectIds.splice(index, 1)
  } else {
    if (formObj.subjectIds.length === 3) {
      $g.showToast('最多选择3门科目')
      return
    }
    formObj.subjectIds.push(item.sysSubjectId)
  }
}
function handleSelect(action) {
  formObj.cityInfo = action.selectedOptions[0]
  showPop = false
}

async function submit() {
  if (!formObj.score || formObj.score > 750 || formObj.score < 300) {
    $g.showToast('请输入300-750之间的整数')
    return
  }
  if (formObj.subjectIds?.length !== 3) {
    $g.showToast('请选择3个学科')
    return
  }
  if (!formObj.cityInfo.value) {
    $g.showToast('请选择地区')
    return
  }
  try {
    let { score, cityInfo, subjectIds } = formObj
    await submitTargetGrade({
      score,
      sys_subject_ids: subjectIds,
      city_code: cityInfo.value,
      city_name: cityInfo.areaName,
    })
    showDialog.value = false
    emit('hasSet')
    $g.bus.emit('getUserInfoApi')
  } catch (e) {
    console.log(e)
  }
}
</script>
<style lang="scss" scoped>
:deep() {
  .van-icon__image {
    width: 16px;
    height: 16px;
  }
}

.myInput {
  border: none;
  margin-right: 8px;
  border-radius: 4px;
  background: #f4f4f4;
  padding: 6px 12px;
}
.reqiuredLabel {
  font-weight: 500;
  font-size: 14px;
  height: 20px;
  line-height: 20px;
  &::before {
    content: '*';
    color: #ff4646;
    margin-top: 4px;
    margin-right: 4px;
  }
}
.myProvince {
  max-height: 30%;
  overflow: auto;
}
.activeItem {
  background: #3398f7 !important;
  color: #ffffff;
  font-weight: 500;
}
</style>
