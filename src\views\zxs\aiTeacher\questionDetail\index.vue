<template>
  <div>
    <g-navbar customTitle="查看原题" customRight>
      <template #csr>
        <div
          class="h-28px bg-[#FFC613] leading-[28px] text-center text-14px br-[16px] flex-cc px-8px"
          @click="showAnswer = !showAnswer"
        >
          <img
            :src="
              showAnswer
                ? $g.tool.getFileUrl('aiTeacher/eye.png')
                : $g.tool.getFileUrl('aiTeacher/close-eye.png')
            "
            class="w-18px h-18px mr-4px"
          />
          <span> {{ showAnswer ? '隐藏' : '查看' }}答案</span>
        </div>
      </template>
    </g-navbar>
    <div
      :style="{
        minHeight: `calc(100vh - ${Math.ceil($g.navBarTotalHeight)}px)`,
      }"
      class="py-12px px-10px"
    >
      <g-loading v-if="fetchLoading"></g-loading>
      <div
        v-else
        class="bg-white rounded-[8px] px-10px pt-18px pb-16px w-full selsect-none"
      >
        <!-- 大题题目 -->
        <g-mathjax
          class="text-16px text-[#141414] font-500 pl-4px overflow-x-auto overflow-y-hidden"
          :text="questionDetail.questionTitle"
        />

        <!-- 子题部分 -->
        <div
          v-for="item in questionDetail.subQuestions"
          :key="item.subQuestionId"
          class="mt-10px"
        >
          <div class="flex items-start text-16px pl-4px font-500">
            <div
              v-if="questionDetail.subQuestions.length > 1"
              class="mr-6px leading-[22px]"
            >
              {{ item.structureNumber }}
            </div>
            <g-mathjax
              :text="item.subQuestionTitle"
              class="overflow-x-auto overflow-y-hidden"
            />

            <div
              v-if="
                questionDetail.subQuestion?.length > 1 &&
                item.subQuestionParseList?.length > 1
              "
              class="bg-sub-parse w-[62px] h-[22px] text-right text-[#fff] pr-9px leading-[22px] text-[14px] font-600 ml-8px flex-shrink-0"
            >
              {{ item.subQuestionParseList?.length }}解
            </div>
          </div>
          <!-- 选择题 -->
          <template v-if="[1, 2, 3].includes(item.subQuestionType)">
            <div
              v-for="option in item.options"
              :key="option.name"
              class="flex mt-13px pl-4px font-400"
            >
              <div class="w-20px mr-6px">{{ option.name }}.</div>
              <div class="flex-1">
                <g-mathjax
                  :text="option.title"
                  class="overflow-x-auto overflow-y-hidden"
                ></g-mathjax>
              </div>
            </div>
          </template>
          <div v-show="showAnswer">
            <!-- 答案 -->
            <div
              class="rounded-[8px] text-[#888] border p-10px my-10px border-[#ededed] video-hidden"
            >
              <div class="font-500">【答案】</div>
              <div class="mt-10px">
                <g-mathjax
                  :text="item.subQuestionAnswer"
                  class="overflow-x-auto overflow-y-hidden"
                />
              </div>
            </div>

            <!-- 解析 -->
            <div
              class="rounded-[8px] text-[#888] border p-10px border-[#ededed] overflow-hidden video-hidden"
            >
              <div class="font-500">【解析】</div>
              <div
                v-for="(val, num) in item.subQuestionParseList"
                :key="val.subQuestionParseId"
                class="my-5px"
              >
                <div v-if="item.subQuestionParseList.length > 1">
                  解法{{ $g.tool.numberToChinese(num + 1) }}
                </div>
                <div>
                  <g-mathjax
                    :text="val.content"
                    class="overflow-x-auto overflow-y-hidden ai-math"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="QuestionDetail">
import { getQuestionDetail } from '@/api/aiTeacher'

const route = useRoute()
//  获取题目详情loading
let fetchLoading = $ref(true)
// 是否展开答案和解析
let showAnswer = $ref(false)
let questionDetail: any = $ref({})

const isTutorial = $computed(() => {
  return Number(route.query.errorType) === 2
})

// 处理题目数据
function processQuestion(question) {
  let questionDetailCopy = $g._.cloneDeep(question)
  questionDetailCopy.subQuestions.forEach((v) => {
    if ([1, 2].includes(v.subQuestionType)) {
      v.options = Object.keys(v)
        .filter(
          (key) =>
            key.includes('option') && v[key] && !key.includes('optionNumber'),
        )
        .map((realKey) => {
          return {
            name: realKey.charAt(realKey.length - 1).toLocaleUpperCase(),
            title: v[realKey],
          }
        })
    } else if (v.subQuestionType == 3) {
      v.options = [
        {
          id: 1,
          name: '√',
          title: null,
        },
        {
          id: 2,
          name: '×',
          title: null,
        },
      ]
    }
  })
  questionDetailCopy.init = true
  return questionDetailCopy
}

// 获取题目详情
async function fetchQuestionDetail() {
  try {
    fetchLoading = true
    let res = await getQuestionDetail({
      questionId: route.query.questionId,
      questionIdFrom: isTutorial ? 7 : null,
    })
    questionDetail = processQuestion(res)
    await nextTick()
    $g.tool.renderMathjax()
  } catch (err) {
    console.log('获取题目数据出错', err)
  } finally {
    fetchLoading = false
  }
}

onMounted(() => {
  fetchQuestionDetail()
})
</script>

<style lang="scss" scoped>
.ai-math {
  white-space: pre-wrap;
  & > p {
    display: inline-block;
    margin-bottom: 10px;
  }
}
:deep() {
  .mathjax-qmdr-yf,
  .mj-qmdr-yf {
    max-height: 40px;
  }
  .video-hidden {
    video {
      display: none;
    }
  }
  // mjx-math {
  //   white-space: pre-wrap;
  //   line-height: 10px;
  // }
}
</style>
