import { defineStore } from 'pinia'
import config from '@/config/index'
import { feedback } from '@/api/aiTeacher'
import { fetchEventSource } from '@cjh0/fetch-event-source'
import { useUserStore } from '@/stores/modules/user'
const { baseURL } = config
const userStore = useUserStore()
export const useSpeakerStore = defineStore('speaker', {
  state: () => ({
    audio: null as any, //audio实例
    urlList: [] as any, //音频列表
    isPlay: 'pause', //播放状态 loading/start/pause
    currentIndex: 0, //当前播放index
    type: null as any, // 播放类型
    question: {
      data: {}, // 题目数据
      params: {}, // 请求参数
      ids: [], //多个音频id
    } as any,
    showSubTitle: false, //显示字幕
    subtitle: '', //字幕
    controller: null as any, //控制器
    accountId: '',
  }),
  getters: {
    getId(state) {
      return state.question.data.id
    },
    getIdByType(state) {
      return state.question.data?.subQuestionParseId || state.question.data.id // 返回对应的id，如果没有匹配到type，则返回null
    },
    getQuestionTtsId(state) {
      return state.urlList?.[0]?.questionTtsId
    },
  },
  actions: {
    /**
     * Description
     * @param {any} item  题目数据
     * @param {any} type  类型 1: 阅读题干 2: 阅读子题题干 3: 阅读答案 4: 阅读解析 5: 其他或者跟题目无关类型
     * @returns {any}
     */
    async fetchAudioStream(sourceId, sourceType) {
      try {
        const url = `${baseURL}/v3/student/aiChat/question/tts/stream`
        const headers = {
          'Content-Type': 'application/json;charset=UTF-8',
          Accept: 'text/event-stream',
          token: userStore.token,
          platform: 'YUNXIAO_STUDENT_H5',
        }
        await fetchEventSource(url, {
          method: 'POST',
          headers,
          body: JSON.stringify({
            sourceId: sourceId,
            sourceType: sourceType,
          }),
          signal: this.controller.signal,
          openWhenHidden: true,
          onmessage: (e: any) => {
            const data = JSON.parse(e.data)
            if (data.code == 200) {
              this.urlList.push(data.data)
              if (this.urlList.length > 5 && !this.audio) {
                this.init()
              }
            } else {
              $g.showToast(data.msg, 'error')
              this.question.data = {}
              this.controller.abort()
              return
            }
          },
          onerror(err) {
            throw new Error(err)
          },
          onclose: () => {
            console.log('接口请求完成')
            if (!this.urlList.length) {
              $g.showToast('暂无音频', 'warning')
              this.isPlay = 'pause'
              return
            }
            !this.audio && this.init()
          },
        })
      } catch (err) {
        this.type = null
        this.question.data = {}
        this.question.params = {}
        console.log(err)
      }
    },
    /* 多个音频请求 */
    async getMultipleAudioList(ids, type) {
      try {
        if (this.audio && $g._.isEqual(ids, this.question.ids)) {
          this.isPlay == 'start' ? this.pause() : this.play()
          return
        } else {
          this.controller && this.controller.abort()
          this.reset()
        }
        this.controller = new AbortController()
        this.type = type
        this.question.ids = ids
        this.isPlay = 'loading'
        for (const id of this.question.ids) {
          await this.fetchAudioStream(id, this.type)
        }
      } catch (err) {
        console.log(err)
        this.type = null
        this.question.data = {}
        this.question.params = {}
        this.isPlay = 'start'
      }
    },
    async getAudioList(type, item?) {
      try {
        if ([1, 2, 3, 4, 5].includes(type)) {
          const idMap = {
            1: item.questionId,
            2: item.subQuestionId,
            3: item.subQuestionId,
            4: item.subQuestionParseId,
            5: item.subQuestionParseId,
          }
          const params = {
            sourceId: idMap[type],
            sourceType: type,
          }

          if (this.audio && $g._.isEqual(params, this.question.params)) {
            this.isPlay == 'start' ? this.pause() : this.play()
            return
          } else {
            this.controller && this.controller.abort()
            this.urlList = []
            this.reset()
          }
          this.controller = new AbortController()
          this.type = type
          this.question.data = item
          this.question.params = params
          this.isPlay = 'loading'
          await this.fetchAudioStream(
            this.question.params.sourceId,
            this.question.params.sourceType,
          )
        }
      } catch (err) {
        this.type = null
        this.question.data = {}
        this.question.params = {}
        console.log(err)
      }
    },
    async getAudioListByMing(obj) {
      try {
        let id = ''
        if (this.audio && $g._.isEqual(obj, this.question.data)) {
          this.isPlay == 'start' ? this.pause() : this.play()
          return
        } else {
          this.controller && this.controller.abort()
          this.urlList = []
          this.reset()
        }
        this.question.data = obj
        this.controller = new AbortController()
        this.isPlay = 'loading'
        const url = `${baseURL}/v3/student/aiChat/text/tts/stream`
        const headers = {
          'Content-Type': 'application/json;charset=UTF-8',
          Accept: 'text/event-stream',
          token: userStore.token,
          platform: 'YUNXIAO_STUDENT_H5',
        }
        await fetchEventSource(url, {
          method: 'POST',
          headers,
          body: JSON.stringify({
            text: obj.content,
            textUnique: obj.id,
          }),
          signal: this.controller.signal,
          openWhenHidden: true,
          onmessage: (e: any) => {
            const data = JSON.parse(e.data)
            if (data.code == 200) {
              this.urlList.push(data.data)
              if (this.urlList.length > 5 && !this.audio) {
                this.init()
              }
            } else {
              $g.showToast(data.msg, 'error')
              this.question.data = {}
              this.controller.abort()
              return
            }
          },
          onerror(err) {
            throw new Error(err)
          },
          onclose: () => {
            console.log('接口请求完成')
            if (!this.urlList.length) {
              $g.showToast('暂无音频', 'warning')
              this.isPlay = 'pause'
              return
            }
            id = this.urlList[0].questionTtsId
            !this.audio && this.init()
          },
        })
        return id
      } catch (err) {
        this.type = null
        this.question.data = {}
        console.log(err)
      }
    },
    /* 播放 */
    async play() {
      if (this.audio && this.audio.pause) {
        this.isPlay = 'start'
        this.showSubTitle = true
        try {
          await this.audio.play()
        } catch (err) {
          console.log('播放请求被中断', err)
        }
      }
    },
    /* 暂停 */
    pause() {
      if (this.audio && !this.audio.paused) {
        this.audio.pause()
        this.isPlay = 'pause'
        this.showSubTitle = false
      }
    },
    /* 重置 */
    reset() {
      this.controller && this.controller.abort()
      if (this.audio) {
        this.audio.pause()
        this.audio.removeEventListener('ended', this.handleNext)
        this.audio = null
        this.showSubTitle = false
        this.subtitle = ''
      }
      this.$reset()
    },
    /* 初始化 */
    init() {
      this.audio = new Audio()
      this.audio.src = this.urlList[this.currentIndex].tts
      this.subtitle = this.urlList[this.currentIndex].text
      this.audio.play().catch((err) => {
        console.log(`⚡[ err ] >`, err)
        console.log('音频地址 => ', this.urlList[this.currentIndex]?.tts)
        this.handleNext()
      })
      this.isPlay = 'start'
      this.showSubTitle = true
      this.audio.addEventListener('ended', this.handleNext)
    },
    /* 顺序播放 */
    handleNext() {
      this.currentIndex++
      if (this.currentIndex < this.urlList.length) {
        this.audio.src = this.urlList[this.currentIndex].tts
        this.subtitle = this.urlList[this.currentIndex].text
        this.audio.play().catch((err) => {
          console.log(`⚡[ err ] >`, err)
          console.log('音频地址 => ', this.urlList[this.currentIndex]?.tts)
          this.handleNext()
        })
      } else {
        console.log('播放完毕')
        this.reset()
      }
    },
    /* 点赞与取消 */
    async userFeedback(state = 1, questionTtsId) {
      try {
        if (!questionTtsId) return
        await feedback({
          questionTtsId,
          feedback: state,
          accountId: this.accountId,
        })
        if (state == 2) $g.showToast('点赞了该音频~')
      } catch (error) {
        console.log('error => ', error)
      }
    },
  },
})
