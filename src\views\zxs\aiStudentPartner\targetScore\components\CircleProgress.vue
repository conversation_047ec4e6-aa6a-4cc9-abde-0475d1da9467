<template>
  <div class="w-180px h-180px svg-container">
    <svg x-mlns="http://www.w3.org/200/svg" viewBox="0 0 180 180">
      <defs>
        <linearGradient id="gradient">
          <stop offset="0%" :style="{ 'stop-color': startColor }" />
          <stop offset="100%" :style="{ 'stop-color': stopColor }" />
        </linearGradient>
      </defs>
      <circle
        r="82"
        cx="90"
        cy="90"
        fill="none"
        stroke="#f0f0f0"
        :stroke-width="6"
      />
      <circle
        class="progress-circle"
        r="82"
        cx="90"
        cy="90"
        fill="none"
        :stroke="'url(#gradient)'"
        :stroke-width="10"
        stroke-linecap="round"
        :stroke-dasharray="`${topArc},100000`"
        transform="rotate(-90)"
        transform-origin="center"
      ></circle>
    </svg>
    <div
      class="circle-container"
      :style="{ transform: `rotate(${rotateDeg}deg)` }"
    >
      <div class="relative">
        <div class="circle"></div>
      </div>
    </div>
    <div class="score-container flex flex-col items-center justify-center">
      <div class="lh-[33px] h-30px">
        <span class="text-30px D-DIN-Bold">{{ targetScore }}</span
        ><span class="text-[#9E9E9E] text-14px ml-4px lh-[20px] h-20px"
          >分</span
        >
      </div>
      <div class="text-[#FE6B47] mt-4px font-500 text-12px h-17px lh-[17px]">
        目标成绩
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
const props = defineProps({
  percent: {
    type: Number,
    default: 0, //比例 小数
  },
  //顺时针方向逆转了，所以两个颜色颠倒
  startColor: {
    type: String,
    default: '#FE6B47',
  },
  stopColor: {
    type: String,
    default: '#FFB281',
  },
  targetScore: {
    type: [Number, String],
    default: 0,
  },
})

let topArc = $ref(0)
let rotateDeg = $ref(0)
let circleRotateDeg = $ref('0deg')

watch(
  () => props.percent,
  () => {
    setTimeout(() => {
      //弧长公式计算要显示的弧长
      topArc = (Math.PI * 82 * props.percent * 360) / 180
      rotateDeg = props.percent * 360
      circleRotateDeg = rotateDeg + 'deg'
    }, 100)
  },
  { immediate: true },
)
</script>
<style scoped lang="scss">
.circle-container {
  width: 1px;
  height: 82px;
  position: absolute;
  left: 50%;
  bottom: 50%;
  transform-origin: bottom;
  transition: transform 0.4s linear;
}

.circle {
  width: 3px;
  height: 3px;
  background: #fff;
  border-radius: 50%;
  position: absolute;
  left: -1.5px;
  bottom: -1.5px;
}

.progress-circle {
  transition:
    stroke-dasharray 0.4s linear,
    stroke 0.3s;
}

.score-container {
  position: absolute;
  left: 50%;
  bottom: 50%;
  transform: translate(-50%, 50%);
}

.svg-container {
  position: relative;
  &::before {
    content: '';
    position: absolute;
    width: 180px;
    height: 180px;
    left: 0;
    bottom: 0;
    background: url(@/assets/img/aiStudentPartner/circleBackground.png) center /
      100% 100% no-repeat;
    transform: rotate(v-bind(circleRotateDeg));
    transition: transform 0.4s linear;
  }
}
</style>
