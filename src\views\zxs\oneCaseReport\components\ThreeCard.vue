<template>
  <div
    v-if="hasKpointData"
    class="bg-[#fff] rounded-[8px] mx-12px px-12px py-17px text-13px text-[#2E2323] mb-15px"
  >
    <div class="message-title">知识点分析</div>
    <!-- 图表 -->
    <div class="flex justify-center">
      <g-chart class="w-350px h-220px" :option="chartOption" />
    </div>
    <!-- 循环展示知识点卡片 -->
    <div class="knowledge-cards">
      <div
        v-for="(kpoint, index) in safeKpointMasterList"
        :key="index"
        class="item-card mb-15px"
      >
        <div class="header-group mb-10px">
          <CardTitle :title="kpoint.kpoint" :ellipsis="true" :max-width="180" />
          <div
            class="tag"
            :class="{
              'tag-bad': kpoint.rating <= 2, // 差或较差 (1-2)
              'tag-medium': kpoint.rating === 3, // 一般 (3)
              'tag-good': kpoint.rating >= 4, // 好或很好 (4-5)
            }"
          >
            {{ getRatingTitle(kpoint.rating) }}
          </div>
          <!-- 使用van-rate组件并自定义图标 -->
          <van-rate
            v-model="kpoint.rating"
            :count="5"
            readonly
            :size="16"
            :icon="starInactiveImg"
            :void-icon="starActiveImg"
            class="ml-6px"
          />
        </div>
        <div class="text-13px text-[#423534]">
          {{ kpoint.ratingContent || '无评价内容' }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import CardTitle from './CardTitle.vue'
import type { IOneCaseReportDetail, IKpointMaster } from '../type'

/** 组件Props接口 */
interface IProps {
  /** 报告数据 */
  reportData?: IOneCaseReportDetail | null
}

/** 定义Props */
const props = withDefaults(defineProps<IProps>(), {
  reportData: null,
})

/** 五角星图片地址 */
const starActiveImg =
  'https://qm-cloud.oss-cn-chengdu.aliyuncs.com/zxsApp/img/oneCaseReport/start1.png'
const starInactiveImg =
  'https://qm-cloud.oss-cn-chengdu.aliyuncs.com/zxsApp/img/oneCaseReport/star2.png'

/** 安全的知识点掌握程度列表数据，避免null和undefined */
const safeKpointMasterList = computed<IKpointMaster[]>(() => {
  return props.reportData?.kpointAbility?.kpointMasterList || []
})

/** 检查是否有知识点掌握程度数据 */
const hasKpointData = computed(() => {
  return safeKpointMasterList.value.length > 0
})

/** 根据评级获取评级标题 */
const getRatingTitle = (rating: number): string => {
  switch (rating) {
    case 1:
      return '差'
    case 2:
      return '较差'
    case 3:
      return '一般'
    case 4:
      return '好'
    case 5:
      return '很好'
    default:
      return '未知'
  }
}

/** 获取雷达图指标数据 */
const radarIndicators = computed(() => {
  // 从kpointMasterList提取知识点名称作为雷达图指标
  return safeKpointMasterList.value.map((kpoint) => ({
    name: kpoint.kpoint,
    max: 100,
  }))
})

/** 获取雷达图数据值 */
const radarValues = computed(() => {
  if (!hasKpointData.value) {
    return [20, 20, 20, 20]
  }

  // 根据评级生成对应的值：1-差(20分), 2-较差(40分), 3-一般(60分), 4-好(80分), 5-很好(100分)
  return safeKpointMasterList.value.map((kpoint) => kpoint.rating * 20)
})

/** 雷达图配置 */
const chartOption = computed(() => {
  return {
    radar: {
      indicator: radarIndicators.value,
      center: ['50%', '50%'],
      radius: '60%', // 缩小雷达图半径，为文本留出更多空间
      axisNameGap: 5, // 增加轴名称与轴线的距离
      nameGap: 20, // 增加标签与雷达图的距离
      // 移除 shape: 'circle'，恢复为默认的多边形
      axisName: {
        color: '#333',
        fontSize: 12,
        padding: [5, 7], // 增加文本内边距
        formatter: (name: string) => {
          // 如果文本超过一定长度，添加换行符
          if (name.length > 6) {
            const mid = Math.ceil(name.length / 2)
            return name.slice(0, mid) + '\n' + name.slice(mid)
          }
          return name
        },
      },
      splitArea: {
        show: false, // 关闭斑马纹区域
      },
      splitLine: {
        lineStyle: {
          color: '#ddd',
        },
      },
      axisLine: {
        lineStyle: {
          color: '#ddd',
        },
      },
    },
    series: [
      {
        type: 'radar',
        data: [
          {
            value: radarValues.value,
            name: '掌握程度',
            itemStyle: {
              color: '#1890FF',
            },
            lineStyle: {
              color: '#1890FF',
              width: 2,
            },
            areaStyle: {
              color: 'rgba(24,144,255,0.3)',
            },
          },
        ],
      },
    ],
  }
})
</script>

<style lang="scss" scoped>
.knowledge-cards {
  margin-top: 15px;
}

.item-card {
  min-height: 100px;
  background: linear-gradient(90deg, #eff7ff 0%, #fff 100%), #ffffff;
  border-radius: 12px;
  border: 2px solid #ffffff;
  padding: 10px;

  .header-group {
    display: flex;
    align-items: center;

    .tag {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 5px;
      height: 100%;
      /* 修改固定高度为100% */
      min-height: 21px;
      /* 保持最小高度 */
      background: #f4f1dc;
      color: #ff5200;
      font-size: 13px;
      margin-left: 13px;
      margin-right: 6px;
      border-radius: 8px 3px 8px 3px;
      position: relative;

      /* 较差和差：默认样式（橙色系） */
      &.tag-bad {
        background: #f4f1dc;
        color: #ff5200;
      }

      /* 一般：蓝色系 */
      &.tag-medium {
        background: #e6e9fe;
        color: #6c7cfd;
      }

      /* 好和很好：绿色系 */
      &.tag-good {
        background: #d7f8ee;
        color: #00da8f;
      }
    }
  }
}
</style>
