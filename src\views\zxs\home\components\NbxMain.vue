<template>
  <div class="h-full overflow-auto no-bar pb-14px">
    <div
      v-if="userStore?.role == 'parents' && showBtnByVerison"
      class="flex items-center justify-end pb-6px"
    >
      <g-icon
        name="svg-home-switch"
        size="16"
        color="#fff"
        class="mr-3px"
        @click="$g.flutter('back')"
      />
      <span class="text-[#fff]" @click="$g.flutter('back')">家长模式</span>
    </div>
    <div
      class="flex justify-between items-center text-white"
      :class="userStore?.role == 'parents' ? '' : 'mt-27px'"
    >
      <div>
        <div class="text-22px font-600 mb-2px">
          <span>Hello,</span>
          <span>{{ userInfo?.studentName }}</span>
        </div>
        <div class="text-13px font-400 flex flex-wrap">
          <div class="mr-10px">
            {{ userInfo?.schoolName }}{{ userInfo?.sysGradeName
            }}{{ userInfo?.className }}
          </div>
          <div></div>
          <div>{{ userInfo?.cityName }}</div>
        </div>
      </div>
      <img
        v-if="userStore?.userInfo?.headPicture"
        :src="userStore?.userInfo?.headPicture"
        class="w-52px h-52px"
        alt=""
      />
      <img
        v-else
        src="@/assets/img/home/<USER>"
        class="w-52px h-52px"
        alt=""
      />
    </div>
    <AiPartner></AiPartner>

    <!-- 智习室APP二期需求隐藏'我的学习计划'板块 -->
    <!-- <MyPlain @jump-j-z-t-app="jumpJZTApp"></MyPlain> -->
    <!-- 启鸣新高考 高三不展示-->
    <NewExamination
      v-if="userInfo?.sysGradeId != 12"
      @jump-j-z-t-app="jumpJZTApp"
    ></NewExamination>

    <!-- 后台配置的icon -->
    <ZxsIcon></ZxsIcon>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from '@/stores/modules/user'
import { getJZTKey } from '@/api/common'
import NewExamination from './NewExamination.vue'
// import MyPlain from './MyPlain.vue'
import AiPartner from './AiPartner.vue'
import ZxsIcon from './ZxsIcon.vue'

const userStore: any = useUserStore()
const props = defineProps({
  userInfo: {
    type: Object,
    default: () => {},
  },
})

const router = useRouter()
let { version } = $(storeToRefs(useUserStore()))

//1.6.8以及以上版本登录判断
const showBtnByVerison = $computed(() => {
  return !($g.tool.compareVersion('1.6.8', version) === 'lt')
})

//跳转金字塔app
async function jumpJZTApp(path) {
  if (!path) return $g.showToast('敬请期待！')
  try {
    // 跳转金字塔app项目地址需要获取兑换token的参数
    let key = await getJZTKey()
    path = path.includes('?') ? `${path}&` : `${path}?`
    if (!$g.inApp) {
      window.location.href = `${path}encryptedStr=${key}`
    } else {
      $g.flutter('launchInNewWebView', {
        url: `${path}encryptedStr=${key}`,
        autoPrefix: false,
        autoClose: false,
      })
    }
  } catch (error) {
    console.log(`⚡[ error ] >`, error)
  }
}
</script>

<style lang="scss" scoped>
.no-bar {
  &::-webkit-scrollbar {
    display: none;
  }
}
</style>
