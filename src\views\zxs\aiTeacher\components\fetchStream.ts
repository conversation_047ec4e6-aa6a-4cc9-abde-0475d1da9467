import { EventSourceParserStream } from 'eventsource-parser/stream'
export async function slowedStreamFetch(fetchPromise, speed, decodeFunc) {
  const response = await fetchPromise
  const eventStream = response.body
    .pipeThrough(new TextDecoderStream())
    .pipeThrough(new EventSourceParserStream())
  const reader = eventStream.getReader()
  const stream = new ReadableStream({
    async start(controller) {
      const pump = async () => {
        const { value, done } = await reader.read()
        if (done) {
          controller.close()
          return
        }
        for await (const char of decodeFunc(value)) {
          controller.enqueue(char)
          await new Promise((resolve) => setTimeout(resolve, speed))
        }
        pump()
      }
      pump()
    },
  })

  const result: any = new Response(stream, { headers: response.headers })

  result.closeStream = () => {
    reader.cancel()
  }

  return result
}

//根据公式计算输出字符数
export function mathCount(text) {
  const regex = /\$([^$]+)\$/g
  let match
  let count = 0
  // 使用正则表达式的 exec 方法来查找所有匹配项
  while ((match = regex.exec(text)) !== null) {
    count++
  }
  //公式数量每10个字符数加2，最大6个
  const num = Math.floor(count / 15)
  const addText = 1 + num * 2
  if (addText >= 7) return 6
  return addText
}
