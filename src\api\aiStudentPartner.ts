import config from '@/config/index'
import request from '@/utils/request/index'
const { baseURL, baseURL2 } = config
//智推院校
export function getUniversityVolunteer(data) {
  return request.get(baseURL + '/v2/family/learn/getUniversityVolunteer', data)
}
//专业弹窗
export function getMajorList(data) {
  return request.get(baseURL + '/v2/family/learn/getMajorList', data)
}

//获取首页Ai学伴信息
export function getAiStudentPartner() {
  return request.get(baseURL + '/v2/family/learn/getZxsAppStudentScore')
}

//获取首页智推专业列表
export function getZxMajorList(data?) {
  return request.get(baseURL + '/v2/family/learn/getMajor', data)
}

//获取科目列表
export function getSubjectList() {
  return request.get(baseURL + '/v3/homePage/sysSubject')
}

//获取省份列表

export function getProvinceList() {
  return request.get(baseURL + '/v3/zx/student/areaList')
}

//提交目标成绩
export function submitTargetGrade(data) {
  return request.post(baseURL + '/v2/family/learn/setVoluntary', data)
}

export async function getStudentInfoList(data) {
  return request.get(baseURL2 + '/h5/student/task/list', data)
}

// 维度
export async function getDimension(data) {
  return request.get(baseURL2 + '/h5/student/dimension/list', data)
}

//智推专业跳转
export function getMajorDetail(data) {
  return request.get(baseURL + '/v2/family/learn/majorDetail', data)
}

//成绩详情
export function getGradeDetail() {
  return request.get(baseURL + '/v2/family/learn/getHxnZxsAppStudentScore', {
    delay: false,
  })
}
