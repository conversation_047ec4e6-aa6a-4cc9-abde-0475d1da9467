/** 知识点信息接口 */
interface IKnowledgePoint {
  /** 知识点ID */
  sysKnowledgePointId: number
  /** 知识点名称 */
  sysKnowledgePointName: string
}

/** 章节评价图表项接口 */
interface IChartItem {
  /** 任务报告图表ID */
  exerciseTaskReportChartId: number
  /** 任务报告ID */
  exerciseTaskReportId: number
  /** 教材目录ID */
  sysTextbookCatalogId: number
  /** 教材目录名称 */
  sysTextbookCatalogName: string
  /** 知识点名称列表（逗号分隔） */
  sysKnowledgePointNames: string
  /** 评级 */
  rating: number
  /** 评级标题 */
  ratingTitle: string
  /** 评级内容 */
  ratingContent: string
  /** 知识点列表 */
  knowledgePointList: IKnowledgePoint[]
}

/** 相关能力细分评级接口 */
interface IRelevantRating {
  /** ID（type为1-sysCourseKnowAbilityId，2-sysCourseAbilityId，3-sysCourseLiteracyId） */
  id: number
  /** 名称 */
  name: string
  /** 评分等级，最大值4，越好得分越高 */
  rating: number
  /** 评语总结 */
  evaluation: string
}

/** 相关能力评价接口 */
interface IRelevantEvaluation {
  /** 相关能力类型：1：知识能力，2：学科素养，3：关键能力 */
  type: number
  /** 评语总结 */
  evaluation: string
  /** 相关能力细分评级 */
  relevantRatingList: IRelevantRating[]
}

/** 知识点掌握程度接口 */
interface IKpointMaster {
  /** 知识点名称 */
  kpoint: string
  /** 等级 1-差,2-较差,3-一般,4-好,5-很好 */
  rating: number
  /** 评级标题 */
  ratingTitle?: string
  /** 评分等级评语 */
  ratingContent: string
  /** 相关能力评价 */
  relevantEvaluationList?: IRelevantEvaluation[]
}

/** 知识点与能力素养评价接口 */
interface IKpointAbility {
  /** 知识点掌握程度列表 */
  kpointMasterList: IKpointMaster[]
  /** 相关能力评价 */
  relevantEvaluationList?: IRelevantEvaluation[]
}

/** 一生一案报告详情接口 */
interface IOneCaseReportDetail {
  /** 练习任务报告ID */
  exerciseTaskReportId: number
  /** 练习任务ID */
  exerciseTaskId: number
  /** 报告状态:1-待生成,2-生成中,3-生成成功,4-生成失败 */
  reportState: number
  /** 教材ID */
  sysTextbookId: number
  /** 评级 */
  rating: number
  /** 评级标题 */
  ratingTitle: string
  /** 评级内容 */
  ratingContent: string
  /** 评级类型:1-落后,2-领先 */
  ratingType: number
  /** 评级落后领先占比 */
  ratingRate: number
  /** 综合评估依据 */
  comprehensiveEvaluation: string[]
  /** 能力短板 */
  shortcomingsAbility: string[]
  /** 提分潜力 */
  enhancePotential: string[]
  /** 寄语 */
  encouragementWords: string[]
  /** 知识点能力图谱 */
  chartList: IChartItem[]
  /** 学生ID */
  schoolStudentId: number
  /** 学生名称 */
  studentName: string
  /** 课程ID */
  sysCourseId: number
  /** 课程名称 */
  sysCourseName: string
  /** 学科ID */
  sysSubjectId: number
  /** 学科名称 */
  sysSubjectName: string
  /** 学生得分 */
  finallyScore: number
  /** 试卷总分 */
  totalScore: number
  /** 得分率 */
  scoreRate: number
  /** 新增的知识点与能力素养评价 */
  kpointAbility?: IKpointAbility
}

export type {
  IKnowledgePoint,
  IChartItem,
  IOneCaseReportDetail,
  IKpointAbility,
  IKpointMaster,
  IRelevantEvaluation,
  IRelevantRating,
}
