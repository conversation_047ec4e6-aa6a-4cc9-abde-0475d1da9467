<template>
  <van-popup
    v-model:show="showPop"
    :close-on-popstate="true"
    class="rounded-[9px]"
  >
    <div
      class="w-[779px] h-[500px] bg-white pt-27px px-21px pb-18px relative text-[#333]"
    >
      <g-icon
        name="svg-global-close-l"
        size="20"
        class="absolute right-24px top-18px van-haptics-feedback"
        @click="showPop = false"
      />
      <div
        class="w-full text-center truncate text-17px text-[#333] h-17px leading-[17px] mb-16px"
      >
        {{ fileDataLocal?.describeTitle || '-' }}
      </div>
      <div
        v-if="fileDataLocal?.describeContext"
        class="w-full h-[288px] mb-9px bg-[#F3F9FF] rounded-[9px] p-12px overflow-y-auto file-md"
      >
        <g-markdown :text="fileDataLocal?.describeContext" mode="preview">
        </g-markdown>
      </div>
      <div
        v-if="fileDataLocal?.attachList.length"
        class="w-full h-126px overflow-y-auto rounded-[4px] bg-[#F8F8F8] py-13px px-14px border border-dashed border-[#E8E8E8]"
      >
        <div
          v-for="(item, index) in fileDataLocal.attachList"
          :key="index"
          class="flex h-28px w-full items-center mb-9px"
        >
          <div class="w-23px h-28px mr-7px">
            <img
              :src="$g.tool.getFileTypeIcon(item.attachExtension)"
              alt="file icon"
              class="w-full h-full object-contain"
            />
          </div>
          <div class="w-[561px] truncate mr-42px text-13px text-[#333]">
            {{ item.attachName }}
          </div>
          <div class="w-71px h-full flex items-center justify-end">
            <div
              v-if="!isZip(item)"
              class="text-13px text-[#3496FA] underline van-haptics-feedback"
              @click.stop="checkItem(item)"
            >
              查看
            </div>
          </div>
        </div>
      </div>
      <div
        v-if="fileDataLocal?.virtualUserName"
        class="text-[#666666] mt-[10px]"
      >
        本报告由{{ fileDataLocal?.virtualUserName }}上传
      </div>
    </div>
  </van-popup>
</template>

<script setup lang="ts">
const router = useRouter()
const props = defineProps<{
  fileData: any
}>()
let showPop = defineModel<boolean>('show')

const fileDataLocal = $computed<any>(() => {
  if (props.fileData) {
    return {
      ...props.fileData,
      attachList: props.fileData.attachList.map((v) => ({
        ...v,
        attachExtension: v.attachExtension.replace(/\./g, ''),
      })),
    }
  }
  return null
})

function checkItem(item) {
  if (isAudio(item)) {
    $g.flutter('playVideo', {
      title: item.attachName,
      resource_url: item.attachUrl,
      config: {
        autoplay: true,
      },
    })
  } else if (isImg(item)) {
    $g.flutter('previewImage', {
      urls: [item.attachUrl],
      index: 0,
    })
  } else {
    router.push({
      name: 'PreviewDoc',
      query: {
        url: item.attachUrl,
        finish: 1,
      },
    })
  }
}

function isAudio(item) {
  return [
    'mp4',
    'avi',
    'rmvb',
    'flv',
    'wmv',
    'mkv',
    'mov',
    'mp3',
    'wav',
    'aif',
    'm3u8',
  ].includes(item?.attachExtension)
}

function isImg(item) {
  return ['jpg', 'jpeg', 'gif', 'png', 'gif', 'webp', 'svg'].includes(
    item?.attachExtension,
  )
}

function isZip(item) {
  return ['zip', 'rar'].includes(item?.attachExtension)
}
</script>

<style lang="scss" scoped>
.file-md {
  :deep() {
    .mdEditor-container {
      position: relative;
      &::before {
        display: inline-block;
        position: absolute;
        content: '';
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
      }
    }
  }
}
</style>
