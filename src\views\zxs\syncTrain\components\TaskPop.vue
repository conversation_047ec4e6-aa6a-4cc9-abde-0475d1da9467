<template>
  <van-popup
    v-model:show="showPop"
    class="w-full max-w-[100%] task-pop left-0"
    closeable
  >
    <div v-if="isVideo && showPop" class="bg-white">
      <g-video
        :url="info.fileAbsoluteUrl"
        :config="{ poster: info.videoCover }"
        class="!w-full h-screen"
        @timeupdate="timeUpdate"
        @ended="ended"
      />
    </div>
    <div v-else-if="showPop" class="h-[800px]">
      <iframe
        v-show="!showLoading"
        :key="info.fileAbsoluteUrl"
        :src="calSrc"
        frameborder="0"
        class="w-full h-full"
        @load="showLoading = false"
      ></iframe>
      <g-loading v-show="showLoading" class="h-200px"></g-loading>
    </div>
  </van-popup>
</template>

<script setup lang="ts">
import { useSettingStore } from '@/stores/modules/setting'

const props = defineProps({
  info: {
    type: Object,
    default: () => {},
  },
})
const emit = defineEmits<{
  (e: 'ended1'): void
  (e: 'timeupdate1', val: any): void
}>()

const settingStore = useSettingStore()
let navHeight = $ref('0px')

let showPop = defineModel<boolean>('show')
let showLoading = $ref(false)
let exts = [
  'mp4',
  'avi',
  'rmvb',
  'flv',
  'wmv',
  'mkv',
  'mov',
  'mp3',
  'wav',
  'aif',
  'm3u8',
  'webm',
]

const isVideo = $computed(() => {
  let ext: any = $g.tool.getExt(props.info.fileAbsoluteUrl)
  if (exts.includes(ext)) {
    return true
  }
  return false
})

const calSrc = $computed(() => {
  if (isVideo) {
    return props.info.fileAbsoluteUrl
  }
  return '//vip.ow365.cn/?i=28777&ssl=1&furl=' + props.info.fileAbsoluteUrl
})

function timeUpdate(player) {
  emit('timeupdate1', player)
}

function ended() {
  emit('ended1')
}

onBeforeMount(() => {
  navHeight = settingStore.navigationHeight + 'px'
})

watch(
  () => props.info,
  () => {
    showLoading = true
  },
  {
    immediate: true,
  },
)
</script>

<style lang="scss" scoped>
.task-pop {
  :deep() {
    .van-icon-cross {
      z-index: 999;
      transform: translateY(v-bind('navHeight'));
    }
  }
}
</style>
