import { loadOnScroll } from './directives'
import vant from './vant'
import 'virtual:svg-icons-register'
import { initVConsole } from './vconsole'
import { registerSentry } from './sentry'
import $g from '@/utils/index'
import { registeredVMdEditor } from './vMdEditor'

const plugins = { $g }

export function setupPlugins(app) {
  registerSentry(app)
  registeredVMdEditor(app)
  // 注册全局变量
  Object.keys(plugins).forEach((key) => {
    app.provide(key, plugins[key as keyof typeof plugins])
    app.config.globalProperties[key] = plugins[key as keyof typeof plugins]
  })

  // 注册vant
  const { vantComponents } = vant
  Object.keys(vantComponents).forEach((key) => {
    if (key == 'Lazyload') app.use(vantComponents[key], { lazyComponent: true })
  })

  // 注册指令
  app.directive('loadOnScroll', loadOnScroll)

  initVConsole()
}
