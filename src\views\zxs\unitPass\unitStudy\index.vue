<template>
  <div id="studyContainer">
    <g-navbar customTitle="小节学习"></g-navbar>
    <div class="px-[16px] py-[12px]">
      <g-loading v-if="showLoading" class="h-[200px]"></g-loading>
      <div v-else>
        <div
          :style="{
            minHeight: `calc(100vh - ${$g.navBarTotalHeight}px - 24px)`,
          }"
        >
          <div v-if="videoList?.length">
            <div
              class="w-full h-[228px] rounded-t-[7px] overflow-hidden relative"
            >
              <g-video
                :key="currentVideo?.fileAbsoluteUrl"
                :url="currentVideo?.fileAbsoluteUrl || ''"
                :current-time="currentVideo.current_time"
                class="!w-full !h-full !pt-0 rounded-[13px]"
                :config="{
                  poster: currentVideo?.videoCoverUrl,
                }"
                @timeupdate="timeUpdate"
                @ended="ended"
                @play="onPlay"
              />
            </div>
            <div
              ref="videos"
              class="flex w-full mt-[12px] flex-wrap gap-y-[12px] gap-x-[11px]"
            >
              <div
                v-for="(item, idx) in videoList"
                :key="
                  item.onion_topic_video_address_id ||
                  item.knowledge_basic_resource_id
                "
                class="w-[166px] border-[2px] bg-[#FFFFFF] rounded-[8px] overflow-hidden"
                :class="[
                  idx == videoIdx ? 'border-[#368FF1]' : 'border-[#E9EDF0]',
                ]"
                @click="videoChange(idx)"
              >
                <div class="w-full relative img-cover rounded-[8px]">
                  <g-img
                    :src="
                      item.videoCoverUrl ||
                      $g.tool.getFileUrl('unitPass/unitVideoPic.png')
                    "
                    alt="cover"
                    width="100%"
                    height="93"
                    fit="cover"
                    :preview="false"
                  ></g-img>
                  <g-icon
                    v-if="item.videoCoverUrl"
                    name="svg-unitPass-videoSvg"
                    size="22"
                    class="absolute left-[50%] top-[50%] -translate-x-[50%] -translate-y-[50%]"
                  />
                  <div
                    class="absolute px-[4px] py-[2px] bg-[#3398F7] font-500 top-0 left-0 min-w-[85px] h-[20px] text-white text-center text-[12px]"
                    style="border-radius: 0 0 8px 0"
                  >
                    {{ item.viewsNumber || 0 }}人已观看
                  </div>
                </div>
                <div class="text-[14px] line-1 p-[6px] font-400 text-[#333333]">
                  {{ item.onion_topic_name || item.video_name }}
                </div>
                <div
                  class="text-11px pb-[7px] flex items-center px-6px font-400"
                >
                  <div class="flex-shrink-0 text-[11px] text-[#666666]">
                    已观看
                  </div>
                  <div class="flex w-[100%] h-2px items-center pl-6px pr-5px">
                    <div class="w-full rounded-[1px] bg-[#F2F3F5] h-2px">
                      <div
                        class="rounded-[1px] bg-[#217DFB] h-2px"
                        :style="[`width:${item.lockRate || 0}%`]"
                      ></div>
                    </div>
                  </div>
                  <div class="text-[11px] !text-[#666666] flex-shrink-0">
                    {{ item.lockRate || 0 }}%
                  </div>
                </div>
              </div>
            </div>
          </div>
          <g-empty v-else></g-empty>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useUnitStore } from '@/stores/modules/unitPass'
import { getVideoListApi, submitVideo } from '@/api/unitPass'
let videoList = $ref<any[]>([])
let videoIdx = $ref(0)
let totalTime: any = $ref(0)
let showLoading = $ref(false)
let chapterList = $ref<any>({})
let timer: any = $ref(null)
let hasSaved = $ref(false) // 是否已经保存章节记录
const unitStore = useUnitStore()
const currentVideo = $computed(() => {
  return videoList[videoIdx] || null
})
const addWatch = (time) => {
  if (!route.query?.sysTextbooksCatalogId) return
  if (!unitStore.active) {
    unitStore.active = true
  }
  if (!unitStore.active1) {
    unitStore.active1 = true
  }

  let type: any = ''
  if (currentVideo.type) {
    type = currentVideo.type
  } else {
    type = currentVideo.onion_topic_video_address_id ? 1 : 2
  }
  submitVideo({
    sys_textbooks_catalog_id: currentVideo.sys_textbooks_catalog_id ?? '',
    resource_id:
      currentVideo.onion_topic_video_address_id ||
      currentVideo.knowledge_basic_resource_id,
    type,
    total_time: currentVideo.videoDuration
      ? currentVideo.videoDuration
      : Math.floor(totalTime),
    current_time: time,
    source: currentVideo.source || 1,
  }).then(() => {
    // getVideoListApi(true)
  })
}
const route = useRoute()
let currentTime = $ref(0)
//视频时间变化执行
const timeUpdate = (player) => {
  if (!player?.duration) return
  const time = Math.floor(player.currentTime)
  //时间小于1或者有计时器不执行
  if (time < 1 || timer || currentVideo.current_time == time) return
  timer = setTimeout(() => {
    totalTime = player.duration
    changeTime(time)
    timer = null
  }, 10000)
}
//获取视频列表
const getVideoList = async (reset = false) => {
  if (!route.query?.sysTextbooksCatalogId) return
  try {
    showLoading = true
    const data = await getVideoListApi({
      sysTextbooksCatalogId: route.query?.sysTextbooksCatalogId ?? '',
    })
    if (data == null) return
    chapterList = { ...data, expand: true }
    if (reset || route.query?.resourceId) return
    if (chapterList?.allVideoList.length) {
      videoList = chapterList?.allVideoList ?? []
    } else {
      videoList = []
    }
  } finally {
    showLoading = false
  }
}
//视频播放完立即添加记录
const ended = () => {
  clearTimeout(timer)
  timer = null
  changeTime(currentVideo.videoDuration)
}
async function onPlay() {}
const videoChange = (idx) => {
  document.getElementById('studyContainer')?.scrollIntoView({
    behavior: 'smooth',
  })
  videoIdx = idx
  clearTimeout(timer)
  timer = null
}
const changeTime = (time) => {
  currentTime = Math.floor(time)
  videoList.forEach((item) => {
    let itemId =
      item.onion_topic_video_address_id || item.knowledge_basic_resource_id
    let currentId =
      currentVideo.onion_topic_video_address_id ||
      currentVideo.knowledge_basic_resource_id
    if (itemId == currentId) {
      item.current_time = currentTime
      item.lockRate = Math.round(
        (currentTime / (item.videoDuration || totalTime)) * 100,
      )
    }
  })
  addWatch(currentTime)
}
onMounted(() => {
  getVideoList()
})
</script>
<style scoped lang="scss"></style>
