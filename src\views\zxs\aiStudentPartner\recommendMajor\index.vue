<template>
  <div class="top-bg bg-[#F9F9F9]">
    <g-navbar customBackGround="transparent" />
    <div
      class="px-16px overflow-y-auto no-bar pb-20px"
      :style="{
        height: `calc(100vh - ${useSettingStore().navBarTotalHeight}px - ${useSettingStore().navigationHeight}px)`,
      }"
    >
      <div
        class="flex-cc h-32px br-[8px] bg-[#FFF6F4] border border-[rgba(254,107,71,0.14)]"
      >
        <img
          :src="$g.tool.getFileUrl('home/AI.png')"
          alt=""
          class="w-17px h-12px"
        />
        <span class="ml-4px text-[#FE6B47] text-14px h-20px lh-[20px]"
          >智推专业根据职业兴趣测评结果推荐</span
        >
      </div>
      <g-loading v-if="loading" class="h-200px"></g-loading>
      <div v-else-if="ifShowMajor">
        <div class="mt-10px flex items-end">
          <div class="font-600 text-14px h-20px lh-[20px]">智推专业</div>
          <div class="text-12px text-[#999999] ml-7px h-17px lh-[17px]">
            根据职业兴趣测评结果推荐
          </div>
        </div>
        <div class="mt-12px major pt-10px pl-10px pr-11px pb-16px">
          <div class="flex justify-between mb-12px items-center">
            <div class="font-600 text-12px ml-2px">推荐专业</div>
            <div class="flex items-center">
              <span
                class="text-12px text-[#666666]"
                @click="
                  $router.push({
                    name: 'EvaluationReport',
                    query: { student_report_id },
                  })
                "
              >
                完整报告</span
              >
              <g-icon
                name="svg-home-arrowRight"
                color="#666666"
                size="12"
                class="ml-4px mb-2px"
              />
            </div>
          </div>
          <div class="flex flex-wrap gap-x-10px gap-y-8px">
            <div
              v-for="item in majorList"
              :key="item"
              class="w-100px h-24px line-1 text-12px bg-white br-[4px] text-center lh-[24px] text-[#363535] pl-10px pr-6px"
              @click="showProfession(item)"
            >
              {{ item.professional_name }}
            </div>
          </div>
          <div class="flex items-center mt-12px h-17px lh-[17px]">
            <img
              :src="$g.tool.getFileUrl('home/info.png')"
              alt=""
              class="w-13px h-13px mb-2px mr-2px"
            />
            <span class="text-[#999999] text-12px"
              >点击可查看专业介绍及包含专业</span
            >
          </div>
        </div>
      </div>
      <div v-else class="mt-120px flex flex-col items-center">
        <img
          :src="$g.tool.getFileUrl('aiStudentPartner/major-empty.png')"
          alt=""
          class="w-100px h-79px"
        />
        <div class="mt-10px text-14px">你当前暂未完成测评</div>
        <div
          class="mt-40px bg-[#EDEDED] br-[12px] w-168px h-43px flex-cc font-600 relative"
          :class="[careerLoading ? 'opacity-[0.5]' : '']"
          @click="jumpCareer()"
        >
          <van-loading
            v-show="careerLoading"
            class="h-full absolute w-full flex-cc"
          ></van-loading>
          <span class="text-[#FE6B47]">【点击此处】</span>
          <span>进行填写</span>
        </div>
      </div>
    </div>
    <MajorPop v-model:show="showPop" :aiMajorInfo="curProfession"></MajorPop>
  </div>
</template>
<script setup lang="ts">
import MajorPop from '@/views/zxs/home/<USER>/MajorPop.vue'
import { getZxMajorList } from '@/api/aiStudentPartner'
import { useJumpCareer } from '@/views/zxs/home/<USER>/jumpCareer'
import { useSettingStore } from '@/stores/modules/setting'

const { loading: careerLoading, jumpCareer } = useJumpCareer()
let loading = $ref(false)
let student_report_id = $ref<any>(null)
let majorList = $ref<any>([])
let curProfession = $ref<any>({})
let showPop = $ref(false)
const ifShowMajor = $computed(() => {
  return majorList.length > 0
})

async function getRecommendedMajor() {
  try {
    loading = true
    let res = await getZxMajorList({
      sliceAll: true,
    })
    if (!res || res?.length == 0) {
      majorList = []
      student_report_id = null
      loading = false
      return
    }
    majorList = res.recommend_subjects || []
    student_report_id = res.student_repot_id
    loading = false
  } catch (err) {
    loading = false
    console.log('获取专业失败', err)
  }
}

function showProfession(item) {
  curProfession = {
    ...item,
    list: item.professional[0].include_professional.split('、'),
  }
  showPop = true
}

onBeforeMount(() => {
  getRecommendedMajor()
})
onMounted(() => {
  $g.bus.on('openMajorPolling', refresh)
})

onBeforeUnmount(() => {
  $g.bus.off('openMajorPolling', refresh)
})

function refresh() {
  loading = true
  setTimeout(() => {
    getRecommendedMajor()
  }, 500)
}
</script>
<style scoped lang="scss">
.top-bg {
  background: url(@/assets/img/aiStudentPartner/top-bg.png) top center / 100%
    auto no-repeat;
}
.major {
  border: 1px solid;
  border-image: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0.65),
      rgba(255, 255, 255, 0)
    )
    1 1;
  clip-path: inset(0 round 12px);
  background: linear-gradient(0deg, #fff4ef 40%, #ffe4da 100%);
  border-bottom: none;
}
</style>
