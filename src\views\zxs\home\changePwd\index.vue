<template>
  <div>
    <g-navbar />
    <div
      class="p-24px !pb-0 form-container"
      :style="{
        minHeight: `calc(100vh - ${Math.ceil($g.navBarTotalHeight)}px)`,
      }"
    >
      <van-form ref="formRef" class="" label-width="0" @submit="submit">
        <div class="text-15px mb-24px">
          账号：{{ userInfo?.studentDetail?.idNum }}
        </div>

        <div class="text-12px mb-8px text-[#999]">原密码</div>
        <van-field
          v-model.trim="formOptions.data.oldPassword"
          required
          type="text"
          name="原密码"
          label=""
          placeholder="请输入原密码"
          maxlength="16"
          :border="false"
          class="mb-30px border border-[#F1F1F2] rounded-[8px]"
          clearable
          :rules="[
            {
              required: true,
              message: '请输入必填项',
              trigger: ['onBlur', 'onSubmit'],
            },
          ]"
          @end-validate="validatePwd($event, 'oldPassword')"
        />
        <div
          v-if="formOptions.error.oldPassword"
          class="text-12px -mt-26px text-[#FF6E26]"
        >
          {{ formOptions.error.oldPassword }}
        </div>

        <div class="text-12px my-8px text-[#999]">新密码</div>
        <van-field
          v-model.trim="formOptions.data.newPassword"
          required
          type="text"
          name="新密码"
          label=""
          placeholder="密码8-16位，数字、字母、特殊符号!@#."
          maxlength="16"
          :border="false"
          class="mb-30px border border-[#F1F1F2] rounded-[8px]"
          clearable
          :rules="[
            {
              required: true,
              message: '请输入必填项',
              validator: (val: string) => {
                const reg =
                  /^(?=.*[A-Za-z])(?=.*[0-9]).{8,16}|(?=.*[A-Za-z])(?=.*[!@#.]).{8,16}|(?=.*[0-9])(?=.*[!@#.]).{8,16}|(?=.*[0-9])(?=.*[A-Za-z])(?=.*[!@#.]).{8,16}$/

                if (!reg.test(val)) {
                  return '密码8-16位，数字、字母、特殊符号!@#.至少两种'
                }
                return true
              },
              trigger: ['onBlur', 'onSubmit'],
            },
          ]"
          @update:model-value="
            (val) => {
              formOptions.data.newPassword = val.replace(/\s/g, '')
            }
          "
          @end-validate="validatePwd($event, 'newPassword')"
        />
        <div
          v-if="formOptions.error.newPassword"
          class="text-12px -mt-26px text-[#FF6E26]"
        >
          {{ formOptions.error.newPassword }}
        </div>
        <div class="text-12px my-8px text-[#999]">确认密码</div>
        <van-field
          v-model.trim="formOptions.data.newPasswordConfirm"
          required
          type="text"
          name="确认密码"
          label=""
          placeholder="密码8-16位，数字、字母、特殊符号!@#."
          maxlength="16"
          :border="false"
          class="border border-[#F1F1F2] rounded-[8px] mb-38px"
          clearable
          :rules="[
            {
              required: true,
              message: '请输入必填项',
              validator: (val: string) => {
                if (val !== formOptions.data.newPassword) {
                  return '两次输入的密码不一致'
                }
                return true
              },
              trigger: ['onBlur', 'onSubmit'],
            },
          ]"
          @end-validate="validatePwd($event, 'newPasswordConfirm')"
        />
        <div
          v-if="formOptions.error.newPasswordConfirm"
          class="text-12px text-[#FF6E26] -mt-34px mb-16px"
        >
          {{ formOptions.error.newPasswordConfirm }}
        </div>
      </van-form>

      <!-- 提交按钮 -->
      <van-button
        type="primary"
        class="w-full h-[48px] border-none text-16px"
        round
        style="background: linear-gradient(315deg, #ff360c 0%, #ff8126 100%)"
        :disabled="btnPermission"
        @click="submit"
      >
        提交
      </van-button>
    </div>
  </div>
</template>
<script setup lang="ts">
import { updatePassword } from '@/api/home'
import { useUserStore } from '@/stores/modules/user'

const { userInfo } = useUserStore()
const router = useRouter()
const formRef = $ref<any>(null)
const formOptions = reactive<any>({
  data: {
    oldPassword: '',
    newPassword: '',
    newPasswordConfirm: '',
  },
  error: {
    oldPassword: '',
    newPassword: '',
    newPasswordConfirm: '',
  },
})
let btnPermission = $ref(false)

function validatePwd({ status, message }, type) {
  formOptions.error[type] = message
  if (type == 'oldPassword' && formOptions.error.oldPassword == '原密码错误') {
    formOptions.error.oldPassword = ''
  }
}

async function submit() {
  try {
    await formRef?.validate()
    btnPermission = true
    const { oldPassword, newPassword, newPasswordConfirm } = formOptions.data

    await updatePassword({
      password: oldPassword,
      newPassword,
      confirmPassword: newPasswordConfirm,
    })
    $g.showToast({
      message: '密码已修改，登录失效，请重新登录',
      duration: 3000,
    })
    useUserStore().resetAll()
    setTimeout(() => {
      $g.tool.isPCTest()
        ? router.replace({ name: 'Debugging' })
        : $g.flutter('closeWebView')
    }, 3000)
  } catch (err: any) {
    btnPermission = false

    if (err.data?.code === 400) {
      // 触发密码字段的校验错误
      formOptions.error.oldPassword = '原密码错误'
    }
  }
}

onMounted(() => {
  $g.flutter('keyboardResize', false)
})
onBeforeUnmount(() => {
  $g.flutter('keyboardResize', true)
})
</script>

<style lang="scss" scoped>
.form-container {
  :deep() {
    .van-field__error-message {
      display: none;
    }
  }
}
</style>
