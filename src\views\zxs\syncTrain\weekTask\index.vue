<template>
  <div>
    <g-navbar :customTitle="route.query.title || '同步训练'" class="wt-bar" />
    <div class="px-16px py-12px">
      <div class="flex items-start text-14px text-[#333] mb-12px">
        <div class="flex-shrink-0">任务名称：</div>
        <g-mathjax :text="progressTitle" />
      </div>
      <div
        v-if="teachingProgress?.weekIndex"
        class="h-20px leading-[20px] text-14px text-[#333] mb-12px"
      >
        任务时间：第{{ teachingProgress?.weekIndex }}周 （{{
          getTime(teachingProgress?.beginTime || route.query.beginTime)
        }}-{{ getTime(teachingProgress?.endTime || route.query.endTime) }}）
      </div>
      <g-loading v-if="showLoading" class="h-200px"></g-loading>
      <template v-else>
        <g-empty
          v-if="
            taskList.length === 0 &&
            courseInfo.courseVideoList.length === 0 &&
            courseInfo.fileList.length === 0
          "
        ></g-empty>
        <template v-else>
          <div
            v-for="item in taskList"
            :key="item.homeworkTaskId"
            class="w-full rounded-[12px] bg-white border border-solid border-white mb-16px"
          >
            <div class="rounded-[12px_12px_0_0] bg-br h-48px px-12px pt-16px">
              <img
                src="@/assets/img/syncTrain/zy.png"
                alt="zy icon"
                class="w-51px h-25px object-contain"
              />
            </div>
            <div class="px-12px pb-12px">
              <div class="text-12px text-[#666666] text-justify">
                <span
                  v-if="item.homeworkTaskDescribe"
                  v-html="item.homeworkTaskDescribe"
                ></span>
                <span v-else>暂无描述</span>
              </div>
              <template v-for="(task, index) in item.taskList" :key="index">
                <div
                  class="flex items-center pb-13px border-b border-solid border-[#EEEEEE] mt-13px mb-11px"
                >
                  <img
                    src="@/assets/img/syncTrain/circle.png"
                    alt="circle icon"
                    class="w-18px h-18px object-contain mr-6px"
                  />
                  <div class="flex items-center text-14px text-[#333333]">
                    任务{{ numberToChinese(index + 1) }}
                  </div>
                </div>
                <div
                  class="px-8px h-32px rounded-[8px] bg-[#F9F9F9] flex items-center justify-between"
                >
                  <div class="w-142px mr-18px overflow-hidden">
                    <g-mathjax
                      :text="task.paperName"
                      class="whitespace-nowrap"
                    />
                  </div>
                  <div
                    v-if="task.isAttach"
                    class="flex items-center van-haptics-feedback"
                    @click="checkAttach(task)"
                  >
                    <div class="text-12px text-[#3496FA]">查看</div>
                    <img
                      src="@/assets/img/syncTrain/arrow-right-blue.png"
                      alt="arrow icon"
                      class="w-16px h-16px object-contain"
                    />
                  </div>
                  <template v-else-if="task.isSelf || task.isMicro">
                    <div
                      class="flex items-center mr-12px van-haptics-feedback"
                      @click="goStudy(task)"
                    >
                      <div class="text-12px text-[#3496FA]">去训练</div>
                      <img
                        src="@/assets/img/syncTrain/arrow-right-blue.png"
                        alt="arrow icon"
                        class="w-16px h-16px object-contain"
                      />
                    </div>
                    <div
                      class="flex items-center van-haptics-feedback"
                      @click="goStudy(task)"
                    >
                      <div class="text-12px text-[#3496FA]">查看</div>
                      <img
                        src="@/assets/img/syncTrain/arrow-right-blue.png"
                        alt="arrow icon"
                        class="w-16px h-16px object-contain"
                      />
                    </div>
                  </template>
                  <template v-else>
                    <div
                      v-if="!task.isComplete"
                      class="flex items-center van-haptics-feedback"
                      @click="startTrainingApi(task)"
                    >
                      <div class="text-12px text-[#3496FA]">去训练</div>
                      <img
                        src="@/assets/img/syncTrain/arrow-right-blue.png"
                        alt="arrow icon"
                        class="w-16px h-16px object-contain"
                      />
                    </div>
                    <template v-else>
                      <div
                        class="flex items-center mr-12px van-haptics-feedback"
                        @click="checkReport(task)"
                      >
                        <div class="text-12px text-[#3496FA]">查看报告</div>
                        <img
                          src="@/assets/img/syncTrain/arrow-right-blue.png"
                          alt="arrow icon"
                          class="w-16px h-16px object-contain"
                        />
                      </div>
                      <div
                        class="flex items-center van-haptics-feedback"
                        @click="startTrainingApi(task)"
                      >
                        <div class="text-12px text-[#3496FA]">再次训练</div>
                        <img
                          src="@/assets/img/syncTrain/arrow-right-blue.png"
                          alt="arrow icon"
                          class="w-16px h-16px object-contain"
                        />
                      </div>
                    </template>
                  </template>
                </div>
              </template>
            </div>
          </div>
          <div
            v-if="courseInfo.courseVideoList.length"
            class="w-full rounded-[12px] bg-white border border-solid border-white mb-16px"
          >
            <div
              class="rounded-[12px_12px_0_0] bg-br h-52px px-12px pt-16px border-b border-solid border-[#EEEEEE]"
            >
              <img
                src="@/assets/img/syncTrain/wk.png"
                alt="wk icon"
                class="w-51px h-25px object-contain"
              />
            </div>
            <div class="p-10px">
              <div
                v-for="(item, idx) in courseInfo.courseVideoList"
                :key="idx"
                class="px-8px h-32px rounded-[8px] bg-[#F9F9F9] flex items-center justify-between van-haptics-feedback"
                :class="{
                  'mt-10px': idx !== 0,
                }"
                @click="clickItem(item)"
              >
                <div class="w-250px truncate mr-18px">{{ item.fileName }}</div>
                <img
                  src="@/assets/img/syncTrain/arrow-right-gray.png"
                  alt="arrow icon"
                  class="w-16px h-16px object-contain"
                />
              </div>
            </div>
          </div>
          <div
            v-if="courseInfo.fileList.length"
            class="w-full rounded-[12px] bg-white border border-solid border-white mb-16px"
          >
            <div
              class="rounded-[12px_12px_0_0] bg-br h-52px px-12px pt-16px border-b border-solid border-[#EEEEEE]"
            >
              <img
                src="@/assets/img/syncTrain/dxa.png"
                alt="dxa icon"
                class="w-51px h-25px object-contain"
              />
            </div>
            <div class="p-10px">
              <div
                v-for="(item, idx) in courseInfo.fileList"
                :key="idx"
                class="px-8px h-32px rounded-[8px] bg-[#F9F9F9] flex items-center justify-between van-haptics-feedback"
                :class="{
                  'mt-10px': idx !== 0,
                }"
                @click="toDetail(item)"
              >
                <div class="w-250px truncate mr-18px">{{ item.fileName }}</div>
                <img
                  src="@/assets/img/syncTrain/arrow-right-gray.png"
                  alt="arrow icon"
                  class="w-16px h-16px object-contain"
                />
              </div>
            </div>
          </div>
        </template>
      </template>
    </div>
    <TaskPop v-if="showPop" v-model:show="showPop" :info="videoInfo"></TaskPop>
  </div>
</template>

<script setup lang="ts">
import TaskPop from '../components/TaskPop.vue'
import {
  getWeekPlan,
  getTaskListPage,
  setAttachStatus,
  startTraining,
  getCourseFileList,
} from '@/api/syncTrain'

const route = useRoute()
const router = useRouter()

let showLoading = $ref(true)

let showPop = $ref(false)
let videoInfo = $ref<any>({})

let teachingProgress = $ref<any>(null)
let taskList = $ref<any[]>([])
let courseInfo = $ref<any>({
  courseVideoList: [],
  fileList: [],
})

const progressTitle = $computed(() => {
  if (!teachingProgress) return '无'
  if (
    teachingProgress?.sysTextbooksCatalogNameAlias &&
    teachingProgress?.jztBookCatalogName
  ) {
    return (
      teachingProgress.sysTextbooksCatalogNameAlias +
      ' | ' +
      teachingProgress.jztBookCatalogName
    )
  } else {
    return (
      teachingProgress?.sysTextbooksCatalogNameAlias ||
      teachingProgress?.jztBookCatalogName ||
      '无'
    )
  }
})

async function getWeekPlanApi() {
  const data = await getWeekPlan({
    schoolPhaseWeekId: route.query.schoolPhaseWeekId,
    sysSubjectId: route.query.sysSubjectId,
  })
  teachingProgress = data
}

function getTime(val) {
  if (!val) return ''
  return val.replace(/-/g, '/')
}

async function getTaskListPageApi() {
  const { list } = await getTaskListPage({
    sysSubjectId: route.query.sysSubjectId,
    beginDate: route.query.beginTime,
    endDate: route.query.endTime,
    page: 1,
    pageSize: 999,
  })
  taskList =
    list?.map((v) => {
      let arr = [] as any
      v.paperList?.forEach((val) => {
        const newPaperItem = {
          ...val,
          paperName: `${val.paperName}${
            val.accuracyRate !== null ? ' 【' + val.accuracyRate + '%】' : ''
          }`,
        }
        arr.push(newPaperItem)
      })
      if (v.selfStudy) {
        const newStudyItem = {
          ...v.selfStudy,
          paperName: v.selfStudy.name,
          isSelf: true,
        }
        arr.push(newStudyItem)
      }
      if (v.microCourse) {
        const newCourseItem = {
          ...v.microCourse,
          paperName: v.microCourse.name,
          isMicro: true,
        }
        arr.push(newCourseItem)
      }
      v.jztPaperList?.forEach((val) => {
        const newJZTItem = {
          ...val,
          paperName: `【AI教辅】${val.paperName}${
            val.accuracyRate !== null ? ' 【' + val.accuracyRate + '%】' : ''
          }`,
        }
        arr.push(newJZTItem)
      })
      v.attachList?.forEach((val, i) => {
        arr.push({
          ...val,
          paperName: `附件${i + 1} ${val.attachName}`,
          isAttach: true,
        })
      })
      return {
        ...v,
        taskList: arr,
      }
    }) || []
}

function numberToChinese(num) {
  const chineseNumbers = [
    '零',
    '一',
    '二',
    '三',
    '四',
    '五',
    '六',
    '七',
    '八',
    '九',
  ]
  const unitNames = ['', '十', '百', '千']

  if (num === 0) {
    return chineseNumbers[0]
  }

  let result = ''
  let unitIndex = 0

  while (num > 0) {
    const digit = num % 10
    if (digit > 0 || result !== '') {
      result = chineseNumbers[digit] + unitNames[unitIndex] + result
    }

    unitIndex++
    num = Math.floor(num / 10)
  }

  return result
}

/* 附件预览 */
async function checkAttach(row) {
  await setAttachStatus({
    homeworkTaskId: row.homeworkTaskId,
    homeworkTaskAttachId: row.homeworkTaskAttachId,
  })
  if (!row.completeTime) {
    row.completeTime = $g.dayjs().format('YYYY-MM-DD HH:mm:ss')
  }
  row.isComplete = true
  const suffix = row.attachSuffix?.toLowerCase()
  if (!suffix) return
  if (
    ['mp4', 'avi', 'rmvb', 'flv', 'wmv', 'mkv', 'mov', 'm3u8', 'mp3'].includes(
      suffix,
    )
  ) {
    $g.flutter('playVideo', {
      title: row.attachName,
      resource_url: row.attachUrl,
    })
  } else if (
    ['jpg', 'png', 'jpeg', 'heic', 'heif', 'gif', 'webp', 'svg'].includes(
      suffix,
    )
  ) {
    $g.flutter('previewImage', {
      urls: [row.attachUrl],
      index: 0,
    })
  } else {
    router.push({
      name: 'PreviewDoc',
      query: {
        url: row.attachUrl,
        activeName: 'MyTaskIndex',
      },
    })
  }
}

/* 自主学习/微课程 */
function goStudy(row) {
  if (row.isSelf) {
    router.push({
      name: 'TaskAuto',
      query: {
        homeworkTaskId: row.homeworkTaskId,
      },
    })
  } else {
    router.push({
      name: 'TaskMicro',
      query: {
        homeworkTaskId: row.homeworkTaskId,
      },
    })
  }
}

/* 去训练/再次训练 */
async function startTrainingApi(row) {
  try {
    const data = await startTraining({
      homeworkTaskPaperId: row.homeworkTaskPaperId,
    })
    if (data) {
      router.push({
        name: 'DoTask',
        query: {
          homeworkTaskPaperId: row.homeworkTaskPaperId,
          ownTestRecordId: data,
          paperId: row.paperId,
          sysSubjectId: route.query.sysSubjectId,
          examId: row.examId,
          resourceType: row?.resourceType,
        },
      })
    }
  } catch (e) {
    console.log(e)
  }
}

/* 查看报告 */
function checkReport(row) {
  router.push({
    name: 'TaskReport',
    query: {
      homeworkTaskId: row.homeworkTaskId,
      ownTestRecordId: row.ownTestRecordId,
      paperId: row.paperId,
      sysSubjectId: route.query.sysSubjectId,
      examId: row.examId,
      resourceType: row?.resourceType,
    },
  })
}

async function getCourseFileListApi() {
  const data = await getCourseFileList({
    sysSubjectId: route.query.sysSubjectId,
    schoolPhaseWeekId: route.query.schoolPhaseWeekId,
  })
  if (data) {
    courseInfo = data
  }
}

function clickItem(item) {
  videoInfo = item
  showPop = true
}

/* 跳转导学案 */
function toDetail(item) {
  router.push({
    name: 'SyncTrainPreview',
    query: {
      newChapterResourceId: item.newChapterResourceId,
    },
  })
}

onBeforeMount(async () => {
  try {
    showLoading = true
    await getWeekPlanApi()
    await getTaskListPageApi()
    await getCourseFileListApi()
    showLoading = false
    await nextTick()
    $g.tool.renderMathjax()
  } catch (e) {
    showLoading = false
    console.error(e)
  }
})
</script>

<style lang="scss" scoped>
.bg-br {
  background: linear-gradient(180deg, #e3f2ff 0%, rgba(255, 255, 255, 0.5) 100%),
    #ffffff;
}
</style>
