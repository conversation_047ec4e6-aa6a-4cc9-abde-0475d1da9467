<template>
  <g-loading v-show="showLoading" class="h-200px"></g-loading>
  <iframe
    v-show="!showLoading"
    :key="src"
    :src="calSrc"
    frameborder="0"
    class="w-full h-full"
    @load="showLoading = false"
  ></iframe>
</template>

<script setup lang="ts">
const props = defineProps<{
  src: string
}>()

let showLoading = $ref(false)

const isImg = $computed(() => {
  const ext: any = $g.tool.getExt(props.src)
  return ['jpg', 'jpeg', 'gif', 'png', 'gif', 'webp', 'svg'].includes(ext)
})

const calSrc = $computed(() => {
  if (isImg) {
    return props.src
  }
  return '//vip.ow365.cn/?i=28777&ssl=1&furl=' + props.src
})

watch(
  () => props.src,
  () => {
    showLoading = true
  },
  {
    immediate: true,
  },
)
</script>

<style lang="scss" scoped></style>
