import { useUserStore } from '@/stores/modules/user'
import { useRouterStore } from '@/stores/modules/router'
import { openZixishi } from '@/api/common'

/**
 * @description 路由守卫
 */
// 是否显示初始loading
let isShowLoading = true
// 是否第一次进入项目页面
let isOncePage = true
export function setupPermissions(router) {
  router.beforeEach(async (to: any, from: any, next: any) => {
    try {
      versionCheck()
      const userStore = useUserStore()

      if (!to.meta.public) {
        if (!userStore.token || from.path == '/') await userStore.getToken()
        await isOpenZixishi(userStore)
      }

      removeLoading()
      restoreRollingBar(to, from, 1)
      keepAlive(to, from)
      needFullPage(to)
      // 放行
      next()
    } catch (error: any) {
      $g.showToast(error.message)
    }
  })
  router.afterEach((to: any, from) => {
    removeLoading()
    recordPageTime('进入路由')
    restoreRollingBar(to, from, 2)
    nextTick(() => {
      $g.flutter('setStatusBarColor', 'transparent')
    })
  })
  router.onError((error) => {
    console.error('🎃 error ==> ', error)
  })
}

/**
 * 描述 keepalive滚动条恢复
 * @param {any} to
 * @param {any} from
 */

function restoreRollingBar(to, from, mode = 1) {
  const routerStore = useRouterStore()
  const App = document.getElementById('app') as HTMLElement
  if (!App) return
  if (mode == 1) {
    // 是否需记录滚动条位置
    if (from.meta.keepAlive || from.meta.keepAliveArr?.includes(to.name)) {
      routerStore.changeMenuMeta({
        name: from.name,
        meta: {
          scrollTop: App?.scrollTop || 0,
        },
      })
    }
  } else if (mode == 2) {
    nextTick(() => {
      if (to.meta.keepAlive && to.meta?.scrollTop) {
        App.scrollTop = to.meta?.scrollTop
      } else {
        App.scrollTop = 0
      }
    })
  }
}

/* 版本监控 */
// async function versionCheck() {
//   if (import.meta.env.VITE_APP_ENV.includes('development')) return false
//   window.addEventListener('vite:preloadError', (event) => {
//     $g.showDialog({
//       title: '更新提示',
//       message: '发现新内容，为了更好的体验，请点击确认刷新当前页面',
//       theme: 'round-button',
//     }).then(() => {
//       window.location.reload() // 例如，刷新页面
//       $g.flutter('clearCache', { reload: true })
//     })
//   })
// }
async function versionCheck() {
  if (import.meta.env.VITE_APP_ENV.includes('development')) return false
  const response = await axios.get(
    'version.json?timestamp=' + new Date().getTime(),
  )
  const vueVersion = response?.data?.version
  const localVueVersion = localStorage.getItem('vueVersion')
  localStorage.setItem('vueVersion', vueVersion)
  if ((localVueVersion || __APP_VERSION__) != vueVersion) {
    $g.showDialog({
      title: '更新提示',
      message: '发现新内容，为了更好的体验，请点击确认刷新当前页面',
      theme: 'round-button',
    }).then(() => {
      window.location.reload() // 例如，刷新页面
      $g.flutter('clearCache', { reload: true })
    })
  }
}

/* 指定页面缓存 */
function keepAlive(to: any, from: any) {
  const routerStore = useRouterStore()
  // 用于一些公用预览页面默认缓存当前页面，需要给当前页面添加name
  const defaultCacheRouteArr = ['PreviewDoc']
  routerStore.changeMenuMeta({
    name: from.name,
    meta: {
      keepAlive:
        from.meta.keepAliveArr?.includes(to.name) ||
        defaultCacheRouteArr.includes(to.name),
    },
  })
  routerStore.changeMenuMeta({
    name: to.name,
    meta: {
      keepAlive:
        to.meta.keepAliveArr?.includes(from.name) ||
        defaultCacheRouteArr.includes(from.name),
    },
  })
}

/* 关闭loading */
function removeLoading() {
  // 移除loading
  if (isShowLoading) {
    const appLoadingId = document.getElementById('appLoadingId') as HTMLElement
    appLoadingId?.remove()
    isShowLoading = false
    recordPageTime('关闭loading')
    setTimeout(() => {
      $g.flutter('fullWithStatusBar', true)
    }, 100)
  }
}

function recordPageTime(title) {
  if (isOncePage) {
    const nowTime = new Date().getTime()
    console.log(`${title}耗时`, (nowTime - window._time) / 1000)
    if (title == '进入路由') isOncePage = false
  }
}

function needFullPage(to: any) {
  if (!to.meta.fullPage) {
    const body = document?.querySelector('body')
    body?.classList.add('full-page')
    const top: any = document?.querySelectorAll(
      '.full-page #TopAdaptationDistance',
    )[0]
    top.style.display = 'none'
  } else {
    const body = document?.querySelector('body')
    body?.classList.remove('full-page')
  }
}

/* 是否开通智习室 */
async function isOpenZixishi(userStore) {
  if (!userStore.userInfo?.studentDetail?.idNum) return false
  const res = await openZixishi({
    idNum: userStore.userInfo.studentDetail.idNum,
  })
  if (res.isOpenZixishi == 1) {
    $g.showToast('请开通智习室')
    setTimeout(() => {
      userStore.resetAll()
      $g.flutter('closeWebView')
    }, 2000)
  }
}
