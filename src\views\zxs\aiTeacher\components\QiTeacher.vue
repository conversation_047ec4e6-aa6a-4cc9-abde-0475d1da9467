<template>
  <div class="overflow-hidden flex flex-col h-full relative">
    <div v-show="showLoading" class="absolute w-full z-10 h-full bg-[#f6f8fa]">
      <g-loading class="h-200px"></g-loading>
    </div>
    <div class="flex-grow min-h-[0px] pt-10px relative">
      <g-virtual-list
        :key="key"
        ref="virtualRef"
        item-key="id"
        :list-data="messageList"
        :size-dependencies="['content', 'buttonList']"
        :min-item-size="30"
        :scrollBottom="true"
        @close-loading="closeLoading"
        @scroll-fun="scrollFun"
      >
        <template #default="{ item }">
          <MessageBox
            :item="item"
            :is-audio="isAudio"
            :account-id="props.inputInfo.accountId + ''"
            role="qi"
            @button-click="buttonClick"
          />
        </template>
      </g-virtual-list>
      <div
        v-show="!scrollAuto"
        class="absolute bottom-[10px] van-haptics-feedback w-30px h-30px br-[50%] bg-white left-[50%] flex-cc translate-x-[-50%] inputShadow !border-0"
        @click="scrollBottomFun"
      >
        <g-icon name="ri-arrow-down-line" size="18" color="" />
      </div>
    </div>
    <div
      class="flex justify-center w-full h-54px px-17px bg-[#FAFAFA] flex-shrink-0"
    >
      <div
        class="h-42px px-13px br-[9px] flex items-center inputShadow w-full"
        :class="disableInput ? 'bg-[#f0f0f0]' : 'bg-white'"
      >
        <div class="flex-1 flex items-center text-14px">
          <template v-if="!disableInput">
            <span class="text-[#fcbc00]">我要提问</span>
            <div class="mx-12px border-l-[1px] h-16px border-[#D9D9D9]"></div>
          </template>
          <van-field
            ref="inputRef"
            v-model="inputText"
            class="flex-1 h-40px"
            :placeholder="
              disableInput
                ? '如需开启自由对话，请联系管理员~'
                : '有问题可以随时提问哟'
            "
            :disabled="disableInput"
            @keyup.enter="inputMessage"
          ></van-field>
        </div>
        <div
          v-show="!disableInput"
          class="flex items-center w-23px flex-shrink-0"
          @click="inputMessage"
        >
          <div
            class="w-23px h-23px flex justify-center items-center br-[6px]"
            :class="
              inputText.trim() && !aiLoading
                ? 'bg-[#fcbc00] van-haptics-feedback'
                : 'bg-[#ddd] cursor-not-allowed'
            "
          >
            <img
              :src="$g.tool.getFileUrl('aiTeacher/feiji.png')"
              class="w-17px h-17px"
              fit="contain"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import config from '@/config/index'
import {
  getChatRecord,
  createChat,
  getLastRoomId,
  createRoom,
  getParseList,
  getParseDetail,
  saveStudyNote,
} from '@/api/aiTeacher'
import { useUserStore } from '@/stores/modules/user'
import { slowedStreamFetch, mathCount } from './fetchStream'
import MessageBox from './MessageBox.vue'
import { useSpeakerStore } from '@/stores/modules/speaker'
let speakerStore = useSpeakerStore()

const emit = defineEmits(['saveNote', 'qiChange'])
const props = defineProps({
  //当前小题id
  subQuestionId: {
    type: [Number, String],
    default: 0,
  },
  inputInfo: {
    type: Object,
    default: () => {},
  },
  questionId: {
    type: [Number, String],
    default: 0,
  },
  //删除的笔记
  deleteNoteId: {
    type: [Number, String],
    default: 0,
  },
  //是否开启音频
  isAudio: {
    type: Boolean,
    default: false,
  },
})

const userStore = useUserStore()
const route = useRoute()
let key = $ref($g.tool.uuid(8))
const virtualRef: any = $ref(null)
let baseURL = config.baseURL
//消息列表
let messageList: any = $ref([
  {
    content: '同学你好，接下来将由小启老师为你讲解！请问你准备好了吗?',
    buttonList: [{ name: '准备好了', isClick: false, type: 0 }],
    ai: true,
    id: $g.tool.uuid(8),
  },
])
let buttonFun = { 0: beReady, 1: saveMessage, 3: answer } //按钮点击函数
let aiLoading = $ref(false) //ai发送消息中
let roomId = $ref('') //房间id
let chatId = $ref('') //聊天id
let parseChatId = $ref('') //解法id
let questionParseList: any = $ref([]) //子题解法列表
let currentParse: any = $ref({}) //当前解法
let showLoading = $ref(true)
let needExplain = $ref(false) //是否需要对接数据库的解析步骤
let once = $ref(true) //AI是否第一次讲话
let parseDetail: any = $ref([]) //数据库中的ai解释
let inputText = $ref('') //用户输入
let scrollAuto = $ref(true) //是否自动滚动
let isSaveMessage = $ref(true)
let scrollTop = $ref(0)
const router = useRouter()
let response: any = $ref(null)
let lastId = $ref('') //接受消息后面拼接的存储id
let disableInput = $ref(false) //是否开启对话

watch(
  () => props.subQuestionId,
  (val) => {
    if (val && val != 0) {
      key = $g.tool.uuid(8)
      initData()
    }
  },
)

watch(
  () => props.inputInfo,
  (val) => {
    disableInput = val.dialogState == 2
  },
)

watch(
  () => props.deleteNoteId,
  (val) => {
    if (val) {
      let obj = messageList.find((item) => item.fastGptChatRecordId == val)
      let button = obj.buttonList.find((item) => item.type == 1)
      if (button) button.isClick = false
    }
  },
)

function closeLoading() {
  showLoading = false
}

//初始化
async function initData() {
  showLoading = true
  aiLoading = false
  emit('qiChange', false)
  chatId = ''
  parseChatId = ''
  questionParseList = []
  currentParse = {}
  once = true
  parseDetail = []
  needExplain = false
  scrollAuto = true
  isSaveMessage = true
  messageList = [
    {
      content: '同学你好，接下来将由小启老师为你讲解！请问你准备好了吗?',
      buttonList: [
        {
          name: '准备好了',
          isClick: false,
          type: 0,
        },
      ],
      ai: true,
      id: $g.tool.uuid(8),
    },
  ]
  await getParseListApi()
  getChatRecordApi()
}

function buttonClick(v, index) {
  if (v.isClick) return
  if (aiLoading && v.type != 1) return
  v.isClick = true
  buttonFun[v.type](v, index)
}

//获取解法步骤详情
async function getParseDetailApi(index, id) {
  const data = await getParseDetail({ subQuestionParseId: id })
  parseDetail[index] = data
}

//数据库解法步骤打印
function writing(text) {
  let timer: any = ''
  let count = 1
  let addCount = 1
  const timeFunc = () => {
    if (count < text.length + addCount && aiLoading) {
      messageList[messageList.length - 1].content = text.substring(0, count) //截取字符串
      scrollBottom()
      count += addCount
      addCount = mathCount(messageList[messageList.length - 1].content)
      setTimeout(timeFunc, 25)
    } else {
      if (timer) clearTimeout(timer)
      if (!aiLoading) return
      aiLoading = false
      messageList[messageList.length - 1].speakLoading = false
      emit('qiChange', false)
      emitNote()
    }
  }
  timer = setTimeout(timeFunc, 25)
}
//数据库是否有解析步骤处理
function setParseDetail(index, text) {
  if (!parseDetail.length) return
  aiLoading = true
  emit('qiChange', true)
  messageList.push({
    content: '',
    ai: true,
    loading: true,
    id: $g.tool.uuid(8),
    speakLoading: true,
    subQuestionParseId: currentParse.subQuestionParseId,
  })
  if (parseDetail[index]?.aiExplain) {
    messageList[messageList.length - 1].loading = false
    writing(parseDetail[index]?.aiExplain)
    needExplain = true
    isSaveMessage = false
  } else {
    aiLoading = false
    needExplain = false
    sendMessage({ str: text, parse: false, aiExplain: true })
  }
}

//获取题目上次id
async function getLastId() {
  try {
    showLoading = true
    let data = await getLastRoomId({
      questionId: props.questionId,
      type: 2,
      accountId: props.inputInfo.accountId,
    })
    roomId = data
    if (roomId) return
    await createRoomId()
  } catch (error) {
    console.log('error => ', error)
    showLoading = false
  }
}

//房间Id
async function createRoomId() {
  try {
    let data = await createRoom({
      questionId: props.questionId,
      type: 2,
      accountId: props.inputInfo.accountId,
    })
    roomId = data
  } catch (error) {
    console.log('error => ', error)
    showLoading = false
  }
}

//新建聊天/解析Id
async function createChatId(isChat, parseId = '') {
  let query = {
    type: isChat ? 'PROCEDURE' : 'PARSE',
    questionId: props.questionId,
    fastGptChatRoomId: roomId,
    subQuestionId: props.subQuestionId,
    accountId: props.inputInfo.accountId,
  }
  if (isChat) query['subQuestionParseId'] = parseId
  let data = await createChat(query)
  if (isChat) {
    chatId = data
  } else {
    parseChatId = data
  }
}

//获取解法列表接口
async function getParseListApi() {
  try {
    let data = await getParseList({ subQuestionId: props.subQuestionId })
    questionParseList = data.map((item, index) => {
      return {
        ...item,
        name: `解法${$g.tool.numberToChinese(index + 1)}`,
        isClick: false,
        type: 3,
      }
    })
    if (questionParseList.length > 4)
      questionParseList = questionParseList.slice(0, 5)
    //获取每个解析的讲解步骤
    let length = questionParseList.length
    for (let i = 0; i < length; i++) {
      await getParseDetailApi(i, questionParseList[i].subQuestionParseId)
    }
  } catch (error) {
    console.log('error => ', error)
    showLoading = false
  }
}

//ai对话之外的数据添加 ：用户解析提问、数据库解析步骤
function insertMessage(item, vItem) {
  let index = questionParseList.findIndex(
    (v) => v.subQuestionParseId == item.subQuestionParseId,
  )
  //解析数据大于1时 添加用户解法提问
  if (questionParseList.length > 1) {
    messageList.push({
      content: `我选择解法${$g.tool.numberToChinese(index + 1)}`,
      //ai回答增加按钮
      buttonList: [],
      ai: false,
      id: $g.tool.uuid(8),
    })
  }
  //解析背景添加
  if (questionParseList[index]?.backgroundMaterials) {
    messageList.push({
      content: questionParseList[index].backgroundMaterials,
      //ai回答增加按钮
      buttonList: [],
      ai: true,
      id: $g.tool.uuid(8),
    })
  }
  messageList.push({
    content: parseDetail[index]?.aiExplain || '',
    //ai回答增加按钮
    buttonList: [],
    ai: true,
    id: $g.tool.uuid(8),
    fastGptChatRecordId: vItem?.fastGptChatRecordId || '',
    subQuestionParseId: questionParseList[index]?.subQuestionParseId,
  })
}

//消息回显处理
function messageSet(vItem, item, vIndex) {
  let obj: any = {
    content: vItem.message,
    //ai回答增加按钮
    buttonList: messageButton(item, vItem),
    ai: vItem.chatType == 1 ? false : true,
    id: $g.tool.uuid(8),
    fastGptChatRecordId: vItem.fastGptChatRecordId,
  }
  //ai的解法讲解记录
  if (item.type == 2 && vItem.chatType == 2) {
    messageList.push(obj)
    return
  } else if (item.type == 3) {
    //步骤讲解
    //数据库有讲解 则第一条数据不为空，数据库无讲解，第一条数据为空
    if (!item.list[0].message) {
      //前两条数据不展示
      if (vIndex == 0) {
        insertMessage(item, vItem)
        return
      } else if (vIndex > 1) {
        //后面数据正常拼接
        messageList.push(obj)
      }
    } else {
      //第一条数据不为空，在添加第一条数据时，自动添加一个或两个数据
      if (vIndex == 0) {
        insertMessage(item, vItem)
      }
      messageList.push(obj)
    }
  }
}

//消息回显按钮处理
function messageButton(item, vItem) {
  if (item.subQuestionParseId) {
    questionParseList.forEach((v) => {
      if (v.subQuestionParseId == item.subQuestionParseId) v.isClick = true
    })
  }
  if (item.type == 2 && vItem.chatType == 2) {
    //解法按钮回显
    return questionParseList
  } else if (vItem.chatType == 2) {
    //保存笔记按钮回显
    return [
      {
        name: '保存笔记',
        isClick: !vItem.addStudyNote ? false : true,
        type: 1,
        lastId: vItem.fastGptChatRecordId,
      },
    ]
  } else {
    return []
  }
}

//获取聊天记录
async function getChatRecordApi() {
  try {
    emitNote()
    //记录
    let data = await getChatRecord({
      fastGptChatRoomId: roomId,
      subQuestionId: props.subQuestionId,
      accountId: props.inputInfo.accountId,
    })
    //防止迅速点击小题后数据混乱
    if (messageList.length > 1) {
      messageList = [messageList[0]]
      messageList[0].buttonList[0].isClick = false
    }
    if (!data || !data.length) {
      showLoading = false
      return
    }
    isSaveMessage = false
    messageList.push({
      content: '我已经准备好，请老师开始进行讲解',
      //ai回答增加按钮
      buttonList: [],
      ai: false,
      id: $g.tool.uuid(8),
    })
    messageList[0].buttonList[0].isClick = true
    currentParse = questionParseList.find(
      (item) =>
        item.subQuestionParseId == data[data.length - 1].subQuestionParseId,
    )
    chatId = data[data.length - 1].chatId
    once = false
    data.forEach((item) => {
      //解法是数据库中并且没有其他操作
      if (!item.list?.length) {
        questionParseList.forEach((v) => {
          if (v.subQuestionParseId == item.subQuestionParseId) v.isClick = true
        })
        insertMessage(item, {})
        needExplain = true
        once = true
      } else {
        item.list.forEach((vItem, vIndex) => {
          messageSet(vItem, item, vIndex)
        })
      }
    })
  } catch (error) {
    showLoading = false
    console.log('error => ', error)
  } finally {
    setTimeout(() => {
      scroller = document.querySelector('.vue-recycle-scroller')
      if (scroller) scroller.addEventListener('touchmove', touchMove)
    }, 10)
  }
}

//消息发送与接收
async function sendMessage(obj) {
  const { str = '', parse = false, explain = false } = obj
  try {
    lastId = ''
    if (aiLoading) return
    aiLoading = true
    emit('qiChange', true)
    const title = str != '' ? str : '我要提问' + inputText
    inputText = ''
    //接收消息加载
    if (!messageList[messageList.length - 1]?.loading) {
      messageList.push({
        content: '',
        ai: true,
        loading: true,
        id: $g.tool.uuid(8),
        speakLoading: true,
        subQuestionParseId: explain ? currentParse.subQuestionParseId : '',
      })
    }
    scrollBottom()
    response = null
    //parse为true使用解法接口，否则使用聊天接口
    let url = parse
      ? `${baseURL}/v3/student/aiChat/chat/question/parse`
      : `${baseURL}/v3/student/aiChat/chat/question/procedure`
    //fetch实现
    const decodeFunc = (value) => {
      let result = ''
      try {
        if (value.data && value.data !== '[DONE]') {
          const data = JSON.parse(value.data)
          if (value.data.includes('fastGptChatRecordId')) {
            lastId =
              JSON.parse(data?.choices[0]?.delta?.content)
                .fastGptChatRecordId ?? ''
            messageList[messageList.length - 1].fastGptChatRecordId = lastId
          } else if (value.data.includes('canSend')) {
            disableInput = true
            return 'canSend'
          } else {
            if (data?.message && data?.message.includes('存在政治敏感不合规')) {
              result = data?.message ?? ''
            } else if (data?.choices?.[0]) {
              result = data.choices[0]?.delta?.content ?? ''
            }
          }
        }
      } catch (error) {
        console.log(`JSON parse error: ${error}, value: ${value}`)
      }
      return result
    }
    const headers = {
      'Content-Type': 'application/json;charset=UTF-8',
      Accept: 'text/event-stream',
      token: userStore.token,
      platform: 'STUDENT_H5',
    }
    response = await slowedStreamFetch(
      fetch(url, {
        method: 'post',
        headers,
        body: JSON.stringify({
          chatId: parse ? parseChatId : chatId,
          questionId: props.questionId,
          subQuestionId: props.subQuestionId,
          subQuestionParseId: currentParse.subQuestionParseId,
          begin: once,
          message: title,
          needExplain,
          accountId: props.inputInfo.accountId,
        }),
      }),
      10,
      decodeFunc,
    )
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    dataInit(response, parse)
  } catch (error: any) {
    console.log('⚡ error ==> ', error)
    let index = messageList.length - 1
    messageList[index].loading = false
    messageList[index].speakLoading = false
    aiLoading = false
    emit('qiChange', false)
    scrollBottom()
  }
}

//消息数据处理
async function dataInit(response, parse) {
  let index = messageList.length - 1
  const stream = response.body
  const reader = stream.getReader()
  let count = 1
  let result: any = {}
  //存储字符串
  let textArr: any = ''
  let addCount = 1
  messageList[index].loading = false
  while (!(result = await reader.read()).done && aiLoading) {
    if (!aiLoading) break
    if (count === 1) {
      messageList[index].content = result.value
    } else {
      textArr += result.value
      //字符串长度大于字符数时加入对话中
      if (textArr.length >= addCount) {
        messageList[index].content += textArr
        textArr = []
      }
      //计算添加字符数
      addCount = mathCount(messageList[index].content)
    }
    count++
    scrollBottom()
  }
  if (!aiLoading) return
  messageList[index].content += textArr
  aiLoading = false
  emit('qiChange', false)
  if (messageList[index].content == 'canSend') {
    messageList[index].buttonList = []
    messageList[index].content = '如需开启自由对话，请联系管理员~'
  } else {
    if (parse) {
      messageList[index].buttonList = $g._.cloneDeep(questionParseList)
    } else if (
      !isSaveMessage &&
      messageList[messageList.length - 1].content != '存在政治敏感不合规' &&
      messageList[messageList.length - 1].content
    ) {
      //第一个讲解步骤笔记已经默认保存
      messageList[index].buttonList = [
        {
          name: '保存笔记',
          isClick: false,
          type: 1,
          lastId: lastId,
        },
      ]
    }
  }
  messageList[messageList.length - 1].speakLoading = false
  if (isSaveMessage) emitNote()
  once = false
  isSaveMessage = false
  needExplain = false
  scrollBottom()
}

//解法背景输出
function parseBkgdInput(content, index, text) {
  aiLoading = true
  emit('qiChange', true)
  messageList.push({
    content: '',
    buttonList: [],
    ai: true,
    id: $g.tool.uuid(8),
    speakLoading: true,
  })
  let timer: any = ''
  let count = 1
  let addCount = 1
  const timeFunc = async () => {
    if (count < text.length + addCount && aiLoading) {
      messageList[messageList.length - 1].content = text.substring(0, count) //截取字符串
      scrollBottom()
      count += addCount
      addCount = mathCount(messageList[messageList.length - 1].content)
      setTimeout(timeFunc, 25)
    } else {
      if (timer) clearTimeout(timer)
      if (!aiLoading) return
      aiLoading = false
      messageList[messageList.length - 1].speakLoading = false
      await createChatId(true, currentParse?.subQuestionParseId)
      isSaveMessage = true
      once = true
      setParseDetail(index, content)
    }
  }
  timer = setTimeout(timeFunc, 25)
}

//触发笔记获取
function emitNote(open = false) {
  let idObj: any = {
    fastGptChatRoomId: roomId,
    subQuestionId: props.subQuestionId,
    type: 2,
  }
  // if (open) idObj.noteOpen = true
  emit('saveNote', idObj)
}

//保存笔记
async function saveMessage(item) {
  try {
    await saveStudyNote({
      fastGptChatRecordId: item.lastId,
      accountId: props.inputInfo.accountId,
    })
    emitNote(true)
  } catch (error) {
    console.log('error => ', error)
  }
}

function inputMessage() {
  if (!inputText.trim()) return
  //只有一种解法需要点击准备好了，才能自由问答
  if (messageList.length < 2) {
    return $g.showToast({
      message: '请先点击 "准备好了" ！',
    })
  }
  //有一种以上解法需要选则解法后，才能自由问答
  if (
    messageList.length > 2 &&
    messageList.length < 4 &&
    questionParseList.length > 1
  ) {
    return $g.showToast({
      message: '请先选则解法 ！',
    })
  }

  if (aiLoading) return $g.showToast('当前对话进行中，请稍后重试！')
  scrollAuto = true
  messageList.push({
    content: '我要提问' + inputText,
    ai: false,
    id: $g.tool.uuid(8),
  })
  sendMessage({})
}

//准备好了
async function beReady(addMessage = true) {
  if (addMessage) {
    messageList.push({
      content: '我已经准备好，请老师开始进行讲解',
      buttonList: [],
      ai: false,
      id: $g.tool.uuid(8),
    })
  }
  scrollAuto = true
  if (questionParseList.length > 1) {
    await createChatId(false)
    sendMessage({ str: '请开始讲解', parse: true })
    return
  }
  currentParse = questionParseList[0]
  if (currentParse.backgroundMaterials) {
    parseBkgdInput('请开始讲解', 0, currentParse?.backgroundMaterials)
    return
  }
  await createChatId(true, currentParse?.subQuestionParseId)
  setParseDetail(0, '请开始讲解')
}

//解法X
async function answer(item, index) {
  if (aiLoading) return
  currentParse = item
  messageList.push({
    content: `我选择${item.name}`,
    buttonList: [],
    ai: false,
    id: $g.tool.uuid(8),
  })
  scrollAuto = true
  if (currentParse.backgroundMaterials) {
    parseBkgdInput(
      `我选择${item.name}`,
      index,
      currentParse?.backgroundMaterials,
    )
    return
  }
  await createChatId(true, currentParse.subQuestionParseId)
  once = true
  isSaveMessage = true
  scrollBottomFun()
  setParseDetail(index, `我选择${item.name}`)
}

//滚动条自动滚到底部
const scrollBottom = $g._.throttle(function () {
  nextTick(() => {
    if (virtualRef) {
      if (!scrollAuto) return
      virtualRef.scrollToBottom()
    }
  })
}, 400)

let scroller: any = $ref(null)

function scrollFun() {
  if (!scroller) scroller = document.querySelector('.vue-recycle-scroller')
  let heightInterval =
    Math.floor(scroller.scrollHeight) -
    Math.floor(scroller.scrollTop + scroller.clientHeight)
  if (scroller && Math.abs(heightInterval) < 6) {
    scrollAuto = true
    return
  }
}

function scrollBottomFun() {
  virtualRef.scrollToItem()
  scrollAuto = true
}

onBeforeMount(async () => {
  disableInput = props.inputInfo.dialogState == 2
  await getLastId()
  await getParseListApi()
  await getChatRecordApi()
})

//触摸滑动函数
function touchMove() {
  let heightInterval =
    Math.floor(scroller.scrollHeight) -
    Math.floor(scroller.scrollTop + scroller.clientHeight)
  if (heightInterval > 1) {
    scrollAuto = false
  } else {
    scrollAuto = true
  }
}

function jumpDetail() {
  if (showLoading) return $g.showToast('等待数据加载完成！')
  scrollTop = scroller?.scrollTop
  speakerStore.reset()
  router.push({
    name: 'QuestionDetail',
    query: {
      questionId: route.query.questionId,
      errorType: route.query?.errorType ? 2 : null,
    },
  })
}

//清除记录
async function clearMessage() {
  if (aiLoading) {
    $g.showToast('请先等待AI老师回答完毕')
    return
  }
  if (messageList?.length < 2) {
    $g.showToast('请先和老师开始对话')
    return
  }
  if (showLoading) return
  speakerStore.reset()
  showLoading = true
  aiLoading = false
  emit('qiChange', false)
  await createRoomId()
  initData()
}

onActivated(() => {
  nextTick(() => {
    //防止滚动条滚动
    if (scroller && scrollTop) scroller.scrollTop = scrollTop
  })
})

onBeforeUnmount(() => {
  //关闭流
  if (aiLoading) response?.closeStream()
  scroller?.removeEventListener('touchmove', touchMove)
})

defineExpose({ clearMessage, jumpDetail })
</script>

<style lang="scss" scoped>
:deep() {
  .van-field--disabled {
    background-color: transparent;
  }
}

.inputShadow {
  border: 1px solid #eee;
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.05);
}
.note-box {
  :deep() {
    .katex-display {
      .katex {
        white-space: pre-wrap;
      }
    }
    .github-markdown-body li {
      overflow-x: auto;
      overflow-y: hidden;
    }
  }
}

.header {
  :deep() {
    .van-nav-bar__right {
      opacity: 1 !important;
    }
  }
}
</style>
