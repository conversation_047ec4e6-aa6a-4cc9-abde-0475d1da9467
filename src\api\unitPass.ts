import config from '@/config/index'
import request from '@/utils/request/index'
const { baseURL, baseURL3 } = config

export function getQuestionData(data) {
  return request.get(baseURL3 + '/pad/student/unit/questionsList', data)
}

//单题提交
export function submitQuestion(data) {
  return request.post(
    baseURL3 + '/pad/student/unit/submitCellQuestionNew',
    data,
  )
}

/* 提交小节答案*/
export function submitNoduleApi(data) {
  return request.post(baseURL3 + '/pad/student/unit/submitBarQuestion', data)
}
/* 获取学段*/
export function getPhaseSelectApi() {
  return request.get(baseURL3 + '/pad/student/unit/getPhase')
}
/* 学科列表 */
export function getSubjectListApi(data) {
  return request.get(baseURL3 + '/pad/student/unit/getSubject', data)
}
/* 教材版本*/
export function getTextbookEditionApi(data) {
  return request.get(baseURL3 + '/pad/student/unit/materialList', data)
}
//获取左侧章节树
export function getLeftChapterListApi(data) {
  return request.get(baseURL3 + '/pad/student/unit/leftChapterList', data)
}
//获取子节点
export function getChildrenApi(data) {
  return request.get(baseURL3 + '/pad/student/unit/childrenListNew', data)
}

//交卷
export function submitPaper(data) {
  return request.post(baseURL3 + '/pad/student/unit/submitCellReport', data)
}

//添加笔记
export function addNote(data) {
  return request.post(baseURL + '/v3/student/exercises/note/save', data)
}

/* 单元闯关-诊断/测评 小节练习报告 报告详情*/
export function getReportDetail(data) {
  return request.get(baseURL3 + '/pad/student/unit/reportV2', data)
}
/* 视频列表*/
export function getVideoListApi(data) {
  return request.get(baseURL3 + '/pad/student/unit/videoListNew', data)
}
/* 写入观看记录 */
export function submitVideo(data) {
  return request.post(baseURL3 + '/pad/student/unit/submitVideoNew', data)
}
