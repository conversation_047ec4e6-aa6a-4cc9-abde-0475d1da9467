<template>
  <div class="mt-16px">
    <span class="text-16px font-600">学霸训练</span>
    <div class="flex mt-14px justify-between px-4px">
      <div class="flex-shrink-0">
        <div
          v-for="item in leftList"
          :key="item.id"
          class="van-haptics-feedback"
          @click="androidJump({ data: item, query: item.query })"
        >
          <img :src="item.image" class="w-160px h-114px mb-10px" alt="" />
        </div>
      </div>
      <div class="flex-shrink-0">
        <div
          v-for="item in rightList"
          :key="item.id"
          class="van-haptics-feedback"
          @click="androidJump({ data: item })"
        >
          <img :src="item.image" class="w-163px h-74px mb-8px" alt="" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import androidJump from '../androidJump'

const leftList = [
  {
    id: 1,
    title: '逆袭课程',
    image: $g.tool.getFileUrl('home/train-course.png'),
    redirect_url: '/zxs/unitPass/unitMain',
    query: { type: 'HXN' },
  },
  {
    id: 1,
    title: '错题本',
    image: $g.tool.getFileUrl('home/train-mistake.png'),
    redirect_url: `${import.meta.env.VITE_APP_THREE_LANDSCAPE_URL}/#/student/errorBook/main`,
    landscape: true, //横屏
    updateData: false, //回到原页面不更新数据
  },
]
const rightList = [
  {
    id: 1,
    title: '导学案',
    image: $g.tool.getFileUrl('home/train-guidance.png'),
    redirect_url: `${import.meta.env.VITE_APP_THREE_LANDSCAPE_URL}/#/student/guidance/guidanceMain`,
    landscape: true, //横屏
    updateData: false, //回到原页面不更新数据
  },
  {
    id: 2,
    title: '阶段性测试',
    image: $g.tool.getFileUrl('home/train-test.png'),
    redirect_url: '',
  },
  {
    id: 3,
    title: 'AI英语',
    image: $g.tool.getFileUrl('home/train-english.png'),
    redirect_url: '',
  },
]
</script>

<style lang="scss" scoped></style>
