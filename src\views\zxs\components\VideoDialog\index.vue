<template>
  <van-popup
    v-if="showDialog"
    ref="myDialogRef"
    v-model:show="showDialog"
    :closeable="true"
    class="br-[9px] w-[700px] h-[630px] px-[17px] top-0 bottom-0 m-auto transform-none"
  >
    <div class="text-[17px] font-500 text-[#000] my-[13px]">{{ title }}</div>
    <div v-if="videoList?.length">
      <div class="w-full h-375px rounded-[13px] overflow-hidden relative">
        <g-video
          v-if="isAudio"
          :key="currentVideo?.fileAbsoluteUrl"
          :url="currentVideo?.fileAbsoluteUrl"
          class="!w-full !h-full !pt-0"
          :config="{
            poster: currentVideo?.videoCoverUrl,
          }"
        />
        <img
          v-else-if="isImg"
          :src="currentVideo?.fileAbsoluteUrl"
          class="w-full h-full object-cover"
        />
        <template v-else-if="isDoc">
          <iframe
            :key="currentVideo?.fileAbsoluteUrl"
            :src="$g.tool.previewUrlHandle(currentVideo?.fileAbsoluteUrl)"
            frameborder="0"
            class="w-full h-full"
          ></iframe>
        </template>
        <div v-else class="w-full h-full flex-cc">
          <g-empty class="h-200px" description="不支持的文件格式"></g-empty>
        </div>
      </div>
      <div class="flex items-center my-10px">
        <g-icon name="svg-global-video2" size="19" />
        <div
          class="text-14px text-[#333] ml-5px pt-1px w-[580px] truncate"
          style="font-weight: 500"
        >
          {{ currentVideo?.fileName || '暂无数据' }}
        </div>
      </div>
      <div class="w-[665px] overflow-auto">
        <div class="flex">
          <div
            v-for="(item, idx) in videoList"
            :key="idx"
            class="w-[121px] flex-[0_0_121px] mr-20px flex-shrink-0 h-121px bg-white overflow-hidden rounded-[9px] cursor-pointer border-[3px] border-solid border-[transparent]"
            :class="{
              '!border-[#3398F7]': videoIdx === idx,
            }"
            @click="change(idx)"
          >
            <div class="w-full h-81px relative">
              <template v-if="audioList.includes(getExt(item))">
                <div
                  v-if="getExt(item) == 'm3u8'"
                  class="w-full h-full bg-white"
                ></div>
                <g-img
                  v-else
                  :preview="false"
                  :src="
                    item?.fileAbsoluteUrl +
                    '?x-oss-process=video/snapshot,t_0,f_jpg,m_fast,w_500'
                  "
                  fit="cover"
                  width="100%"
                  height="100%"
                />
                <g-icon
                  name="svg-global-play-white"
                  size="28"
                  class="absolute left-[50%] top-[50%] -translate-x-[50%] -translate-y-[50%]"
                />
              </template>
              <g-img
                v-else-if="imgList.includes(getExt(item))"
                :preview="false"
                :src="currentVideo?.fileAbsoluteUrl"
                fit="cover"
                width="100%"
                heigh="100%"
              />
              <div v-else class="w-full flex justify-center">
                <img
                  :preview="false"
                  :src="$g.tool.getFileTypeIcon(getExt(item))"
                  alt="logo"
                  class="w-60px h-60px object-contain"
                />
              </div>
            </div>
            <div class="p-6px truncate text-[#333333] text-14px font-medium">
              {{ item?.fileName || '暂无数据' }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="w-full flex justify-center py-20px">
      <g-empty></g-empty>
    </div>
  </van-popup>
</template>
<script lang="ts" setup>
const props = defineProps({
  //视频组
  videoList: {
    type: Array<any>,
    required: true,
  },
  //用v-model:show 控制弹窗显示隐藏
  show: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '讲解视频',
  },
})
let videoIdx = $ref(0)
const emit = defineEmits(['update:show'])
const currentVideo: any = $computed(() => {
  return props.videoList?.[videoIdx] || null
})

watch(
  () => props.show,
  () => {
    videoIdx = 0
  },
)
const ext = $computed(() => {
  return getExt(currentVideo)
})

function getExt(item) {
  if (!item) return ''
  const suffix =
    item.fileAbsoluteUrl.slice(item.fileAbsoluteUrl.lastIndexOf('.') + 1) ||
    item.fileExtension ||
    ''
  return suffix.toLowerCase()
}
const audioList: any = [
  'mp4',
  'avi',
  'rmvb',
  'flv',
  'wmv',
  'mkv',
  'mov',
  'mp3',
  'wav',
  'aif',
  'm3u8',
  'webm',
]
const imgList: any = ['jpg', 'jpeg', 'gif', 'png', 'gif', 'webp', 'svg']
const isAudio = $computed(() => {
  return audioList.includes(ext)
})
const isImg = $computed(() => {
  return imgList.includes(ext)
})

const isDoc = $computed(() => {
  return ['doc', 'docx', 'xls', 'xlsx', 'pdf', 'ppt', 'pptx'].includes(ext)
})
function change(idx) {
  videoIdx = idx
}
const showDialog = useVModel(props, 'show', emit)
</script>
<style scoped lang="scss"></style>
