<template>
  <div
    class="w-full h-screen bg-gradient-to-b from-[#F0F8FF] to-[#F0F0FF] flex flex-col justify-between"
  >
    <div>
      <div
        :style="{
          paddingTop: `${$g.navigationHeight + 44}px`,
        }"
        class="px-20px AlimamaShuHeiTi leading-none"
      >
        <div class="gradient-text">
          {{ reportData?.studentName || '无' }}
        </div>
        <div class="gradient-text my-23px">
          {{ reportData?.sysSubjectName }}测试报告
        </div>
      </div>
      <div class="pl-20px text-[#7074C0]">数据统计 截止到 2025年1月1日</div>
    </div>
    <div class="">
      <div class="w-full h-363px relative">
        <img
          class="w-full h-full object-contain"
          :src="$g.tool.getFileUrl('oneCaseReport/screen-icon.png')"
        />
        <div
          class="glass-effect AlimamaShuHeiTi text-white flex items-center justify-center"
          @click="changeStatus"
        >
          <div class="flex flex-col pr-0px pl-20px">
            <span class="text-19px">点击开启</span>
            <span class="text-10px">生成您的成绩报告</span>
          </div>
          <g-icon
            name="ri-arrow-right-double-line"
            size="20"
            color="#fff"
            class="px-12px"
          />
          <div
            class="w-69px h-69px bg-[#fff]/10 rounded-full flex items-center justify-center"
          >
            <div class="rounded-full circle-icon">
              <van-icon name="down" size="18" class="-rotate-90" />
            </div>
          </div>
        </div>
      </div>
      <div
        class="text-center py-23px pb-15px text-[#7074C0] text-13px flex items-center justify-center"
      >
        <g-icon name="ri-error-warning-fill" size="14" color="#144AF9" />
        <span class="ml-7px">这里就是一句话随便给个什么</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from '@/stores/modules/user'
import type { IOneCaseReportDetail } from '../../type'

const emit = defineEmits(['changeStatus'])
const props = defineProps<{
  reportData: IOneCaseReportDetail | null
}>()
const { userInfo } = useUserStore()
function changeStatus() {
  emit('changeStatus')
}
</script>

<style scoped lang="scss">
.gradient-text {
  background: linear-gradient(90deg, #3d3d3d 0%, #271ec9 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
  font-size: 52px;
}
.glass-effect {
  position: absolute;
  bottom: 43px;
  left: 0;
  width: 100%;
  height: 77px;
  background: rgba(166, 189, 241, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}
.circle-icon {
  background: linear-gradient(126deg, #f79554 0%, #fe3f42 100%);
  width: 62px;
  height: 62px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
