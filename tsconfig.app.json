{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["env.d.ts", "src/**/*", "src/**/*.vue", "types/**/*"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"composite": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "baseUrl": ".", "paths": {"@/*": ["./src/*"], "/#/*": ["./types/*"]}, "types": ["node", "vite/client", "@vue-macros/reactivity-transform/macros-global"], "noImplicitAny": false}}