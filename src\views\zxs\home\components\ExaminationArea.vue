<template>
  <div class="mb-12px px-12px">
    <img
      :src="$g.tool.getFileUrl('home/gkccy-banner.png')"
      class="w-full van-haptics-feedback"
      @click="handleMenuItemClick"
    />
    <div class="grid grid-cols-2 justify-center mt-14px gap-5px">
      <div
        v-for="item in newExam"
        :key="item.id"
        class="van-haptics-feedback flex flex-col items-center"
        @click="jumpJZTApp(item.redirect_url)"
      >
        <div class="w-full h-full mb-8px">
          <img :src="item.image" class="w-full h-auto" alt="" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getJZTKey } from '@/api/common'
import { getIsActivityOn } from '@/api/home'
import androidJump from '../androidJump'

const emit = defineEmits(['jumpJZTApp'])

let newExam: any = $ref([
  {
    title: '思维训练',
    redirect_url: `${import.meta.env.VITE_JZT_APP_URL}/#/jzt/thinkingExplanation/main`,
    image: $g.tool.getFileUrl('home/swxl-new.png'),
  },
  {
    title: '专题训练',
    redirect_url: `${import.meta.env.VITE_JZT_APP_URL}/#/jzt/home/<USER>
    image: $g.tool.getFileUrl('home/ztxl-new.png'),
  },
  {
    title: '必备知识闯关',
    redirect_url: `${import.meta.env.VITE_JZT_APP_URL}/#/jzt/home/<USER>
    image: $g.tool.getFileUrl('home/zsjj-new.png'),
  },
  {
    title: '全量真题',
    redirect_url: `${import.meta.env.VITE_JZT_APP_URL}/#/jzt/home/<USER>
    image: $g.tool.getFileUrl('home/qlzt-new.png'),
  },
])

async function handleMenuItemClick() {
  let key = await getJZTKey()
  const activityUniqueId =
    import.meta.env.VITE_APP_ENV === 'production'
      ? '4d7f64f2746366849d984e5e999f4c84'
      : '2abc8af8ebc72cb9bfa3b8464c6c106b'
  const { isOpen } = await getIsActivityOn({
    activityUniqueId,
  })
  const locationUrl = `${
    import.meta.env.VITE_APP_THREE_LANDSCAPE_URL
  }/#/student/sprintCamp/main`
  if (isOpen === 2) {
    androidJump({
      data: {
        redirect_url: locationUrl,
        landscape: true,
      },
      query: {
        activityUniqueId: activityUniqueId,
        showBack: true,
        encryptedStr: key,
      },
    })
  } else {
    $g.showToast('未开通此活动权限，请联系公司处理')
  }
}

//跳转金字塔app
async function jumpJZTApp(path) {
  if (!path) return $g.showToast('敬请期待！')
  try {
    // 跳转金字塔app项目地址需要获取兑换token的参数
    let key = await getJZTKey()
    path = path.includes('?') ? `${path}&` : `${path}?`
    if (!$g.inApp) {
      window.location.href = `${path}encryptedStr=${key}`
    } else {
      $g.flutter('launchInNewWebView', {
        url: `${path}encryptedStr=${key}`,
        autoPrefix: false,
        autoClose: false,
      })
    }
  } catch (error) {
    console.log(`⚡[ error ] >`, error)
  }
}
</script>

<style lang="scss" scoped></style>
