<template>
  <div class="top-bg bg-[#F9F9F9]">
    <g-navbar customBackGround="transparent" />
    <div
      class="px-16px overflow-y-auto no-bar pb-20px"
      :style="{
        height: `calc(100vh - ${useSettingStore().navBarTotalHeight}px - ${useSettingStore().navigationHeight}px)`,
      }"
    >
      <div
        class="flex-cc h-32px br-[8px] bg-[#FFF6F4] border border-[rgba(254,107,71,0.14)]"
      >
        <img
          :src="$g.tool.getFileUrl('home/AI.png')"
          alt=""
          class="w-17px h-12px"
        />
        <span class="ml-4px text-[#FE6B47] text-14px h-20px lh-[20px]"
          >智推院校根据目标成绩推荐</span
        >
      </div>
      <g-loading v-if="loading"></g-loading>
      <div v-else-if="ifShowSchool">
        <div class="mt-10px">
          <div class="flex justify-between items-center">
            <div class="text-16px font-600">国内院校</div>
            <div
              class="flex items-center"
              @click="
                $router.push({
                  name: 'AiStudentPartnerMore',
                  query: {
                    title: '国内院校',
                    noInternational: 1,
                  },
                })
              "
            >
              <span class="text-12px text-[#666666] h-17px lh-[17px]"
                >查看更多</span
              >
              <img
                :src="$g.tool.getFileUrl('aiStudentPartner/arrow-right.png')"
                alt=""
                class="w-12px h-12px"
              />
            </div>
          </div>
          <div class="container">
            <div v-if="aiPartnerInfo.targetScoreCollege?.length">
              <div
                v-for="(item, index) in aiPartnerInfo.targetScoreCollege"
                :key="item.universityId"
                @click="goToUniversity(1, item)"
              >
                <div class="pt-16px flex">
                  <img
                    v-if="item.logo"
                    :src="item.logo"
                    alt=""
                    class="w-40px h-40px mr-8px flex-shrink-0"
                  />
                  <div class="text-14px flex-1 w-0">
                    <div class="font-500 text-[#141414] line-1">
                      {{ item.universityName }}
                    </div>
                    <div
                      v-if="item.tags?.length"
                      class="text-[#666666] mt-6px flex flex-wrap"
                    >
                      <div
                        v-for="(tag, tagIndex) in item.tags"
                        :key="tagIndex + 'tag'"
                        class="flex-cc"
                      >
                        <span>{{ tag }}</span>
                        <div
                          v-if="tagIndex !== item.tags.length - 1"
                          class="w-1px h-6px bg-[#CCCCCC] ml-8px mr-7px"
                        ></div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="flex-shrink-0 text-12px text-white w-20px h-20px flex-cc br-[4px]"
                    :style="{ background: colorMap[item.cwbStr] }"
                  >
                    {{ item.cwbStr }}
                  </div>
                </div>
                <div
                  v-if="index !== aiPartnerInfo.targetScoreCollege?.length - 1"
                  class="w-full h-1px bg-[#F4F4F4] mt-17px"
                ></div>
              </div>
            </div>
            <g-empty v-else></g-empty>
          </div>
        </div>
        <div class="mt-16px">
          <div class="flex justify-between items-center">
            <div class="text-16px font-600">国际院校</div>
            <div class="flex items-center" @click="goInternationalSchool">
              <span class="text-12px text-[#666666] h-17px lh-[17px]"
                >查看更多</span
              >
              <img
                :src="$g.tool.getFileUrl('aiStudentPartner/arrow-right.png')"
                alt=""
                class="w-12px h-12px"
              />
            </div>
          </div>
          <div class="container">
            <div
              v-if="aiPartnerInfo.internationalSchool?.internationalSchoolId"
              class="flex pt-16px items-center"
              @click="goToUniversity(2)"
            >
              <img
                v-if="aiPartnerInfo.internationalSchool?.schoolBadge"
                :src="aiPartnerInfo.internationalSchool?.schoolBadge"
                alt=""
                class="w-40px h-40px mr-8px flex-shrink-0"
              />
              <div class="flex-1 w-0 text-14px">
                <div class="line-1 font-500 text-[#141414]">
                  {{ aiPartnerInfo.internationalSchool?.schoolName }}
                </div>
                <div class="line-1 mt-6px text-[#666666]">
                  {{ aiPartnerInfo.internationalSchool?.tags }}
                </div>
              </div>
            </div>
            <g-empty v-else></g-empty>
          </div>
        </div>
      </div>
      <div v-else class="mt-120px flex flex-col items-center">
        <img
          :src="$g.tool.getFileUrl('aiStudentPartner/school-empty.png')"
          alt=""
          class="w-100px h-79px"
        />
        <div class="mt-10px text-14px">{{ emtyText }}</div>
        <div
          class="mt-40px bg-[#EDEDED] br-[12px] w-168px h-43px flex-cc font-600"
          @click="
            $router.push({
              name: 'TargetScore',
            })
          "
        >
          <span class="text-[#FE6B47]">【点击此处】</span>
          <span>进行填写</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { getGradeDetail } from '@/api/aiStudentPartner'
import { useSettingStore } from '@/stores/modules/setting'
let aiPartnerInfo = $ref<any>({})
let loading = $ref(true)
let emtyText = $ref('你当前暂未填写目标成绩')
const ifShowSchool = $computed(() => {
  if (!aiPartnerInfo.score) {
    emtyText = '你当前暂未填写目标成绩'
    return false
  }
  if (!aiPartnerInfo.targetScoreCollege?.length) {
    emtyText = '你当前填写的目标成绩过低，无智推院校，建议提高目标分数'
    return false
  }
  return true
})
const colorMap = {
  冲: '#FFB813',
  稳: '#5DB5FC',
  保: '#2DD367',
}
async function init() {
  loading = true
  let res = await getGradeDetail()
  aiPartnerInfo = res || {}

  loading = false
}
onBeforeMount(() => {
  init()
})

function goInternationalSchool() {
  if (
    !aiPartnerInfo.internationalSchool ||
    !aiPartnerInfo.internationalSchool.internationalSchoolId
  )
    return
  $g.flutter('nativeRoute', {
    name: '/foreignCollege',
    backCallJsRefresh: false, //返回后要不要刷新
  })
}

function goToUniversity(type, item?) {
  //type为1是国内 2是国际
  if (type == 1) {
    $g.flutter('nativeRoute', {
      name: '/collegeDetail',
      backCallJsRefresh: false,
      params: {
        collegeId: item.universityId, //注意这里是String
        collegeName: item.universityName,
      },
    })
    return
  }
  $g.flutter('nativeRoute', {
    name: '/foreignCollegeDetail',
    backCallJsRefresh: false,
    params: {
      internationalSchoolId:
        aiPartnerInfo.internationalSchool.internationalSchoolId,
      internationalSchoolName: aiPartnerInfo.internationalSchool.schoolName,
    },
  })
}
</script>
<style scoped lang="scss">
.top-bg {
  background: url(@/assets/img/aiStudentPartner/top-bg.png) top center / 100%
    auto no-repeat;
}
.container {
  @apply bg-white br-[12px] px-14px pb-17px mt-10px;
}
</style>
