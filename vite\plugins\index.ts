import { resolve } from 'node:path'
import type { PluginOption } from 'vite'
import Vue from '@vitejs/plugin-vue'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { VantResolver } from 'unplugin-vue-components/resolvers'
import { codeInspectorPlugin } from 'code-inspector-plugin'
import inject from '@rollup/plugin-inject'
import vueSetupExtend from 'unplugin-vue-setup-extend-plus/vite'
import { createHtmlPlugin } from 'vite-plugin-html'
import ReactivityTransform from '@vue-macros/reactivity-transform/vite'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import { visualizer } from 'rollup-plugin-visualizer'
import legacy from '@vitejs/plugin-legacy'
import { Plugin as importToCDN } from 'vite-plugin-cdn-import-async'
import { sentryVitePlugin } from '@sentry/vite-plugin'
import versionPlugin from './version'
import progress from 'cc-vite-progress'

export function setupVitePlugins({ mode, ENV, version }): PluginOption[] {
  let plugins = [
    Vue({
      script: {
        defineModel: true,
      },
      template: {
        compilerOptions: {
          isCustomElement: (tag) => tag.startsWith('wx-open'),
        },
      },
    }),
    ReactivityTransform(),
    inject({
      include: ['src/**/**'],
      modules: {
        $g: resolve('src/utils/index'),
      },
    }),
    createHtmlPlugin({
      inject: {
        data: {
          mode: mode,
        },
      },
    }),
    vueSetupExtend({}),
    AutoImport({
      imports: [
        'vue',
        'pinia',
        'vue-router',
        '@vueuse/core',
        {
          axios: [['default', 'axios']],
        },
      ],
      dts: 'types/auto-imports.d.ts',
      eslintrc: {
        filepath: './.eslintrc-auto-import.json',
        enabled: false,
      },
    }),
    Components({
      dirs: ['src/components/'],
      resolvers: [VantResolver()],
      dts: 'types/components.d.ts',
    }),
    createSvgIconsPlugin({
      iconDirs: [resolve(process.cwd(), 'src/assets/svg')],
      symbolId: 'svg-[dir]-[name]',
    }),
    codeInspectorPlugin({
      bundler: 'vite',
      hideDomPathAttr: true,
    }),
    progress({ projectName: '智习室APP' }),
  ]
  if (mode?.includes('report')) {
    plugins = [...plugins, visualizer({ open: true, gzipSize: true })]
  }
  if (ENV.VITE_APP_SOURCEMAP == 'open') {
    plugins = [
      ...plugins,
      sentryVitePlugin({
        org: 'qmdr',
        project: 'zxs-app',
        authToken:
          '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',
        debug: false, // 禁用调试信息打印
        silent: true,
      }),
    ]
  }
  // 非开发环境
  if (!mode?.includes('development')) {
    plugins = [
      ...plugins,
      versionPlugin({ version }),
      importToCDN({
        modules: [
          {
            name: 'lottie-web',
            var: 'lottie',
            mode: 'async',
            path: `//frontend-cdn.qimingdaren.com/common/lottie.min.js`,
          },
          {
            name: 'echarts',
            var: 'echarts',
            mode: 'async',
            path: '//frontend-store.oss-cn-chengdu.aliyuncs.com/cdn/echarts.min.js',
          },
          {
            name: 'lodash',
            var: '_',
            path: '//frontend-store.oss-cn-chengdu.aliyuncs.com/cdn/lodash.min.js',
          },
        ],
      }),
    ]
  }
  if (['production'].includes(mode)) {
    plugins = [
      ...plugins,
      legacy({
        targets: ['Android >= 6', 'iOS >= 9'],
        modernPolyfills: true,
      }),
    ]
  }

  return plugins
}
