<template>
  <van-popup v-model:show="showPop" class="rounded-[12px]">
    <div
      class="w-fit h-fit relative pt-16px pb-25px px-34px flex flex-col items-center"
    >
      <img
        src="@/assets/img/aiTeacher/close.png"
        alt="close icon"
        class="absolute w-16px h-16px right-20px top-20px van-haptics-feedback"
        @click="showPop = false"
      />
      <div class="text-[#000] text-18px font-500 h-26px leading-[26px] mb-6px">
        选择AI老师
      </div>
      <div class="mb-20px">
        <div
          v-for="item in teacher"
          :key="item.id"
          class="flex items-center mt-18px rounded-[12px] border-[2px] border-[transparent] bg-[#FEF7E0] px-34px py-24px van-haptics-feedback"
          :class="{
            '!border-[#FFC613]': selectID == item.id,
          }"
          @click="$emit('changeId', item.id)"
        >
          <img
            :src="
              selectID === item.id
                ? $g.tool.getFileUrl('aiTeacher/teacher-change.png')
                : $g.tool.getFileUrl('aiTeacher/teacher.png')
            "
            alt="ming avatar"
            class="w-50px h-50px mr-15px"
          />
          <div>
            <div class="text-[#333] text-18px font-600 mb-4px">
              {{ item.name }}
            </div>
            <div class="text-14px text-[#666] whitespace-nowrap">
              {{ item.tip }}
            </div>
          </div>
        </div>
      </div>
      <div
        class="px-37px py-7px rounded-[8px] bg-[#FFC613] text-16px font-600 flex-cc van-haptics-feedback"
        @click="$emit('confirm')"
      >
        确定
      </div>
    </div>
  </van-popup>
</template>

<script setup lang="ts">
const props = defineProps({
  selectID: {
    type: [String, Number],
    default: '',
  },
})
const emit = defineEmits(['confirm', 'changeId'])
const showPop = defineModel<boolean>('show')
const teacher = [
  { name: '小鸣老师', tip: '逐步引导解题思路', id: 1 },
  { name: '小启老师', tip: '精准回答步骤问题', id: 2 },
]
</script>

<style lang="scss" scoped></style>
