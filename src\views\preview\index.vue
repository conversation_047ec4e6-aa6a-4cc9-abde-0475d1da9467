<template>
  <div>
    <g-navbar :title="navigationBarTitle" />
    <div class="pt-17px px-10px">
      <g-empty v-show="!fileUrl && !showLoading"></g-empty>
      <g-loading v-show="showLoading" class="h-200px"></g-loading>
      <div class="br-[9px] overflow-hidden">
        <iframe
          v-show="!showLoading"
          v-if="fileUrl"
          id="viewIframe"
          class="iframeHeight overflow-hidden"
          :src="fileUrl"
          frameborder="1"
          width="100%"
          noresize="noresize"
          style="box-sizing: border-box"
          @load="handleIframeLoad"
        ></iframe>
      </div>
    </div>
  </div>
</template>

<script setup name="PreviewDoc">
const route = useRoute()
const showLoading = ref(true) // 是否开启动画展示
let navigationBarTitle = $ref('查看')
// 监听iframe加载完成事件
function handleIframeLoad() {
  showLoading.value = false
}

const fileUrl = $computed(() => {
  // if (route.query.title) {
  //   navigationBarTitle = route.query.title
  // }
  if (!route.query.url) return ''
  return $g.tool.previewUrlHandle(route.query.url)
})

if (!route.query.url) {
  // 未传入url参数，显示空页面
  showLoading.value = false
}
</script>

<style scoped>
.iframeHeight {
  height: calc(100vh - 47px - 17px - 13px);
}
</style>
