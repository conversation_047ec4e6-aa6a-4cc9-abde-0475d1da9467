export function createBuildConfig(ENV) {
  return {
    outDir: './dist',
    sourcemap: (ENV.VITE_APP_SOURCEMAP == 'open' ? 'hidden' : false) as any,
    commonjsOptions: {
      transformMixedEsModules: true,
    },
    assetsInlineLimit: 0,
    rollupOptions: {
      output: {
        experimentalMinChunkSize: 20 * 1024,
        entryFileNames: 'js/[name].entry.[hash].js',
        chunkFileNames: ({ name, moduleIds }) => {
          if (name === 'index') {
            const pathArray = moduleIds[moduleIds.length - 1].split(/[/\\]/)
            const lastFolderName = pathArray[pathArray.length - 2]
            return `js/${lastFolderName}.[hash].js`
          }
          return 'js/[name].[hash].js'
        },
        assetFileNames: () => {
          return '[ext]/[name].[hash].[ext]'
        },
        manualChunks: {
          'vue-render': [
            'vue',
            'vue-router',
            'pinia',
            'pinia-plugin-persistedstate',
          ],
          'utils-vender': [
            'mitt',
            'bignumber.js',
            'dayjs',
            'timeago.js',
            'eventemitter3',
            'axios',
          ],
          'vant-vender': ['vant'],
          'md-editor-vender': [
            '@l9m/v-md-editor',
            '@l9m/v-md-editor/lib/theme/github.js',
            '@l9m/v-md-editor/lib/preview',
            '@l9m/v-md-editor/lib/preview-stream',
          ],
        },
      },
    },
  }
}
