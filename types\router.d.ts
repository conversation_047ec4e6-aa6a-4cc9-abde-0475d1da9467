import { RouteRecordName, RouteRecordRaw } from 'vue-router'

export type AppRouteRecordRaw = {
  meta?: AppRouteMeta
  name: RouteRecordName
  children?: AppRouteRecordRaw[]
} & RouteRecordRaw

export interface AppRouteMeta {
  /** 页面名称 */
  title: string
  /** 页面是否为无需校验即可访问的页面*/
  public?: boolean
  /** 是否缓存当前页面 */
  keepAlive?: boolean
  /** 前往指定页面时，缓存当前页面 */
  keepAliveArr?: string[]
  /** 开发时可以携带的调试参数 */
  debugQuery?: { [k: string]: any }
  /** 是否验证用户是否绑定学生 */
  isBindChild?: boolean
  /** 是否缓存当前路由和滚动条 */
  noKeepAlive?: boolean
  /** todo:显示tabbar（还需要安卓配合） */
  showTabbar?: boolean
}

export interface RoutesModuleType {
  routerArr: AppRouteRecordRaw[]
}
