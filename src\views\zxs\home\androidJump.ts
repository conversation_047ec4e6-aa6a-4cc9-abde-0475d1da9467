import router from '@/router/index'
import { useUserStore } from '@/stores/modules/user'

/**
 * @description 兼容本地跳转、安卓跳转（安卓跳转信息需有'landscape'字段或者跳转地址包含'qimingdaren'）
 * @param {Object}
 * { data:{
 *    redirect_url:'' 跳转地址（安卓跳转时，有参数直接拼接在地址上）
 *    landscape:false 是否横屏
 *    updateData:false 回到原页面后是否调用函数
 *   }
 *   callback 默认null,安卓跳转回到原页面后调用的函数
 *   query:  默认{} 本地跳转参数/拼接参数
 * }
 */

export default function androidJump(option) {
  const {
    data,
    callback = null,
    query = {}, //本地跳转
    tokenName = 'token',
  } = option
  if (!data.redirect_url) return $g.showToast('开发中，敬请期待！')
  // 安卓跳转
  if (
    'landscape' in data ||
    data.redirect_url.includes('qimingdaren.com') ||
    data.redirect_url.includes('http://172.19.180')
  ) {
    const userStore: any = useUserStore()
    let jumpUrl = data.redirect_url
    let params: any = {}
    if ($g.tool.isTrue(query)) params = query

    // 跳转本地电脑或三端横屏项目带上token(使用encryptedStr换token的方式不带)
    if ((!$g.inApp || data.landscape) && !query?.encryptedStr) {
      params.token = userStore[tokenName]
    }

    //地址带参
    for (const i in params) {
      jumpUrl += `${jumpUrl.includes('?') ? '&' : '?'}${i}=${params[i]}`
    }

    if ($g.inApp) {
      setFlutterParam(jumpUrl, data?.landscape, data?.updateData, callback)
      return
    }
    window.location.href = jumpUrl
    return
  }

  //当前项目地址跳转
  router.push({ path: data.redirect_url, query })
}

//安卓跳转 url-地址 landscape-是否横屏 refreshCallJs-返回后是否调用方法更新数据
async function setFlutterParam(url, landscape, refreshCallJs, callback) {
  let option: any = {
    url,
    refreshCallJs: refreshCallJs || false,
  }
  //横屏参数
  if (landscape) {
    option = {
      url,
      refreshCallJs,
      inSafeArea: {
        top: false,
        left: true,
        bottom: false,
        right: false,
      },
      beforeEnter: {
        orientation: {
          portraitUp: true,
          portraitDown: false,
          landscapeLeft: false,
          landscapeRight: false,
        },
        fullPage: false,
      },
      afterEnter: {
        orientation: {
          portraitUp: false,
          portraitDown: false,
          landscapeLeft: true,
          landscapeRight: false,
        },
        fullPage: true,
      },
    }
  }
  await $g.flutter('launchInNewWebView2', option)
  if (refreshCallJs && callback) window.refreshPage = callback
}
