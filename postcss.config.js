export default {
  plugins: {
    tailwindcss: {},
    autoprefixer: {
      // overrideBrowserslist: [
      //   'Android 4.1',
      //   'iOS 7.1',
      //   'Chrome > 31',
      //   'ff > 31',
      //   'ie >= 8',
      //   '> 1%',
      // ],
      // grid: true,
    },
    'postcss-px-to-viewport-8-plugin': {
      unitToConvert: 'px',
      viewportWidth: 375,
      unitPrecision: 6,
      propList: ['*'],
      viewportUnit: 'vw',
      fontViewportUnit: 'vw',
      selectorBlackList: ['no-'],
      minPixelValue: 1,
      mediaQuery: true,
      replace: true,
      landscape: false,
      // exclude: [/\/src\/views\/results-search\/answer-card/],
    },
    'postcss-mobile-forever': {
      appSelector: '#app',
      viewportWidth: 375,
      maxDisplayWidth: 500,
      enableMediaQuery: true,
      // landscapeWidth: 500,
      desktopWidth: 500,
      propList: ['*', '!opacity'],
      rootContainingBlockSelectorList: [
        'van-tabbar',
        'van-popup',
        'xgplayer-rotate-fullscreen',
      ],
      // 选择器黑名单，名单上的不转换
      selectorBlackList: [
        '.xgplayer.xgplayer-rotate-fullscreen',
        '.xg-top-bar',
        '.xg-pos',
        '.van-overlay',
        '.van-popup--bottom',
        '.van-popup--top',
        '.van-dropdown-item',
        '.van-dialog__footer',
        '.van-action-bar',
        '.van-floating-bubble',
      ],
      // 属性黑名单，名单上的不转换，如果要指定选择器内的属性
      // propertyBlackList: {
      //   '.van-overlay': ['width', 'left'],
      //   '.van-popup--bottom': ['width', 'left'],
      //   '.van-popup--top': ['width', 'left'],
      //   '.van-dropdown-item': ['width', 'left', 'right'],
      // },
    },
  },
}
