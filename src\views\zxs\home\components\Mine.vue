<template>
  <div class="h-full relative overflow-auto">
    <div class="flex items-center mt-31px relative">
      <img
        v-if="userStore?.userInfo?.headPicture"
        :src="userStore?.userInfo?.headPicture"
        alt=""
        class="w-61px h-61px"
      />
      <img
        v-else
        src="@/assets/img/home/<USER>"
        class="w-61px h-61px"
        alt=""
      />
      <div class="ml-12px pt-4px">
        <div class="text-24px font-600">{{ userInfo?.studentName }}</div>
        <div class="text-12px mt-1px text-[#999999] flex justify-between">
          <span> {{ userInfo?.sysGradeName }} · {{ userInfo?.cityName }} </span>
        </div>
      </div>
      <g-icon
        v-if="!showBtnByVerison"
        class="absolute right-0 bottom-2px"
        name="svg-home-change"
        size="20"
        color=""
        @click="
          $g.flutter('nativeRoute', {
            name: 'changeRolePage',
          })
        "
      />
    </div>

    <div
      v-for="item in menuList"
      :key="item.pathName"
      class="mt-12px py-20px px-16px br-[8px] bg-[white] flex justify-between items-center van-haptics-feedback"
      @click="jumpReport(item.pathName)"
    >
      <span class="text-15px">{{ item.title }}</span>
      <g-icon name="ri-arrow-right-s-line" size="24" color="#999900" />
    </div>
    <div
      v-if="showLogout"
      class="!bg-[white] flex-cc br-[8px] py-12px text-15px van-haptics-feedback fixed bottom-[150px] w-[calc(100%_-_32px)]"
      @click="logOut"
    >
      退出登录
    </div>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from '@/stores/modules/user'

const userStore: any = useUserStore()
const router = useRouter()
const props = defineProps({
  userInfo: {
    type: Object,
    default: () => {},
  },
  type: {
    type: String,
    default: 'NBX',
  },
})

let { version, userPlatform } = $(storeToRefs(useUserStore()))
const staticMenuList = [
  {
    title: '使用报告',
    pathName: 'aiReport',
    platform: ['YUNXIAO_STUDENT_H5', 'QMYX'],
  },
  // {
  //   title: '修改密码',
  //   pathName: 'ChangePwdIndex',
  // },
  {
    title: '系统设置',
    pathName: 'sysSetting',
    platform: ['YUNXIAO_STUDENT_H5'],
  },
]

const menuList = $computed(() => {
  return staticMenuList.filter((item) => item.platform.includes(userPlatform))
})

//1.6.8以及以上版本登录判断
const showBtnByVerison = $computed(() => {
  return !($g.tool.compareVersion('1.6.8', version) === 'lt')
})

const showLogout = $computed(
  () => !showBtnByVerison || (showBtnByVerison && userStore?.role == 'student'),
)

function jumpReport(pathName: string) {
  switch (pathName) {
    case 'aiReport':
      let jumpUrl = `${import.meta.env.VITE_YX_APP_URL}/#/yx/aiReport`
      // 本地电脑测试带上token
      if (!$g.inApp) {
        jumpUrl = `${jumpUrl}?token=${userStore.token}`
        window.location.href = jumpUrl
        return
      }
      $g.flutter('launchInNewWebView2', {
        url: jumpUrl,
      })
      break
    case 'ChangePwdIndex':
      router.push({ name: pathName })
      break
    case 'sysSetting':
      $g.flutter('nativeRoute', {
        name: '/MineSetPage',
      })
      break
  }
}

function logOut() {
  useUserStore().resetAll()
  if (!$g.inApp) {
    router.replace({ name: 'Debugging' })
  } else {
    $g.flutter('closeWebView')
  }
}
</script>

<style lang="scss" scoped></style>
