<template>
  <div class="mb-[25px]">
    <div class="mb-8px flex flex-wrap justify-between">
      <span
        class="AlimamaShuHeiTi text-[14px] leading-[17px] flex-shrink-0 mb-8px"
      >
        {{ knowledgeType }}知识点({{ tableOption.data.length }}个)
      </span>
      <span class="text-[12px] text-[#999] leading-[17px] mb-8px">
        {{ knowledgeTips }}
      </span>
    </div>

    <g-table
      :table-option="tableOption"
      :header-style="{
        background: '#f5f5f5',
        color: '#666',
        fontSize: '12px',
        height: '32px',
      }"
      :stripe="false"
      :cell-style="cellStyle"
    />
  </div>
</template>

<script setup lang="ts">
/** 报告页面知识点维度、知识点组件*/

const props = defineProps({
  //知识点类型 0薄弱 1良好 2优秀
  type: {
    type: Number,
    default: 0,
  },
  //知识点table数据
  tableData: {
    type: Array,
    default: () => [],
  },
})

const knowledgeType = computed(() => {
  return ['薄弱', '良好', '优秀'][props.type]
})

const knowledgeTips = computed(() => {
  return [
    '知识点对应试题的平均正确率<60%',
    '知识点对应试题的平均正确率>=60%,<85%',
    '知识点对应试题的平均正确率>=85%',
  ][props.type]
})

let tableOption = $ref<any>({
  column: [
    {
      prop: 'commonKnowledgePointName',
      label: '知识点',
      attr: { style: { textAlign: 'left', paddingLeft: '12px' } },
    },
    { prop: 'questionNums', label: '对应试题' },
    { prop: 'accuracy', label: '正确率' },
  ],
  data: [],
})

//设置单元格样式
function cellStyle(row, index, col) {
  if (col.prop === 'commonKnowledgePointName') {
    return {
      textAlign: 'left',
      padding: '12px 5px 12px 12px',
      borderBottom: '1px solid #eeeeee',
      lineHeight: '16px',
    }
  }
  return {
    wordBreak: 'break-all',
    padding: '12px 5px',
    borderBottom: '1px solid #eeeeee',
    fontSize: '11px',
  }
}

watch(
  () => props.tableData,
  (val: any) => {
    tableOption.data = val.map((item) => ({
      ...item,
      accuracy: $g.math(item.accuracy).multiply(100).toFixed().value() + '%',
    }))
  },
  { immediate: true },
)
</script>
