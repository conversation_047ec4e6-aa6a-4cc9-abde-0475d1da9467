import flutter from './flutter'
import tool from './tool'
import math from './math'
import _ from 'lodash'
import vant from '@/plugins/vant'
import mitt from 'mitt'
import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'
import JSON_DATA from '@/json/index'

dayjs.locale('zh-cn')
dayjs.extend(duration)
const $g = {
  ...vant.vantFunction,
  flutter,
  tool,
  bus: mitt(),
  dayjs,
  inApp: window.flutter_inappwebview ? true : false,
  _,
  math: (num) => {
    return new math(num)
  },
  navigationHeight: 0, // 导航条高度
  navBarTotalHeight: tool.pxConversionAdaptedPx(46, false), // 使用@/views/components/g-navbar/index.vue这个组件的整个bar高度，已包括了手机状态栏的高度
  inTabbar: false, // 用于记录是否通过tabbar显示的页面
  // 用户相关
  user: {
    isBindChild: true, // 是否绑定孩子
  },
  JSON_DATA,
  previewImg: (url, index = 0) => {
    // url：图片链接：字符串数组或者单个， index: 默认开始位置
    const data = Array.isArray(url)
      ? url
      : [url + '?x-oss-process=image/quality,q_80/format,webp']
    flutter('previewImage', {
      urls: data,
      index,
      canShare: false,
    })
  },
}

window.$g = $g

export default $g
