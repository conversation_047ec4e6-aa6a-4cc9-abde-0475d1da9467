<template>
  <div class="question-item pb-80px">
    <!-- 题目附加数据 -->
    <div class="flex items-center justify-between mb-12px">
      <div
        class="flex-shrink-0 text-[#141414] text-[12px] px-8px br-[2px] bg-[#f3f3f3] leading-[24px]"
      >
        {{
          questionItem.jk_new_question_type_name ||
          questionItem.common_question_types_name
        }}
        ID:{{ questionItem.paper_question_id }}
      </div>

      <div class="flex items-center">
        <!-- 需要添加的其他按钮 -->
        <slot name="topAction"></slot>

        <!-- AI老师讲解按钮 -->
        <AiTeacherButton v-if="showAiTeacher" :question-item="questionItem" />

        <!-- 我要纠错按钮 -->
        <CorrectionDialog
          v-if="showToCorrect"
          :question-item="questionItem"
          :correction-source="correctionSource"
          :extra-params="correctionExtraParams"
          @corrected-and-next="emit('correctedAndNext', $event)"
        />
      </div>
    </div>

    <!-- 主题题干 -->
    <g-mathjax
      :text="questionItem.paper_question_title"
      class="mb-[18px] font-500 text-[16px] mt-10px overflow-y-hidden overflow-x-auto"
    />

    <!-- 多子题切换按钮 -->
    <div
      v-if="questionItem.sub_question.length > 1"
      class="flex items-center mb-[16px] overflow-x-auto no-bar"
    >
      <div
        v-for="(subQuestion, subIndex) in questionItem.sub_question"
        :id="'sub-btn-' + subIndex"
        :key="subIndex"
        class="w-32px h-32px border border-[#DFDFDF] rounded-[8px] flex-cc mr-12px flex-shrink-0"
        :class="{
          'border-theme-primary !text-white !bg-theme-primary':
            curSubQuestionIndex === subIndex,
          'border-theme-primary text-theme-primary bg-[#fff]':
            ifSelected(subQuestion),
        }"
        @click="handleSubQuestionIndexChange(subIndex)"
      >
        {{ subIndex + 1 }}
      </div>
    </div>

    <!-- 子题目 -->
    <template
      v-for="(subQuestion, subIndex) in questionItem.sub_question"
      :key="subQuestion.paper_sub_question_id"
    >
      <ChildQuestion
        v-if="curSubQuestionIndex === subIndex"
        :subQuestion="subQuestion"
        :subIndex="
          questionItem.sub_question.length > 1 ? subIndex + 1 : undefined
        "
        :questionItem="questionItem"
        :isLastQuestion="isLastQuestion"
        :showTakePhoto="showTakePhoto"
        @next-question="handleNextQuestion"
        @next-sub-question="handleNextSubQuestion"
        @update-checked="handleUpdateChecked"
      >
        <template #bottomAction>
          <slot name="bottomAction"></slot>
        </template>
      </ChildQuestion>
    </template>
  </div>
</template>

<script setup lang="ts">
import ChildQuestion from './ChildQuestion.vue'
import CorrectionDialog from '../components/CorrectionDialog/index.vue'
import AiTeacherButton from '../components/AiTeacherButton.vue'

const props = defineProps({
  // 题目数据
  questionItem: {
    type: Object,
    required: true,
    default: () => ({}),
  },
  // 是否是最后一个题目，最后一个题目的最后一个小问提交按钮文案会变成 完成作答
  isLastQuestion: {
    type: Boolean,
    default: false,
  },

  // 是否显示AI老师讲解按钮
  showAiTeacher: {
    type: Boolean,
    default: false,
  },

  // 是否显示拍照按钮,仅在主观题上支持拍照
  showTakePhoto: {
    type: Boolean,
    default: true,
  },

  // 是否显示纠错按钮,如果题目数据中没有纠错状态的key值，也不会显示
  showToCorrect: {
    type: Boolean,
    default: false,
  },
  // 页面来源，传递给我要纠错弹窗中使用
  correctionSource: {
    type: Number,
  },
  // 额外所需参数，传递给我要纠错弹窗中使用
  correctionExtraParams: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits<{
  // 更新题目数据
  (e: 'updateChecked', questionItem: any): void
  // 切换下一个大题
  (e: 'nextQuestion'): void
  // 纠错完成并跳转下一题
  (e: 'correctedAndNext', questionItem: any): void
}>()

// 当前显示的子题序号
let curSubQuestionIndex = $ref(0)

// 子题切换
async function handleSubQuestionIndexChange(index) {
  curSubQuestionIndex = index
  scrollIntoCenter()
  await nextTick()
  $g.tool.renderMathjax()
}

// 切换下一题
function handleNextQuestion() {
  emit('nextQuestion')
}

// 多个按钮滚动到中间
function scrollIntoCenter() {
  document.getElementById('sub-btn-' + curSubQuestionIndex)?.scrollIntoView({
    behavior: 'smooth',
    block: 'center',
    inline: 'center',
  })
}

// 切换下一个小问
async function handleNextSubQuestion() {
  curSubQuestionIndex++
  scrollIntoCenter()
  await nextTick()
  $g.tool.renderMathjax()
}

// 大题变化以后,重置当前子题的序号
watch(
  () => props.questionItem,
  () => (curSubQuestionIndex = 0),
  { immediate: true },
)

// 判断子题目是否是选择题
function ifChoice(subQuestion) {
  return [1, 2, 3].includes(subQuestion.jk_new_question_type_id)
}
// 判断子题目是否已经作答完毕
const ifSelected = (subQuestion) => {
  if (ifChoice(subQuestion)) {
    return subQuestion.answers.length || subQuestion.is_jump === 2
  }
  return subQuestion.is_jump === 2 || [1, 2].includes(subQuestion.is_correct)
}
watchEffect(() => {
  let questionItem = props.questionItem
  questionItem.isSelected = questionItem.sub_question?.every((subItem) =>
    ifChoice(subItem) ? subItem.hasChecked : ifSelected(subItem),
  )
})

// 更新题目选中状态
function handleUpdateChecked() {
  emit('updateChecked', props.questionItem)
}
</script>
