import { login, getJ<PERSON><PERSON><PERSON><PERSON>, getJ<PERSON><PERSON><PERSON> } from '@/api/common'
interface StateType {
  token: string
  jztToken: string
  userInfo: UserInfoType
  platform: string
  userPlatform: string
  version: string
  role: string
}

// 用户信息
interface UserInfoType {
  accountId: number
  schoolId: number
  headPicture: null
  accountName: string
  userName: string
  mobile: null
  nickname: null
  token: string
  userType: string
  [key: string]: any
}

export const useUserStore = defineStore('user', {
  state: (): StateType => ({
    token: '',
    jztToken: '',
    userInfo: {} as UserInfoType,
    platform: 'YUNXIAO_STUDENT_H5',
    userPlatform: 'YUNXIAO_STUDENT_H5', // 保留真实的用户平台
    version: '',
    role: '',
  }),
  getters: {},
  actions: {
    async getToken() {
      if (!$g.inApp) return
      // 获取token
      await $g.flutter('token').then((res) => {
        this.token = res
      })
      // 获取版本信息
      await $g.flutter('version').then((res) => {
        this.version = res
      })
      // 获取jztToken
      await $g.flutter('jztToken').then((res) => {
        if (res) {
          this.jztToken = res
        }
      })
      // 获取用户信息
      await $g.flutter('userInfo').then((res) => {
        this.userInfo = res
      })
      // 获取当前使用平台
      await $g.flutter('platform').then((res) => {
        this.platform = 'YUNXIAO_STUDENT_H5'
        this.userPlatform = res
      })
      // 获取当前角色
      await $g.flutter('role').then((res) => {
        this.role = res
      })
    },

    // 获取金字塔token，在PC端调试调用
    async getJztToken() {
      const encryptedStr = await getJZTKey()
      const res = await getJZTToken({
        encryptedStr,
      })
      this.jztToken = res.token
    },

    // debugging 页面啊登录获取token
    async getTokenTest(loginInfo: { userName: string; password: string }) {
      await login(loginInfo).then((res) => {
        this.token = res.token
        this.userInfo = res
        $g.showToast('登录成功')
      })
      await this.getJztToken()
    },

    async resetAll() {
      const userStore = useUserStore()
      userStore.$reset()
      localStorage.clear()
      sessionStorage.clear()
    },
  },
  persist: {
    storage: localStorage,
  },
})
