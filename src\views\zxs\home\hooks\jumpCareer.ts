import { getStudentInfoList, getDimension } from '@/api/aiStudentPartner'
export function useJumpCareer() {
  const loading = ref(false)
  const $router = useRouter()
  // 跳转到职业规划 mode 6: 跳转到学科效能 2: 跳转到职业兴趣
  function jumpCareer(mode: number = 2) {
    loading.value = true
    getStudentInfoListApi(mode)
  }

  //更新智推专业数据
  function updateMajor() {
    $g.bus.emit('openMajorPolling')
  }

  function getStudentInfoListApi(mode) {
    getStudentInfoList({
      page: 1,
      page_size: 10000,
    }).then((res) => {
      const taskColumns = res?.list.map((v) => ({
        text: v.taskPackage.title,
        value: v.student_task_id,
        status: v.taskPackage.status,
        doStatus: v.status,
      }))
      const currentTask = taskColumns[0]
      // 判断当前任务包是否已经完成，判断是否有任务包，判断任务包内是否包含指定 mode 维度

      if (currentTask?.status == 3 || !currentTask) {
        $g.showToast('暂无测评任务，请联系老师开通任务包')
        loading.value = false
        return
      } else if (currentTask?.doStatus == 2) {
        $g.showToast('该任务已完成，请等待报告生成')
        loading.value = false
        return
      }

      // 再去拿任务包下面维度
      getDimension({ student_task_id: currentTask.value }).then(async (res) => {
        loading.value = false
        const item = res.find((v) => v.taskDimension.dimension_type == mode)
        if (!item) {
          $g.showToast('暂无测评任务，请联系老师开通任务包')
          return
        } else {
          if (item.status == 2) {
            $g.showToast('该测评已完成，请等待报告生成')
            loading.value = false
            return
          }
          await $g.flutter('launchInNewWebView2', {
            url: `${import.meta.env.VITE_YX_APP_URL}/#/career/appraisal?student_dimension_record_id=${item.student_dimension_record_id}&title=${mode == 2 ? '职业兴趣测评' : '学科效能 '}&source=zxsApp`,
            refreshCallJs: true,
          })
          window.refreshPage = updateMajor
        }
      })
    })
  }

  return {
    jumpCareer,
    loading,
  }
}
