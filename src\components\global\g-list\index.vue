<template>
  <van-pull-refresh
    v-model="refreshing"
    :disabled="!pulldown"
    :style="{ 'min-height: 80vh': data?.length }"
    @refresh="onPulldownRefresh"
  >
    <van-list
      v-model:loading="loading"
      v-model:error="error"
      :finished="finished"
      :finished-text="finishedText"
      :offset="200"
      :style="{ 'min-height: 80vh': data?.length }"
      @load="onPullupRefresh"
    >
      <slot>
        <g-loading v-if="!once" class="h-200px"></g-loading>
        <slot v-if="!data.length && !loading && !error && once" name="empty">
          <g-empty
            :text="description"
            style="margin: 10px auto 0 auto"
          ></g-empty>
        </slot>

        <van-empty
          v-if="error"
          :image="_g.tool.getFileUrl('common/error.png')"
          description="请求失败"
          @click="onPulldownRefresh"
        />
      </slot>
    </van-list>
  </van-pull-refresh>
</template>

<script lang="ts" setup>
const _g = $g
const props = defineProps({
  pageOption: {
    type: Object,
    default() {
      return { page: 1, page_size: 10, total: 0, total_pages: 0 }
    },
  },
  pullup: {
    type: Boolean,
    default: true,
  },
  pulldown: {
    type: Boolean,
    default: true,
  },
  // 列表数据源
  data: {
    type: Array,
    default: () => [],
  },
  description: {
    type: String,
    default: '暂无内容',
  },
  // 用于报错处理,传请求url
  url: {
    type: [String, Array],
  },
  finishedText: {
    type: String,
    default: '  — 没有更多了 —',
  },
})

/* 是否达到最后一页 */
const pullUpLoadEnd = computed(() => {
  let { page } = props.pageOption

  return page + 1 > maxPage.value && !loading.value
})

const maxPage = computed(() => {
  let { page_size, total } = props.pageOption
  const num = (total + page_size - 1) / page_size
  return parseInt(num.toString())
})

const finished = computed({
  get() {
    return pullUpLoadEnd.value && once.value && Boolean(props.data.length)
  },
  set(value) {
    return value
  },
})

const emit = defineEmits(['pullup', 'pulldown', 'update:data'])
const once = ref(false) // 判断是否第一次加载数据
const error = ref(false)
const refreshing = ref(false)
const loading = ref(false)

watch(
  () => props.data,
  async () => {
    once.value = true
    if (refreshing.value) {
      refreshing.value = false
    }
    loading.value = false
  },
  { deep: true },
)

/* 上拉加载 */
function onPullupRefresh() {
  if (!once.value) {
    loading.value = false
  } else {
    if (props.data.length) {
      emit('pullup')
    } else {
      loading.value = false
    }
  }
}

/* 下拉刷新 */
function onPulldownRefresh() {
  finished.value = false
  // loading.value = true
  setTimeout(() => {
    emit('pulldown')
  }, 1000)
}

async function onError({ reason }) {
  if ($g.tool.typeOf(props.url) == 'string') {
    if (reason?.config?.url?.includes(props.url)) {
      error.value = true
      once.value = false
      finished.value = false
      loading.value = false
      emit('update:data', [])
    }
  }
}

window.addEventListener('unhandledrejection', onError, true)

onBeforeUnmount(() => {
  window.removeEventListener('unhandledrejection', onError)
})
</script>

<style lang="scss" scoped>
:deep() {
  .van-list__finished-text {
    padding-bottom: 50px !important;
  }
}
</style>
