// @ts-nocheck
import config from '@/config/index'
import request from '@/utils/request/index'
const { baseURL, baseURL4 } = config

/*八省联考活动弹窗开启状态 */
export function getActivityConfig() {
  return request.get(baseURL + '/v3/student/home/<USER>/unionActivityExam/config')
}
/*获取区域select */
export function getAreaSelectApi() {
  return request.get(
    baseURL + '/v3/student/home/<USER>/unionActivityExam/union/area',
  )
}
/*获取区域list */
export function getAreaListApi() {
  return request.get(
    baseURL + '/v3/student/home/<USER>/unionActivityExam/union/list',
  )
}
/*金字塔获取区域select */
export function getJztAreaSelectApi() {
  return request.get(baseURL4 + '/tutoring/yx/ai/union/area')
}
/*金字塔获取区域list */
export function getJztAreaListApi() {
  return request.get(baseURL4 + '/tutoring/yx/ai/union/list')
}
