<template>
  <!-- AI老师讲解按钮 -->
  <div class="text-12px">
    <span
      v-show="!aiTeacherLoading"
      class="text-[#FCBC00] flex-cc"
      @click="handleAiTeacherClick"
    >
      <g-icon name="svg-common-teacher" size="12" class="mr-4px" />
      <span>AI老师讲解</span>
    </span>

    <span v-show="aiTeacherLoading" class="text-[#999999] flex-cc">
      <g-icon name="svg-common-loading" size="12" class="mr-4px" />
      <span>解析中</span>
    </span>
  </div>
</template>

<script setup lang="ts">
import { isSupportAi } from '@/api/common'
const router = useRouter()

// AI老师讲解按钮点击后的loading状态
let aiTeacherLoading = $ref(false)

const props = defineProps({
  questionItem: {
    type: Object,
    required: true,
    default: () => ({}),
  },
})

// 题目变化以后,重置loading状态
watch(
  () => props.questionItem,
  () => (aiTeacherLoading = false),
  { immediate: true },
)

// 题目ID字段，兼容多个变量名称
const idKey = $computed(() => {
  return (
    ['paper_question_id', 'paperQuestionId', 'questionId'].find((key) =>
      Reflect.has(props.questionItem, key),
    ) || ''
  )
})

// AI老师讲解按钮点击
async function handleAiTeacherClick() {
  aiTeacherLoading = true
  try {
    let res = await isSupportAi({
      questionId: props.questionItem[idKey],
      questionIdFrom: props.questionItem.questionIdFrom,
    })
    res
      ? router.push({
          name: 'AiTeacherMain',
          query: {
            questionId: props.questionItem[idKey],
            errorType: props.questionItem.questionIdFrom === 7 ? 2 : null,
          },
        })
      : $g.showToast({
          message: '该题暂不支持AI讲解！',
        })
  } catch (err) {
    console.log('获取是否支持AI老师出错', err)
  } finally {
    aiTeacherLoading = false
  }
}
</script>

<style lang="scss" scoped></style>
