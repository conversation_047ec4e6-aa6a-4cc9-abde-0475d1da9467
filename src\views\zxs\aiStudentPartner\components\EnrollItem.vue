<template>
  <div class="bg-[#F8F8F8] mb-14px br-[8px]">
    <div class="school-info">
      <div class="school-title">
        <div class="school-type type2">
          <div class="word1">
            {{ info.rate == -1 ? '-' : info.rate
            }}<span v-if="info.rate != -1" class="text-9px">%</span>
          </div>
          <div class="word2">{{ info.probability }}</div>
        </div>
        <div class="school-name">
          <div class="name line-1">{{ info.majorName }}</div>
          <div class="flex justify-between mt-4px">
            <div class="code">
              代码：{{ info.recruitCode }}&nbsp;&nbsp;&nbsp;&nbsp;{{
                info.newYear ? info.newYear : info.year
              }}计划：{{ info.planNum }}人
            </div>
          </div>
        </div>
      </div>
      <div
        v-if="info.majorDesc"
        class="rounded-[8px] bg-[rgba(255,255,255,0.7)] m-10px p-10px"
      >
        <div class="text-12px text-[#999999] lh-[18px]">
          {{ info.majorDesc }}
        </div>
      </div>
      <g-table
        :headerStyle="enrollHeaderStyle"
        :stripe="false"
        :tableOption="enrollTableOption"
        class="enroll-table m-10px"
        description="暂无数据"
      >
      </g-table>
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  info: {
    type: Object,
    default: () => {
      return {}
    },
  },
})

const enrollHeaderStyle = {
  background: 'rgba(255, 255, 255, 0.8)',
  color: '#666666',
  'font-size': '13px',
  'border-radius': '15px',
  'line-height': '14px',
}
const subjectObj = {
  0: '文科',
  1: '理科',
  2: '历史类',
  3: '物理类',
  4: '综合类',
}
const enrollTableOption = computed(() => {
  let dataMoulde = reactive<Record<string, any>>({
    data: [],
    column: [
      {
        prop: 'year',
        label: '年份',
        minWidth: '50px',
      },
      {
        prop: 'types',
        label: '科类',
        minWidth: '50px',
      },
      {
        prop: 'recruitNum',
        label: '录取',
        minWidth: '50px',
      },
      {
        prop: 'minScore',
        label: '最低分',
        minWidth: '50px',
      },
      {
        prop: 'minRank',
        label: '最低位次',
        minWidth: '50px',
      },
    ],
  })
  let list: any = []
  list = props.info.hlist
  if (list?.length != 0) {
    list?.forEach((item) => {
      let arrData: any = []
      arrData.year = item.year
      arrData.recruitNum = item.recruitNum
      arrData.minScore = item.minScore
      arrData.minRank = item.minRank
      arrData.types = subjectObj[item.type]
      dataMoulde.data.push(arrData)
    })
  }
  // console.log(list);
  return dataMoulde
})
</script>

<style lang="scss" scoped>
:deep() {
  .table-wrapper {
    overflow-y: hidden !important;
  }
}
.score-table {
  :deep() {
    tbody {
      tr {
        border-bottom: 1px solid rgba(204, 204, 204, 0.3) !important;
      }
    }
  }
}

.enroll-table {
  :deep() {
    table {
      border-collapse: separate;
      empty-cells: hide;
      border-spacing: 0;
      border-radius: 15px;
    }

    table thead tr th:first-child {
      border-radius: 15px 0 0 15px;
    }

    table thead tr th:last-child {
      border-radius: 0 15px 15px 0;
    }
  }
}

.school-info {
  padding-bottom: 10px;
  background: linear-gradient(180deg, #eaf5ff 0%, #f4f9ff 100%);
  border-radius: 8px;

  .school-title {
    position: relative;
    height: 60px;

    .school-type {
      position: absolute;
      width: 82px;
      height: 51px;
      top: 8px;
      left: -5px;
      text-align: center;
      background-size: contain;

      .word1 {
        color: white;
        font-size: 18px;
        line-height: 30px;
      }

      .word2 {
        line-height: 18px;
        letter-spacing: 2px;
      }
    }

    .type2 {
      background: url('@/assets/img/aiStudentPartner/wen.png') top left
        no-repeat;
      background-size: contain;

      .word2 {
        color: #84a5ff;
        font-size: 12px;
      }
    }

    .type1 {
      background: url('@/assets/img/aiStudentPartner/bao.png') top left
        no-repeat;
      background-size: contain;

      .word2 {
        color: #4de352;
        font-size: 12px;
      }
    }

    .type3 {
      background: url('@/assets/img/aiStudentPartner/chong.png') top left
        no-repeat;
      background-size: contain;

      .word2 {
        color: #f2b93e;
        font-size: 12px;
      }
    }

    .school-name {
      position: relative;
      left: 84px;
      top: 9px;
      padding-right: 10px;
      width: calc(100% - 89px);

      .name {
        font-size: 16px;
        font-weight: bold;
        color: #333333;
        line-height: 24px;
      }

      .code {
        width: 64px;
        height: 22px;
        line-height: 22px;
        font-size: 12px;
        font-weight: 400;
        color: #333333;
        flex: 1;
      }

      .speciality-btn {
        width: 52px;
        height: 22px;
        line-height: 22px;
        background: #3496fa;
        border-radius: 13px;
        font-size: 12px;
        font-weight: 400;
        color: #ffffff;
        text-align: center;
      }
    }
  }
}

.enroll-info {
  .enroll-title {
    height: 30px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 15px;
    display: flex;
    align-items: center;
    font-size: 13px;
    font-weight: 400;
    color: #666666;
    line-height: 30px;
    padding: 0 18px 0 0;
    text-align: center;

    .enroll-title-col {
      flex: 1;
    }
  }
}

.enroll-list {
  height: 19px;
  font-size: 13px;
  font-weight: 400;
  color: #333333;
  line-height: 19px;
  display: flex;
  align-items: center;
  padding: 0px 18px 0 0;
  margin-top: 8px;
  text-align: center;

  .enroll-list-col {
    flex: 1;
  }
}
</style>
