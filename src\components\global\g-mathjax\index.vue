<template>
  <div class="mathjax g-mathjax" v-html="text"></div>
</template>

<script lang="ts" setup>
defineProps({
  text: {
    type: String,
    default: '',
  },
})
</script>

<style lang="scss" scoped>
.mathjax {
  width: 100%;
  word-break: break-word;
  word-wrap: break-word;
  overflow-wrap: anywhere;
  font-size: 14px;
  :deep() {
    img {
      max-width: 680rpx;
      display: inline;
    }
    table {
      border-collapse: collapse;
      th {
        border: 1px solid #333; /* 设置边框 */
        background: #ccc;
        padding-left: 10px;
      }
      td {
        border: 1px solid #333; /* 设置边框 */
        padding-left: 10px;
      }
    }
    span[wave] {
      text-decoration-style: wavy;
      text-decoration-line: underline;
      text-underline-position: auto;
      white-space: pre-wrap;
    }
  }
}
</style>
