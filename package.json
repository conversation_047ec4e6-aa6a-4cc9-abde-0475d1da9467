{"name": "ctxx-h5-v3", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "env-cmd -e development vite", "build:test": "cross-env NODE_OPTIONS=--max-old-space-size=4096 env-cmd -e test vite build", "build:report": "cross-env NODE_OPTIONS=--max-old-space-size=4096 env-cmd -e test-report vite build", "build": "cross-env NODE_OPTIONS=--max-old-space-size=4096  run-p \"build-only {@}\" --", "preview": "vite preview --port 4173", "build-only": "env-cmd -e production vite build", "type-check": "vue-tsc --build --force", "git:push": "git add -A && git-pro commit && git pull && git push", "git:tag": "run-p type-check && git-pro tag", "git:update-branch": "git remote update origin --prune && git checkout develop && git fetch -p && git branch -vv | grep ': gone]' | awk '{print $1}' | xargs git branch -D", "git:update-branch2": "git remote update origin --prune && git checkout master && git branch | grep -v 'master' | xargs git branch -D", "git:merge-test": "git-pro merge-test", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "cle": "rm -rf node_modules && rm -rf dist &&  rm -rf stats.html", "format": "prettier --write src/"}, "dependencies": {"@cjh0/fetch-event-source": "^2.0.3", "@l9m/v-md-editor": "^3.2.12", "@sentry/vue": "^7.111.0", "@vueuse/core": "^10.9.0", "axios": "^1.6.8", "bignumber.js": "^9.1.2", "clipboard": "^2.0.11", "dayjs": "^1.11.6", "echarts": "^5.4.0", "eventemitter3": "^5.0.1", "eventsource-parser": "^1.1.2", "lodash": "^4.17.21", "lottie-web": "^5.12.2", "mermaid": "^11.0.2", "mitt": "^3.0.1", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "timeago.js": "^4.0.2", "vant": "^4.8.10", "vue": "^3.4.21", "vue-router": "^4.3.0", "vue-tippy": "v6", "vue-virtual-scroller": "^2.0.0-beta.8", "xgplayer": "^3.0.16", "xgplayer-flv.js": "3.0.16", "xgplayer-hls": "^3.0.16"}, "devDependencies": {"@cjh0/git-pro": "^7.0.0", "@rollup/plugin-inject": "^5.0.5", "@rushstack/eslint-patch": "^1.3.3", "@sentry/vite-plugin": "^2.16.1", "@tsconfig/node20": "^20.1.2", "@types/node": "^20.11.30", "@typescript-eslint/eslint-plugin": "^7.3.1", "@typescript-eslint/parser": "^7.3.1", "@vitejs/plugin-legacy": "^5.3.2", "@vitejs/plugin-vue": "^5.0.4", "@vue-macros/reactivity-transform": "^0.4.4", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.19", "cc-vite-progress": "1.1.3", "code-inspector-plugin": "^0.15.2", "commitlint-config-gitmoji": "^2.3.1", "cross-env": "^7.0.3", "env-cmd": "^10.1.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-define-config": "^2.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.23.0", "npm-run-all2": "^6.1.2", "postcss": "^8.4.38", "postcss-mobile-forever": "^4.1.5", "postcss-px-to-viewport-8-plugin": "^1.2.5", "prettier": "^3.2.5", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.74.1", "tailwindcss": "3.2.7", "typescript": "5.4.3", "unplugin-auto-import": "^0.17.5", "unplugin-vue-components": "^0.26.0", "unplugin-vue-setup-extend-plus": "^1.0.1", "vite": "5.2.2", "vite-plugin-cdn-import-async": "^1.1.0", "vite-plugin-html": "^3.2.2", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "2.0.13"}, "eslintIgnore": ["src/assets", "public", "dist", "node_modules", "types"], "volta": {"node": "18.20.1"}}