<template>
  <div class="mt-14px br-[8px] bg-white px-10px py-12px">
    <div class="AlimamaShuHeiTi text-17px plain mb-14px font-600">
      <span>我的学习计划</span>
    </div>
    <div
      v-for="(item, index) in barList"
      :key="index"
      :class="item.className"
      class="h-42px mb-5px pl-12px leading-[42px] w-full text-15px font-500"
      @click="studyJump(item)"
    >
      {{ item.title }}
    </div>
  </div>
</template>

<script setup lang="ts">
const emit = defineEmits(['jumpJZTApp'])
const barList = [
  {
    title: '校本同步学习',
    name: 'SyncTrainIndex',
    path: '',
    className: 'xb text-[#5E2F2F]',
  },
  {
    title: '启鸣同步学习',
    name: '',
    path: `${import.meta.env.VITE_JZT_APP_URL}/#/jzt/home/<USER>
    className: 'qm text-[#2A5028]',
  },
  {
    title: '我的复习计划',
    name: '',
    path: `${import.meta.env.VITE_JZT_APP_URL}/#/jzt/home/<USER>
    className: 'wd text-[#3F576C]',
  },
]
const router = useRouter()

//我的学习计划跳转
function studyJump(item) {
  if (item.name) {
    router.push({ name: item.name })
    return
  }
  if (item.path) {
    emit('jumpJZTApp', item.path)
    return
  }
  $g.showToast('敬请期待！')
}
</script>

<style lang="scss" scoped>
.xb {
  background: url(@/assets/img/home/<USER>/ 100% 100% no-repeat;
}
.qm {
  background: url(@/assets/img/home/<USER>/ 100% 100% no-repeat;
}
.wd {
  background: url(@/assets/img/home/<USER>/ 100% 100% no-repeat;
}
.plain {
  background: url(@/assets/img/home/<USER>/ 27px 12px no-repeat;
}
</style>
