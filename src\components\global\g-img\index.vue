<template>
  <van-image
    v-lazy="newImgUrl"
    :width="width"
    :height="height"
    :round="round"
    :radius="radius"
    fit="cover"
    :src="newImgUrl"
    lazy-load
    class="img-box"
    :style="{
      '--rounded': `${roundedNum}`,
      'pointer-events': preview ? 'auto' : 'none',
    }"
    @click.stop="previewPhoto"
  >
    <template v-if="showLoading" #loading>
      <van-loading type="spinner" size="20" />
    </template>
    <slot></slot>
  </van-image>
</template>

<script setup lang="ts">
const props = defineProps({
  src: {
    type: String,
    default: '',
  },
  round: {
    type: Boolean,
    default: false,
  },
  // 质量
  quality: {
    type: [String, Boolean],
    default: '70',
  },
  // 缩放
  scale: {
    type: [String, Number],
    default: 0,
  },
  // oss图片处理参数 以 / 结尾 https://help.aliyun.com/document_detail/410762.html
  // 示例：resize,w_500/
  ossParams: {
    type: String,
    default: '',
  },
  width: {
    type: [Number, String],
    default: 100,
  },
  height: {
    type: [Number, String],
    default: 100,
  },
  currentIndex: {
    type: [Number, String],
    default: 0,
  },
  radius: {
    type: [Number, String],
    default: 0,
  },
  // 是否预览
  preview: {
    type: Boolean,
    default: false,
  },
  // 预览资源
  previewResource: {
    type: Object,
    default: () => {},
  },
  // 圆角
  roundedNum: {
    type: [String, Number],
    default: '0',
  },
  // 是否显示loading
  showLoading: {
    type: Boolean,
    default: true,
  },
  // 是否需要下载
  isShowDown: {
    type: Boolean,
    default: false,
  },
  // 是否需要后缀
  affix: {
    type: Boolean,
    default: true,
  },
})

const currentType = $computed(() => {
  return props.previewResource?.length
    ? $g.tool.resourceType(props.previewResource?.[props.currentIndex])
    : props.src
      ? $g.tool.resourceType(props.src)
      : ''
})

const newImgUrl: any = $computed(() => {
  if (props.src.includes('pic?')) {
    return props.src
  }
  // const suffix =
  //   currentType == 'img'
  //     ? $g.tool.isTrue(props.scale)
  //       ? `?x-oss-process=image/resize,p_${props.scale},1/format,webp`
  //       : `?x-oss-process=image/${props.ossParams}quality,q_${props.quality}/interlace,1/format,webp`
  //     : '?x-oss-process=video/snapshot,t_1000,f_jpg,w_800,h_600,m_fast'
  let suffix = ''
  if (currentType == 'img') {
    if ($g.tool.isTrue(props.scale)) {
      suffix = `?x-oss-process=image/resize,p_${props.scale},1/format,webp`
    } else {
      suffix = `?x-oss-process=image/${props.ossParams}quality,q_${props.quality}/interlace,1/format,webp`
    }
  }
  if (currentType == 'video') {
    suffix = '?x-oss-process=video/snapshot,t_1000,f_jpg,w_800,h_600,m_fast'
  }
  let url = props.src || props.previewResource
  if (url && typeof url !== 'string') {
    url = props.previewResource?.[props.currentIndex]
  }
  url += props.affix ? suffix : ''
  return url
})

const previewPhoto = () => {
  if (!props.preview) return
  let data = $g._.cloneDeep(props.previewResource) || [props.src]
  if (currentType == 'img') {
    data = data
      .map((image) => {
        if (
          $g.tool.resourceType(image) == 'img' &&
          $g.tool.getExt(image) != 'gif'
        ) {
          if (props.affix) {
            image += '?x-oss-process=image/quality,q_80/format,webp'
          }
          return image
        }
        return image
      })
      .filter((image) => image)

    $g.flutter('previewImage', {
      urls: data,
      index: props.currentIndex,
      canShare: false,
      useBigBackButton: true,
      isShowDown: props.isShowDown,
    })
  } else if (currentType == 'video') {
    $g.flutter('playVideo', {
      title: '',
      resource_url: props.previewResource[props.currentIndex],
    })
  }
}
</script>
<style lang="scss" scoped>
.img-box {
  :deep() {
    .van-image__img {
      border-radius: var(--rounded) !important;
    }
  }
}
</style>
