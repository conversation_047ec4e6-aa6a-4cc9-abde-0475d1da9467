<template>
  <div>
    <van-empty :image-size="size" :image="url" :description="text">
      <template #description>
        <slot name="description"
          ><span :style="{ color: textColor }">{{ text }}</span></slot
        >
      </template>
      <slot></slot>
    </van-empty>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  text: {
    type: String,
    default: '暂无数据',
  },
  size: {
    type: Number,
    default: 130,
  },
  url: {
    type: String,
    default: $g.tool.getFileUrl('common/empty.png'),
  },
  textColor: {
    type: String,
    default: '#999',
  },
})
</script>

<style lang="scss" scoped>
:deep() {
  .van-empty__description {
    font-size: 14px;
    margin-top: -8px !important;
  }
  .van-empty__image {
    width: 160px;
    height: 160px;
    img {
      object-fit: contain !important;
    }
  }
}
</style>
