<template>
  <div class="two-card bg-white">
    <div class="message-title flex-cc text-[#000340] mx-auto mb-19px">
      综合点评
    </div>
    <div class="item">
      <CardTitle title="综合评分依据" class="mb-14px" />
      <div class="items-center">
        {{ reportData?.comprehensiveEvaluation?.join('') || '无' }}
      </div>
    </div>
    <div class="item">
      <CardTitle title="能力短板明显" class="mb-14px" />
      <div class="items-center">
        {{ reportData?.shortcomingsAbility?.join('') || '无' }}
      </div>
    </div>
    <div class="item">
      <CardTitle title="提升潜力" class="mb-14px" />
      <div class="items-center">
        {{ reportData?.enhancePotential?.join('') || '无' }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { IOneCaseReportDetail } from '../type'
import CardTitle from './CardTitle.vue'

/** 组件Props接口 */
interface IProps {
  /** 报告数据 */
  reportData?: IOneCaseReportDetail | null
}

/** 定义Props */
let props = withDefaults(defineProps<IProps>(), {
  reportData: null,
})
</script>

<style lang="scss" scoped>
.two-card {
  margin: 0 12px;
  border-radius: 8px;
  margin-bottom: 15px;
  padding: 17px 12px;
}

.item {
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, #eff7ff 0%, #ffffff 100%);
  border-radius: 12px;
  border: 2px solid #eeeeee;
  min-height: 50px;
  padding: 10px;
  margin-bottom: 17px;
  &:last-child {
    margin-bottom: 0;
  }
}

.item-title {
  @apply flex items-center text-13px text-[#423534];
}

.bg-title1 {
  background: url('@/assets/img/oneCaseReport/zhpgyj.png') no-repeat center
    center;
  background-size: 100% 100%;
  width: 112px;
  height: 24px;
  margin-bottom: 14px;
}

.bg-title2 {
  background: url('@/assets/img/oneCaseReport/nldbmx.png') no-repeat center
    center;
  background-size: 100% 100%;
  width: 112px;
  height: 24px;
  margin-bottom: 14px;
}

.bg-title3 {
  background: url('@/assets/img/oneCaseReport/tfql.png') no-repeat center center;
  background-size: 100% 100%;
  width: 88px;
  height: 24px;
  margin-bottom: 14px;
}

.message-title {
  background: url('@/assets/img/oneCaseReport/title.png') no-repeat center
    center;
  background-size: 100% 100%;
  width: 197px;
  height: 27px;
}
</style>
