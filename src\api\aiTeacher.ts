import config from '@/config/index'
import request from '@/utils/request/index'
const { baseURL } = config

//获取题目信息
export function getQuestionDetail(data) {
  return request.get(baseURL + '/v3/student/aiChat/question/detail', data)
}

/*获取题目上次问答 roomId*/
export async function getLastRoomId(data) {
  return request.get(baseURL + '/v3/student/aiChat/latestQuestionChat', data)
}

/*获取聊天记录*/
export async function getChatRecord(data) {
  return request.get(baseURL + '/v3/student/aiChat/chatRecord', data)
}

/*保存笔记*/
export async function saveStudyNote(data) {
  return request.post(baseURL + '/v3/student/aiChat/studyNote/save', data)
}

/*创建聊天*/
export async function createChat(data) {
  return request.post(baseURL + '/v3/student/aiChat/chat/create', data)
}

/*创建聊天室*/
export async function createRoom(data) {
  return request.post(baseURL + '/v3/student/aiChat/chat/room/create', data)
}

/*获取解法列表*/
export async function getParseList(data) {
  return request.get(
    baseURL + '/v3/student/aiChat/chat/question/sub/parse',
    data,
  )
}

/*学习笔记列表*/
export async function getStudyNode(data) {
  return request.get(baseURL + '/v3/student/aiChat/studyNote/list', data)
}

/*小启获取解法步骤详情*/
export async function getParseDetail(data) {
  return request.get(baseURL + '/v3/student/aiChat/question/parse/detail', data)
}

/*是否能自由对话*/
export async function accountInfo() {
  return request.get(baseURL + '/v3/student/aiChat/accountDetails')
}

/*获取默认老师*/
export async function getAiTeacher(data) {
  return request.get(baseURL + '/v3/student/aiChat/account/config', data)
}

/*切换默认老师*/
export async function changeAiTeacher(data) {
  return request.post(baseURL + '/v3/student/aiChat/account/config', data)
}

/*删除笔记*/
export async function deleteNote(data) {
  return request.delete(baseURL + '/v3/student/aiChat/studyNote/delete', data)
}

/* 语音点赞 select */
export function feedback(data) {
  return request.post(
    baseURL + '/v3/student/aiChat/question/tts/user/feedback',
    data,
  )
}
