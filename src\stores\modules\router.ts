import { routes } from '@/router/index'
import type { RoutesModuleType } from '/#/router'

export const useRouterStore: any = defineStore('router', {
  state: (): RoutesModuleType => ({
    routerArr: routes,
  }),
  getters: {
    keepAliveArr: (state) => {
      const newArr: string[] = []
      deepFn(state.routerArr)
      function deepFn(routerArr) {
        routerArr.forEach((e) => {
          if (e?.meta?.keepAlive) newArr.push(e.name)
          if (e?.children?.length) {
            deepFn(e.children)
          }
        })
      }
      return newArr
    },
  },
  actions: {
    changeMenuMeta(options) {
      function handleRoutes(routes) {
        return routes.map((route) => {
          if (route.name === options.name)
            Object.assign(route.meta, options.meta)
          if (route.children && route.children.length)
            route.children = handleRoutes(route.children)
          return route
        })
      }
      this.routerArr = handleRoutes(this.routerArr)
    },
  },
})
