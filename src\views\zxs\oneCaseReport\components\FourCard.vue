<template>
  <div
    class="bg-[#fff] rounded-[8px] mx-12px px-12px py-17px text-13px text-[#2E2323] mb-15px"
  >
    <div class="message-title">能力评价</div>
    <div>
      <template
        v-if="relevantEvaluationList && relevantEvaluationList.length > 0"
      >
        <template
          v-for="(evaluation, evalIndex) in relevantEvaluationList"
          :key="evalIndex"
        >
          <!-- 每个能力类型（知识能力、学科素养、关键能力）-->
          <CardTitle
            :title="getAbilityTypeName(evaluation.type)"
            class="mb-14px"
          />

          <template
            v-if="
              evaluation.relevantRatingList &&
              evaluation.relevantRatingList.length > 0
            "
          >
            <template
              v-for="(item, itemIndex) in evaluation.relevantRatingList"
              :key="itemIndex"
            >
              <!-- 能力名称和进度条 -->
              <div class="flex items-center mb-14px">
                <div class="text-13px mr-12px">{{ item.name }}</div>
                <!-- 进度条 -->
                <div class="flex items-center gap-4px">
                  <div
                    v-for="(segment, index) in progressSegments"
                    :key="index"
                    class="progress-segment"
                    :class="index < item.rating ? 'active' : 'inactive'"
                  ></div>
                </div>
              </div>
            </template>
          </template>
          <div v-else class="text-center py-12px">
            暂无{{ getAbilityTypeName(evaluation.type) }}数据
          </div>

          <!-- 类型级别的总结 -->
          <div class="zj mb-16px">
            <CardTitle title="总结" class="mb-14px" />
            <div class="text-13px">
              {{ evaluation.evaluation }}
            </div>
          </div>

          <!-- 只在不同类型之间添加分隔 -->
          <div
            v-if="evalIndex !== relevantEvaluationList.length - 1"
            class="fgx"
          ></div>
        </template>
      </template>
      <div v-else class="text-center py-20px">暂无能力评价数据</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import CardTitle from './CardTitle.vue'
import type { IOneCaseReportDetail, IRelevantEvaluation } from '../type'

const props = defineProps<{
  /** 报告详情数据 */
  reportData: IOneCaseReportDetail
}>()

/** 模拟数据，用于开发和测试 */
const mockData: IRelevantEvaluation[] = [
  {
    type: 1,
    evaluation: '基础知识掌握薄弱，需加强记忆与理解。',
    relevantRatingList: [
      {
        id: 1,
        name: '知识记忆',
        rating: 2,
        evaluation: '在公式记忆和基本概念识别方面需要加强。',
      },
      {
        id: 2,
        name: '知识理解',
        rating: 1,
        evaluation: '对于知识要点的理解比较模糊，需加快进度。',
      },
    ],
  },
  {
    type: 2,
    evaluation: '学科素养有待提升，尤其在逻辑推理与运算方面。',
    relevantRatingList: [
      {
        id: 1690,
        name: '数学应用',
        rating: 3,
        evaluation: '在实际应用上有一定理解，但需要深入加强。',
      },
      {
        id: 1691,
        name: '理性思维',
        rating: 3,
        evaluation: '在推理过程中逻辑相对欠缺，需要加强训练。',
      },
    ],
  },
  {
    type: 3,
    evaluation: '关键能力表现一般化，数学抽象和建模能力较弱。',
    relevantRatingList: [
      {
        id: 44,
        name: '运算求解能力',
        rating: 2,
        evaluation: '方案解题能力欠佳，需提升处理复杂题目的能力。',
      },
      {
        id: 43,
        name: '逻辑思维能力',
        rating: 2,
        evaluation: '分析问题能力可再提升，建议加强逻辑训练。',
      },
    ],
  },
]

/** 使用实际数据或模拟数据 */
const relevantEvaluationList = $computed<IRelevantEvaluation[] | undefined>(
  () => {
    // 如果props中有数据，优先使用实际数据
    if (props.reportData?.kpointAbility?.relevantEvaluationList) {
      return props.reportData.kpointAbility.relevantEvaluationList
    }
    // 否则使用模拟数据
    return mockData
  },
)

/** 根据类型获取能力类型名称 */
function getAbilityTypeName(type: number): string {
  switch (type) {
    case 1:
      return '知识能力'
    case 2:
      return '学科素养'
    case 3:
      return '关键能力'
    default:
      return '未知能力'
  }
}

/**
 * 进度条总段数
 * 根据IRelevantRating接口中的rating字段，最大值为4
 */
const progressSegments = $ref(Array.from({ length: 4 }, (_, i) => i + 1))
</script>

<style lang="scss" scoped>
.progress-segment {
  width: 60px;
  height: 6px;
  border-radius: 3px;

  &.active {
    background: linear-gradient(to right, #8ba2fa 0%, #5369f4 100%);
  }

  &.inactive {
    background: #ebedfe;
  }
}

.fgx {
  background-image: url('@/assets/img/oneCaseReport/fgx.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 294px;
  width: 100%;
  height: 20px;
  margin: 12px 0;
}

.zj {
  background: linear-gradient(90deg, #eff7ff 0%, #fff 100%);
  border-radius: 12px;
  border: 2px solid #eeeeee;
  padding: 10px;
  margin: 16px 0;
}

.ability-type-title {
  color: #333;
  padding-left: 12px;
  position: relative;
  &:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 16px;
    background: linear-gradient(to bottom, #8ba2fa, #5369f4);
    border-radius: 2px;
  }
}
</style>
