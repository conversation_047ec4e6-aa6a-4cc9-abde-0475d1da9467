<template>
  <div ref="myContainer">
    <g-navbar ref="navBarRef" customTitle="单元练习">
      <div v-if="curQuestion">
        <div class="py-17px bg-white pl-16px pr-14px flex-cc">
          <van-progress
            :percentage="percentage"
            stroke-width="12px"
            class="flex-1"
            :showPivot="false"
            style="background: #f3f3f3"
          />
          <div class="flex-shrink-0 ml-16px text-14px">
            <span class="text-[#333333]">{{ curQuestion?.current }}/</span>
            <span class="text-[#999999]">{{ curQuestion?.count }}</span>
          </div>
        </div>
      </div>
    </g-navbar>

    <g-loading v-if="loading"></g-loading>
    <div
      v-else-if="curQuestion"
      class="px-16px pt-14px"
      style="
        background: linear-gradient(
          180deg,
          #e3eeff 0%,
          #ffffff 4%,
          #ffffff 100%
        );
      "
    >
      <QuestionItem
        :question-item="curQuestion"
        :correction-source="2"
        :show-to-correct="false"
        @next-question="handleNextQuestion"
        @corrected-and-next="handleErrorCorrected"
      >
      </QuestionItem>
    </div>

    <g-empty v-else></g-empty>
  </div>
</template>
<script setup lang="ts">
import QuestionItem from '@/views/zxs/components/QuestionItem/Underline/QuestionItem.vue'
import { getQuestionData, submitNoduleApi, addNote } from '@/api/unitPass'
import {
  getIsJump,
  getIsCorrect,
  getIsCorrectSub,
  getSubAnswer,
} from '@/views/zxs/components/QuestionItem/submit'
import { useUnitStore } from '@/stores/modules/unitPass'
const { active } = storeToRefs(useUnitStore())
const props = defineProps({
  type: {
    type: [Number, String],
    default: 1,
  },
  sysTextbooksCatalogId: {
    type: [Number, String],
    required: true,
  },
})

let curQuestion = $ref<any>(null)
let loading = $ref(true)
const navBarRef = $ref<any>(null)
let percentage = $ref(0)
const route = useRoute()
const router = useRouter()
let initFlag = false
const myContainer = $ref<any>(null)
async function getQuestion() {
  loading = true
  try {
    let res = await getQuestionData({
      type: props.type,
      sysTextbooksCatalogId: props.sysTextbooksCatalogId,
    })
    if (!res) {
      curQuestion = null
      return
    }
    res.sub_question.map((item) => {
      if (item.jk_new_question_type_id == 3) {
        item.options = [
          {
            id: 1,
            name: '√',
            selectId: item.paper_sub_question_id + '1',
            content: null,
          },
          {
            id: 2,
            selectId: item.paper_sub_question_id + '2',
            name: '×',
            content: null,
          },
        ]
      } else {
        item.options = Object.keys(item)
          .filter((key) => key.includes('option_') && key !== 'option_numbers')
          .map((realKey) => {
            return {
              name: realKey.replace('option_', '').toUpperCase(),
              content: item[realKey],
            }
          })
          .filter((v) => v.content)
      }
    })

    curQuestion = {
      disabled: ![1, 2, 3].includes(res.jk_new_question_type_id) ? true : false,
      isJump: 1,
      submitDisabled: true,
      notDisabled: false,
      answers: [],
      is_jump: 1,
      is_correct: 1,
      startAnswer: new Date().getTime(),
      ...res,
      sub_question: (res?.sub_question || []).map((h) => {
        return {
          ...h,
          is_jump: 1,
          is_correct: null,
          answers: [],
        }
      }),
    }
    percentage = Math.round(
      Math.round((curQuestion.current / curQuestion.count) * 10000) / 100,
    )
    await nextTick()
    myContainer?.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    })
    $g.tool.renderMathjax()
    if (!initFlag) {
      navBarRef?.changeUpdateKey()
      initFlag = true
    }
  } catch {
    curQuestion = null
  } finally {
    loading = false
  }
}

async function submit({ params, ifNext }) {
  try {
    let res = await submitNoduleApi(params)
    active.value = true
    if (res == 2) {
      router.replace({
        name: 'UnitReport',
        query: {
          ...route.query,
        },
      })
      return
    }
    getQuestion()
  } catch (e) {
    console.log(e)
    if (ifNext) {
      if (curQuestion.current == curQuestion.count && curQuestion.count !== 0) {
        router.replace({
          name: 'UnitReport',
          query: {
            ...route.query,
          },
        })
        return
      }
      getQuestion()
    }
  }
}

onBeforeMount(() => {
  getQuestion()
})

function getRealAnswer(item) {
  return item.sub_question.reduce((res, h) => {
    if ([1, 2, 3].includes(h.jk_new_question_type_id)) {
      res += h.structure_number + h.question_answer
    } else {
      res += h.structure_number + '线下作答'
    }
    return res
  }, '')
}

function getAnswer(item) {
  return item.sub_question.reduce((res, h) => {
    if ([1, 2, 3].includes(h.jk_new_question_type_id)) {
      res +=
        h.structure_number +
        ((h.answers.length && h.answers.join?.()) || '我不会')
    } else {
      res +=
        h.is_jump === 2
          ? h.structure_number + '我不会'
          : h.structure_number + '线下作答'
    }
    return res
  }, '')
}
async function handleNextQuestion(ifNext?) {
  if (!curQuestion.isSelected) {
    $g.showToast('请完成所有小题~')
    return
  }
  let params = {
    sys_textbooks_catalog_id: props.sysTextbooksCatalogId,
    question_id: curQuestion.paper_question_id,
    student_question_answer: getAnswer(curQuestion),
    type: 2,
    is_jump: getIsJump(curQuestion.sub_question),
    is_correct: getIsCorrect(curQuestion.sub_question),
    question_answer: getRealAnswer(curQuestion),
    complete_time:
      curQuestion.sub_question.reduce(
        (res, item) => res + item.complete_time,
        0,
      ) ?? 0,
    sub_question: curQuestion.sub_question?.map((k) => {
      return {
        sub_question_id: k.paper_sub_question_id,
        is_jump: k.is_jump,
        question_answer: k.question_answer,
        student_question_answer: getSubAnswer(k),
        is_correct: getIsCorrectSub(k),
        complete_time: k.complete_time ?? 0,
      }
    }),
  }
  await submit({ params, ifNext })
  savePhoto(curQuestion)
}

async function savePhoto(currentQues) {
  if (!currentQues.noteList || !currentQues.noteList.length) return
  let notes = [
    {
      questionId: currentQues.paper_question_id,
      noteUrls: currentQues.noteList.map((v) => v.noteUrl),
    },
  ]
  let params = {
    exerciseId: currentQues.chapter_exercises_id,
    type: 3,
    notes,
  }
  await addNote(params)
}

// 题目是否是选择题
function ifChoice(item) {
  return [1, 2, 3].includes(item.jk_new_question_type_id)
}
function handleErrorCorrected(questionItem) {
  questionItem.sub_question?.forEach((h) => {
    if (h.isShow) h.isShow = false
    if (ifChoice(h)) {
      h.answers = h.question_answer.split('')
    } else {
      h.hasChecked = true
      h.is_correct = 2
    }
  })
  handleNextQuestion(true)
}
</script>
