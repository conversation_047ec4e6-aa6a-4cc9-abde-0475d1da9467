<template>
  <van-popup
    v-model:show="show"
    position="bottom"
    class="rounded-t-[20px]"
    :lock-scroll="false"
  >
    <div class="content">
      <div
        class="text-center py-10px text-[#FFC613] flex items-center justify-center van-haptics-feedback"
        @click="close"
      >
        <span class="mr-3px">收起</span>
        <img
          src="@/assets/img/aiTeacher/down.png"
          class="w-10px h-10px mt-[-2px]"
        />
      </div>
      <div ref="noteBox" class="max-h-[80vh] overflow-auto px-10px pt-10px">
        <template v-if="noteList.length">
          <div
            v-for="item in noteList"
            :key="item.fastGptStudyNoteId"
            class="p-10px pb-30px bg-white rounded-[7px] mb-16px relative overflow-hidden note"
          >
            <g-markdown
              :text="item.content"
              mode="preview"
              class="overflow-x-auto overflow-y-hidden"
            ></g-markdown>
            <img
              v-if="item.type !== 1"
              src="@/assets/img/aiTeacher/delete.png"
              alt="back"
              class="w-20px h-20px absolute bottom-[10px] right-[10px]"
              @click="emit('deleteNoteApi', item)"
            />
          </div>
        </template>
        <g-empty v-else></g-empty>
      </div>
    </div>
  </van-popup>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'
const props = defineProps({
  noteList: {
    type: Array as PropType<any>,
    default: () => [],
  },
  isDeleteNoteFlag: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(['deleteNoteApi'])
const show = defineModel<boolean>('show')
const noteBox = ref<any>()

function close() {
  show.value = false
}

//学习笔记面板滚动到底部,等待面板中的内容渲染完毕以后滚动到底部
let scrollToBottomFn = useDebounceFn(() => {
  if (noteBox.value)
    !props.isDeleteNoteFlag &&
      (noteBox.value.scrollTop = noteBox.value?.scrollHeight)
}, 100)

const { stop: stopObserver } = useMutationObserver(
  noteBox,
  (mutations) => {
    if (mutations[0].type === 'childList' && show) scrollToBottomFn()
  },
  {
    childList: true,
    subtree: true,
  },
)
onBeforeUnmount(stopObserver)
</script>

<style lang="scss" scoped>
.note {
  border: 1px solid #ededed;
  :deep() {
    .github-markdown-body li {
      overflow-x: auto;
      overflow-y: hidden;
    }
  }
}
</style>
