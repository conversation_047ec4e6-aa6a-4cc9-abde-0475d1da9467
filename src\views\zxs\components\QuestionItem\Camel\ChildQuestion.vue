<template>
  <div class="child-question">
    <!-- 标题 -->
    <g-mathjax
      v-if="subIndex || childLength !== 1 || subQuestion.paperSubQuestionTitle"
      :text="subQuestion.paperSubQuestionTitle"
      class="overflow-x-auto overflow-y-hidden font-500 mb-[18px]"
    />

    <!-- 选择题,判断题选项 -->
    <button
      v-for="(optionItem, index) in subQuestion.options"
      :key="index"
      class="choice-option"
      :class="getOptionClass(optionItem)"
      :disabled="subQuestion.hasChecked"
      @click="handleOptionClick(optionItem)"
    >
      <!-- 选项内容 -->
      <div class="flex-1 overflow-hidden flex items-center">
        <div class="flex-shrink-0 mr-4px text-[#333]">
          {{ optionItem.name && optionItem.name + '.' }}
        </div>

        <g-mathjax
          :text="optionItem.content"
          class="flex-1 overflow-x-auto overflow-y-hidden"
        ></g-mathjax>
      </div>

      <!-- 选择状态反馈 -->
      <div
        v-if="subQuestion.hasChecked"
        class="flex items-center flex-shrink-0"
      >
        <!-- 选择正确 -->
        <g-icon
          v-if="ifCorrect(optionItem)"
          name="svg-common-correctOption"
          size="16"
        />

        <!-- 选择错误 -->
        <g-icon
          v-if="ifError(optionItem)"
          name="svg-common-errorOption"
          size="16"
        />

        <!-- 正确答案未选 -->
        <span v-if="ifNotSelected(optionItem)" class="text-[#51C551]">
          未选
        </span>
      </div>
    </button>
    <!-- 选择题的我不会按钮 -->
    <button
      v-if="!ifNotChoice"
      class="choice-option mb-30px"
      :class="getOptionClass({ name: 'cant' })"
      :disabled="subQuestion.hasChecked"
      @click="handleButtonClick('cant')"
    >
      我不会
    </button>

    <!-- 作答笔记 -->
    <template v-if="ifNotChoice">
      <div class="section-title">作答笔记</div>

      <!-- 作答照片空显示 -->
      <div
        v-if="!questionItem.noteList?.length"
        class="leading-[22px] text-[12px] text-center text-[#999] mb-36px border border-dashed border-[#979797] py-8px br-[8px]"
      >
        暂未上传作答照片
      </div>

      <!-- 作答照片显示 -->
      <div v-if="questionItem.noteList?.length" class="note-list-wrap">
        <van-swipe loop lazy-render class="note-list-container">
          <van-swipe-item
            v-for="(item, index) in questionItem.noteList"
            :key="index"
          >
            <g-img
              :current-index="index"
              :src="item?.noteUrl"
              :preview-resource="$g._.map(questionItem?.noteList, 'noteUrl')"
              :preview="true"
              :affix="false"
              fit="contain"
              width="100%"
              height="100%"
            />
          </van-swipe-item>
        </van-swipe>
      </div>
    </template>

    <!-- 我的作答,仅线下作答的题型才会有,且需要选择完答案以后才进行显示 -->
    <div
      v-if="ifNotChoice && (subQuestion.isJump === 2 || subQuestion.isCorrect)"
      class="mb-30px"
    >
      <div class="section-title mb-20px">我的作答</div>
      <!-- 已选答案回显，非选择题且答案确定时显示 -->
      <div
        class="w-[165px] h-36px br-[8px] bg-[red] text-white flex-cc mx-auto"
        :style="{
          background:
            subQuestion.isJump === 2
              ? '#FB9721'
              : subQuestion.isCorrect === 1
                ? '#F5222D'
                : '#51C551',
        }"
      >
        <img
          class="w-[50px] mb-4px self-end"
          :src="
            subQuestion.isJump === 2
              ? $g.tool.getFileUrl('common/resultCantBig.png')
              : subQuestion.isCorrect === 1
                ? $g.tool.getFileUrl('common/resultErrorBig.png')
                : $g.tool.getFileUrl('common/resultCorrectBig.png')
          "
        />
        <span class="mx-14px">丨</span>
        <span class="AlimamaShuHeiTi">
          {{
            subQuestion.isJump === 2
              ? '我不会'
              : subQuestion.isCorrect === 1
                ? '答错了'
                : '答对了'
          }}
        </span>
      </div>
    </div>

    <!-- 展开解析按钮,需要选择完答案以后才显示 -->
    <div>
      <div
        v-if="
          subQuestion.isCorrect ||
          (ifNotChoice && subQuestion.isJump == 2) ||
          subQuestion.hasChecked
        "
        class="flex-cc py-8px van-haptics-feedback text-theme-primary"
        @click="showAnswer = true"
      >
        <span class="mr-2px leading-[22px]">展开解析</span>
        <van-icon name="arrow-double-left" class="rotate-90" />
      </div>
    </div>

    <!-- 底部按钮 -->
    <div
      class="van-safe-area-bottom fixed bottom-0 left-0 right-0 w-full px-16px bg-white z-[1000]"
    >
      <div class="flex items-center">
        <!-- 底部按钮插槽 -->
        <slot name="bottomAction"></slot>

        <!-- 拍照按钮,仅在主观题且未选择答案时显示 -->
        <div
          v-if="showTakePhoto && ifNotChoice && !isSelected"
          class="mr-20px text-center leading-[14px]"
          @click="handleTakePhoto"
        >
          <g-icon name="svg-common-camera" size="20" />
          <span class="text-10px">拍作答</span>
        </div>

        <!-- 题目相关按钮 -->
        <div class="flex-1 pl-10px">
          <!-- 提交按钮,仅在选择题,判断题题型和未提交时显示 -->
          <van-button
            v-if="ifChoice && !isSelected"
            type="primary"
            round
            class="w-full h-40px my-10px"
            :disabled="!subQuestion.answers?.length && subQuestion.isJump !== 2"
            @click="handleButtonClick('submit')"
          >
            提交
          </van-button>

          <!-- 下一题按钮,所有题型都有,提交答案以后显示 -->
          <van-button
            v-show="isSelected"
            round
            type="primary"
            class="w-full h-40px my-10px"
            @click="handleNext"
          >
            {{ formatNextBtnText }}
          </van-button>

          <!-- 做完了查看答案按钮,线下作答的题型才显示,选择完答案以后不再显示 -->
          <van-button
            v-if="
              ifNotChoice && subQuestion.isJump !== 2 && !subQuestion.isCorrect
            "
            v-loadOnScroll:[timeDelay].once="intoLook"
            class="w-full h-40px my-10px"
            type="primary"
            round
            :disabled="disabledClick"
            @click="handleButtonClick('notChoice')"
          >
            做完了，查看答案
            <span v-if="disabledClick">({{ countDownTime }})</span>
          </van-button>
        </div>
      </div>
    </div>

    <!-- 答案和解析弹框 -->
    <AnswerDialog
      v-model:show="showAnswer"
      :subQuestion="subQuestion"
      :ifChoice="ifChoice"
      :isLastQuestion="isLastQuestion"
      :isLastSubQuestion="!subIndex || subIndex === childLength"
      @handle-next="handleNext"
      @handle-button-click="handleButtonClick"
      @close="showAnswer = false"
    ></AnswerDialog>
  </div>
</template>
<script setup lang="ts">
import AnswerDialog from '../components/AnswerDialog.vue'
import config from '@/config/index'
const { countDownTime: initDownTime } = config
const props = defineProps({
  // 子题数据
  subQuestion: {
    type: Object,
    required: true,
  },
  // 子题序号
  subIndex: {
    type: Number,
    default: null,
  },
  // 大题数据
  questionItem: {
    type: Object,
    default: () => ({}),
  },
  // 是否显示拍照按钮,仅在主观题上支持拍照
  showTakePhoto: {
    type: Boolean,
    default: true,
  },
  // 是否是最后一个题目，最后一个题目最后一个小问的提交按钮文案会变成 完成作答
  isLastQuestion: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['updateChecked', 'nextQuestion', 'nextSubQuestion'])

const route = useRoute()

// 大题的子题数量
const childLength = $computed(() => props.questionItem.subQuestionList?.length)
// 倒计时时间计数
let countDownTime = $ref(initDownTime)
//哪些页面需要限制主观题开始答题时间
const limitRouteConfig = reactive<any>(['DoTask'])
//主观题答题时间限制执行的延时
const timeDelay = $computed(() => (route.name === 'UnitDiagnosis' ? 1000 : 0))

// 做完了,查看答案按钮禁用状态
const disabledClick = $computed(
  () => limitRouteConfig.includes(route.name) && countDownTime > 0,
)
// 是否是多选题
const ifMultiple = $computed(() => props.subQuestion.jkNewQuestionTypeId == 2)
// 是否是单选题或判断题
const ifSingle = $computed(() =>
  [1, 3].includes(props.subQuestion.jkNewQuestionTypeId),
)
// 是否是选择题
const ifChoice = $computed(() => ifMultiple || ifSingle)
// 是否是非选择题
const ifNotChoice = $computed(() => !ifChoice)
// 答案列表
const answerArr = $computed(() => props.subQuestion.questionAnswer?.split(''))
// 用户已选答案
const myAnswers = $computed(() => props.subQuestion.answers)

// 该选项是否是正确选项
const ifCorrect = (option) =>
  answerArr.includes(option.name) && myAnswers.includes(option.name)
// 该选项是错误选项
const ifError = (option) =>
  !answerArr.includes(option.name) && myAnswers.includes(option.name)
// 该选项是正确答案但是未选
const ifNotSelected = (option) =>
  answerArr.includes(option.name) && !myAnswers.includes(option.name)

// 选择题选项的各个状态
function getOptionClass(option) {
  //  该选项是否被选中
  let optionIsChecked = props.subQuestion.answers?.includes(option.name)
  // '我不会'选项是否被选中
  let cantOptionIsChecked =
    option.name === 'cant' && props.subQuestion.isJump === 2

  // 还未提交的时候，选中的答案为蓝色边框
  if (!props.subQuestion.hasChecked) {
    return optionIsChecked || cantOptionIsChecked ? 'option-checked' : ''
  }
  // 选择正确为绿色边框
  if (ifCorrect(option)) return 'option-correct'
  // 选择错误为红色边框
  if (ifError(option)) return 'option-error'
  // 正确未选中为绿色边框
  if (ifNotSelected(option)) return 'option-correct-uncheck'
  // 选择'我不会'为黄色边框
  if (cantOptionIsChecked) return 'option-cant-check'
}

// 是否显示答案解析
let showAnswer = $ref(false)

// 选项选中的处理函数
function handleOptionClick(option) {
  let subQuestion = props.subQuestion

  // 如果之前答案是我不会则修改为未选中
  if (subQuestion.isJump === 2) subQuestion.isJump = 1

  const clickFunMap = {
    ifMultiple: () => {
      let index = subQuestion.answers.indexOf(option.name)
      if (index > -1) {
        subQuestion.answers.splice(index, 1)
      } else {
        subQuestion.answers.push(option.name)
      }
    },
    ifSingle: () => {
      if (subQuestion.answers.includes(option.name)) return
      subQuestion.answers = [option.name]
    },
  }
  if (ifMultiple) clickFunMap['ifMultiple']()
  if (ifSingle) clickFunMap['ifSingle']()
}

// 是否已经选择完毕
let isSelected = $ref(false)
// 如果已经是做过的题，回显正确的状态
if (
  props.subQuestion.isJump === 2 ||
  props.subQuestion.answers?.length ||
  props.subQuestion.isCorrect
) {
  isSelected = true
  props.subQuestion.hasChecked = true
}

// 按钮点击事件
async function handleButtonClick(type) {
  if (isSelected) return
  let subQuestion = props.subQuestion
  const funMap = {
    // 点击我不会
    cant: () => {
      if (subQuestion.answers.length > 0) subQuestion.answers = []
      if (ifChoice) {
        subQuestion.isJump = 2
        return
      }
      subQuestion.isJump = 2
      showAnswer = false
      isSelected = true
      //主观题我不会触发提交
      emit('updateChecked')
    },
    // 点击提交
    submit: () => {
      subQuestion.hasChecked = true
      showAnswer = true
      isSelected = true
      // 选择题选项触发提交
      emit('updateChecked')
    },
    // 点击做完了查看答案
    notChoice: () => {
      showAnswer = true
    },

    // 答对了
    correct: () => {
      subQuestion.isCorrect = 2
      showAnswer = false
      isSelected = true
      //主观题答对了触发提交
      emit('updateChecked')
    },

    // 答错了
    error: () => {
      subQuestion.isCorrect = 1
      showAnswer = false
      isSelected = true
      //主观题答错了触发提交
      emit('updateChecked')
    },
  }

  funMap[type]()
  await nextTick()
  $g.tool.renderMathjax()
}

// 答案解析弹窗中点击下一题的逻辑
function handleNext() {
  // 如果是单个小问或者当前是多个小问的最后一问则直接跳转下一题
  if (!props.subIndex || props.subIndex === childLength) emit('nextQuestion')
  // 如果是多个小问，且不是最后一小问， 则跳转到下一个小问
  if (childLength > 1 && props.subIndex < childLength) emit('nextSubQuestion')
}

// 下一题按钮的文字显示
const formatNextBtnText = $computed(() => {
  // 如果是单个小问或者当前是多个小问的最后一问，且当前题目为试卷的最后一题
  if (
    (!props.subIndex || props.subIndex === childLength) &&
    props.isLastQuestion
  )
    return '完成作答'
  return '下一题'
})

// 调用安卓拍照
function handleTakePhoto() {
  window.onFileChoose = onFileChoose

  $g.flutter('chooseMedia', {
    singlePhotoSize: 20, //单张照片最大size，单位M
    singleVideoSize: 100, //单个视频最大size，单位M
    takeVideo: false, //是否可以拍视频
    takePicture: true, //是否可以拍照片
    pickVideo: false, //是否可以选视频
    pickPicture: false, //是否可以选照片
    picDoc: false, //是否可以选文件
    enableScanDocument: false, //是否可以使用扫一扫
    applicationParam: 'image',
    enableCrop: true,
    unlimitedAspectRatio: true,
  })
}

// 安卓拍照以后，选择文件的回调
function onFileChoose(params) {
  let data = $g.tool.isTrue(params) && JSON.parse(params)
  if (!data[0] || !data[0].host) {
    return
  }
  if (!props.questionItem.noteList) props.questionItem.noteList = []
  props.questionItem.noteList.push({
    noteUrl: data[0].host + '/' + data[0]?.filePath,
  })
}

let timer: any = null
function intoLook() {
  const defun = () => {
    timer = setTimeout(() => {
      if (countDownTime > 0) {
        countDownTime--
        defun()
      } else {
        clearTimeout(timer)
      }
    }, 1000)
  }

  limitRouteConfig.includes(route.name) && defun()
}

onBeforeUnmount(() => timer && clearTimeout(timer))
</script>

<style scoped lang="scss">
.answer {
  height: 23px;
  background: #51c96b;
  font-size: 12px;
  font-weight: 500;
  color: #fff;
  border-radius: 0px 1px 11px 0px;
  padding-left: 15px;
  line-height: 23px;
  position: relative;
  margin-left: -20px;
}
:deep() {
  .van-button--disabled {
    opacity: 1;
    background-color: #afafaf;
    border-color: #afafaf;
  }

  .g-mathjax {
    img {
      display: inline;
    }
  }
}

.v-enter-active,
.v-leave-active {
  transition: opacity 0.5s ease;
}

.v-enter-from,
.v-leave-to {
  opacity: 0;
}

.choice-option {
  @apply w-full min-h-[40px] py-[9px] px-[12px] mb-[10px] br-[8px] border border-[#dfdfdf] text-left text-[14px] text-[#666] flex items-center overflow-hidden;
}

//选中的样式
.option-checked {
  border-color: #217dfb;
  position: relative;
  padding-right: 30px;
  &::before {
    content: '';
    display: inline-block;
    position: absolute;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: white;
    border: 6px solid #217dfb;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
  }
}

//正确答案
//正确答案但是未选择
//我不会被选中
.option-correct,
.option-correct-uncheck {
  border-color: #79b879;
}

//错误答案
.option-error {
  border-color: #f5222d;
}

.option-cant-check {
  border-color: #fb9721;
}

.section-title {
  font-size: 15px;
  line-height: 22px;
  font-weight: 500;
  position: relative;
  margin-bottom: 12px;
  &::after {
    content: '';
    display: inline-block;
    width: 4px;
    height: 14px;
    background: #217dfb;
    border-radius: 3px;
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
  }
}

// 笔记容器保持宽高比
.note-list-wrap {
  @apply border-[2px] border-[#f3f3f3] mb-36px br-[8px] pb-[50%] relative overflow-hidden;
  .note-list-container {
    @apply absolute left-0 right-0 top-0 bottom-0 overflow-hidden;
  }
}
</style>
