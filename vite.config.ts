import { fileURLToPath, URL } from 'node:url'
import { defineConfig, loadEnv } from 'vite'
import { setupVitePlugins } from './vite/plugins/index'
import { createBuildConfig } from './vite/build/index'

export default defineConfig(({ mode, command }) => {
  const ENV = loadEnv(mode, process.cwd(), '')
  const version = new Date().getTime()
  return {
    base: './',
    plugins: setupVitePlugins({
      mode: ENV.VITE_APP_ENV,
      ENV,
      command,
      version,
    } as any),
    server: {
      //使用IP能访问
      host: true,
      open: false,
    },
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
        '#': fileURLToPath(new URL('./types', import.meta.url)),
      },
    },
    define: {
      __APP_VERSION__: version,
    },
    build: createBuildConfig(ENV as any),
    optimizeDeps: {
      include: [
        'lottie-web',
        '@vueuse/core',
        'lodash/debounce',
        'echarts',
        'eventemitter3',
        'vue-virtual-scroller',
        'vant/es/empty/style/index',
        'vant/es/popup/style/index',
        'vant/es/button/style/index',
        'vant/es/field/style/index',
        'vant/es/pull-refresh/style/index',
        'vant/es/list/style/index',
        'vant/es/image/style/index',
        'vant/es/loading/style/index',
        'vant/es/radio-group/style/index',
        'vant/es/radio/style/index',
        'vant/es/progress/style/index',
        'vant/es/tabs/style/index',
        'vant/es/tab/style/index',
        'vant/es/cascader/style/index',
        'vant/es/popover/style/index',
        'vant/es/steps/style/index',
        'vant/es/step/style/index',
        'vant/es/picker/style/index',
        'vant/es/form/style/index',
        'vant/es/cell-group/style/index',
        'vant/es/checkbox-group/style/index',
        'vant/es/checkbox/style/index',
        'vant/es/circle/style/index',
        'vant/es/uploader/style/index',
        'eventemitter3',
        'vant/es/pagination/style/index',
        'vant/es/date-picker/style/index',
        'vant/es/sticky/style/index',
        'vant/es/nav-bar/style/index',
        'vant/es/icon/style/index',
        'vant/es/divider/style/index',
        'vant/es/collapse/style/index',
        'vant/es/collapse-item/style/index',
        'vant/es/space/style/index',
        'vant/es/picker-group/style/index',
        'vant/es/dropdown-menu/style/index',
        'vant/es/dropdown-item/style/index',
        'vant/es/swipe/style/index',
        'vant/es/swipe-item/style/index',
        'xgplayer',
        'xgplayer-flv.js',
        'xgplayer-hls',
        'vant/es/swipe-item/style/index',
      ],
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `$base-oss: '//frontend-cdn.qimingdaren.com/cdn/img/';`,
        },
      },
    },
  }
})
