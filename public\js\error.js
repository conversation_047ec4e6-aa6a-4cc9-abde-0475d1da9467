// 创建一个函数来处理错误
function handleError(message, source, lineno, colno, error) {
  // 构建错误信息字符串
  var errorMessage = 'Error: ' + message + '\n'
  errorMessage += 'Source: ' + source + '\n'
  errorMessage += 'Line: ' + lineno + '\n'
  errorMessage += 'Column: ' + colno + '\n'
  if (error && error.stack) {
    errorMessage += 'Stack: ' + error.stack + '\n'
  }

  // 将错误信息添加到 <body> 元素中
  // var errorElement = document.createElement('pre')
  // errorElement.innerText = errorMessage
  // document.body.appendChild(errorElement)

  setTimeout(() => {
    console.log('来自error.js', errorMessage)
  }, 1000 * 10)
}

// 监听 window.onerror 事件，并调用 handleError 函数处理错误
window.onerror = handleError

setTimeout(() => {
  let app = document.getElementById('app')
  const hasNode = app.hasChildNodes()
  if (!hasNode) {
    let str = `<div class="app-error">
          <img src="/static/img/load-error.png" class="app-error-img" />
          <div>
            <span class="load-error">加载失败</span>
            <span class="error-back"> 返回</span>
          </div>
        </div>`
    app.innerHTML = str

    const back = document.getElementsByClassName('error-back')[0]
    back.onclick = function () {
      if (
        window.flutter_inappwebview &&
        window.flutter_inappwebview.callHandler
      ) {
        window.flutter_inappwebview.callHandler('back')
      }
    }
  }
}, 20 * 1000)
