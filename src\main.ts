import '@/styles/index.scss'
import { createApp } from 'vue'
import { setupStore } from '@/stores'
import { setupPlugins } from '@/plugins'
import { setupRouter } from '@/router'

import App from './App.vue'
const app = createApp(App)

/* 注册插件 */
setupPlugins(app)
/* 注册状态机 */
setupStore(app)
/* 注册路由 */
setupRouter(app)

app.mount('#app')

app.config.warnHandler = (msg, instance, trace) => {
  if (msg.includes('Extraneous non-props attributes')) {
    return
  }
}
