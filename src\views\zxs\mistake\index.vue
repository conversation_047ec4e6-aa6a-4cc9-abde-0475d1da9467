<template>
  <div>
    <g-navbar
      :customTitle="$route.query.type == 'HXN' ? '我的错题本' : '启鸣错题本'"
    />
    <div
      class="bg-white px-20px pt-20px"
      :style="{
        height: `calc(100vh - ${useSettingStore().navigationHeight}px)`,
      }"
    >
      <div
        class="bg-[#e9f2fd] py-40px px-20px br-[14px] van-haptics-feedback"
        @click="toSchoolMistake"
      >
        学校考试错题
      </div>
      <div
        class="bg-[#f7fede] py-40px px-20px br-[14px] mt-20px van-haptics-feedback"
        @click="jumpPage"
      >
        AI智习室错题
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="MistakeMain">
import { useUserStore } from '@/stores/modules/user'
import { useSettingStore } from '@/stores/modules/setting'

const userStore: any = useUserStore()
const router = useRouter()
function toSchoolMistake() {
  let jumpUrl = `${import.meta.env.VITE_YX_APP_URL}/#/accurate-improvement`
  // 本地电脑测试带上token
  if (!$g.inApp) jumpUrl = `${jumpUrl}?token=${userStore.token}`
  window.location.href = jumpUrl
}

function jumpPage() {
  router.push({ name: 'ZxsMistake' })
}
</script>

<style lang="scss" scoped></style>
