import config from '@/config/index'
import request from '@/utils/request/index'
const { baseURL, baseURL4 } = config

//获取首页icon
export function getIcon(data) {
  return request.get(baseURL + '/v2/family/layout/module', data, {
    delay: false,
  })
}

//获取个人信息
export function getUserInfo() {
  return request.get(
    baseURL + '/v3/student/home/<USER>/basic',
    {},
    {
      delay: false,
    },
  )
}

/**
 * 获取学生是否有新的最近异常拆卷考试情况分析
 */
export function getStudentExamAnalysis(data) {
  return request.get(baseURL4 + '/tutoring/ai/hasNewExam', data)
}

/* 学生活动是否开通 */
export function getIsActivityOn(data) {
  return request.get(baseURL + '/v3/student/activity/open', data)
}

/* 修改密码 */
export function updatePassword(data) {
  return request.post(baseURL + '/v3/user/changePwd', data, {
    showTip: false,
    returnAll: true,
  })
}
