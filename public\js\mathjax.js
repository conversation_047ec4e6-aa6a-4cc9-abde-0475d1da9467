window.MathJax = {
  loader: { load: ['[tex]/noerrors'] },
  tex: {
    packages: { '[+]': ['noerrors'] },
    inlineMath: [
      ['$', '$'],
      ['\\(', '\\)'],
    ], // ⾏内公式选择符
    displayMath: [
      ['$$', '$$'],
      ['\\[', '\\]'],
    ], // 段内公式选择符
    processEscapes: true,
  },
  options: {
    enableMenu: false,
    ignoreHtmlClass: 'tex2jax_ignore',
    processHtmlClass: 'tex2jax_process',
  },
}
