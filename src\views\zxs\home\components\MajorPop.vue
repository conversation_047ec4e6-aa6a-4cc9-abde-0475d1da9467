<template>
  <van-popup
    v-bind="$attrs"
    teleport="#app"
    position="bottom"
    round
    closeable
    class="p-20px max-h-[90%]"
  >
    <div
      class="text-18px font-500 text-[#333333] lh-[40px] mb-15px flex flex-col items-center"
    >
      {{ aiMajorInfo.professional_name }}
    </div>
    <div class="text-14px font-500 text-[#333333] lh-[28px] mb-10px">
      专业介绍
    </div>
    <div
      class="bg-[#F3F5F6] rounded-[8px] p-10px text-14px font-400 text-[#333333] lh-[28px] mb-14px break-words"
    >
      {{ aiMajorInfo.professional[0].professional_introduce }}
    </div>
    <div class="text-14px font-500 text-[#333333] lh-[28px] mb-10px">
      包含专业<span class="text-12px ml-4px">(点击专业可查看专业详情)</span>
    </div>
    <div class="flex flex-wrap">
      <span
        v-for="(itm, idx) in aiMajorInfo.list"
        :key="idx"
        class="bg-[#F3F5F6] rounded-[8px] text-14px font-400 text-[#333333] lh-[32px] text-center pl-10px pr-10px mr-10px mb-10px"
        @click="goMajorDetail(itm)"
        >{{ itm }}</span
      >
    </div>
  </van-popup>
</template>
<script setup lang="ts">
import { getMajorDetail } from '@/api/aiStudentPartner'
const props = defineProps({
  aiMajorInfo: {
    type: Object,
    default: () => ({}),
  },
})

function goMajorDetail(item) {
  try {
    getMajorDetail({
      keyword: item,
    }).then((res) => {
      $g.flutter('nativeRoute', {
        name: '/majorDetail',
        backCallJsRefresh: false,
        params: {
          majorId: String(res),
        },
      })
    })
  } catch (e) {
    console.error(e)
  }
}
</script>
