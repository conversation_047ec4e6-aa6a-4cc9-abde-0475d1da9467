<template>
  <div>
    <g-navbar
      ref="navBarRef"
      :customTitle="$route.query?.type == 'HXN' ? '逆袭课程' : '单元闯关'"
    >
      <div class="h-[82px]">
        <div class="bg-[#FFFFFF] w-full z-20">
          <div>
            <van-dropdown-menu>
              <van-dropdown-item
                v-if="phaseList?.length"
                v-model="currentPhase"
                :options="phaseList"
              />
              <van-dropdown-item
                v-if="textbookList?.length"
                v-model="currentTextbook"
                :options="textbookList"
              />
              <van-dropdown-item
                v-if="bookList?.length"
                v-model="currentBook"
                :options="bookList"
              />
            </van-dropdown-menu>
          </div>
          <van-tabs
            v-model:active="currentSubject"
            title-inactive-color="#969799"
          >
            <van-tab
              v-for="(item, index) in subjectList"
              :key="index"
              :title="item?.sys_subject_name"
              :name="item?.jk_subject_id"
            ></van-tab>
          </van-tabs>
        </div>
      </div>
    </g-navbar>
    <div
      class="mx-[16px] mt-[25px] overflow-y-auto"
      :style="{
        height: 'calc(100 % - $g.navBarTotalHeight) - 82px',
      }"
    >
      <g-loading v-if="treeLoading" class="h-[200px]"></g-loading>
      <div v-else>
        <div v-if="treeData?.length">
          <Card
            v-for="(cardItem, cardIndex) in treeData"
            :key="cardIndex"
            :data="cardItem"
            :currentSubject="currentSubject"
            class="mb-[16px]"
            @on-parent-change="onParentChange"
            @on-child-change="onChildChange"
          ></Card>
        </div>
        <g-empty v-else></g-empty>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup name="UnitMain">
import {
  getPhaseSelectApi,
  getSubjectListApi,
  getTextbookEditionApi,
  getLeftChapterListApi,
  getChildrenApi,
} from '@/api/unitPass'
import { useUserStore } from '@/stores/modules/user'
import { useUnitStore } from '@/stores/modules/unitPass'
import Card from './components/Card.vue'
let phaseList: any = $ref([])
let currentPhase: any = $ref(null)
let currentSubject: any = $ref(null)
let currentBook: any = $ref(null)
const unitStore = useUnitStore()
const { active } = storeToRefs(unitStore)
let currentChapter: any = $ref({})
let currentTextbook: any = $ref(null)
let textbookList: any = $ref([])
let subjectList: any = $ref([])
let treeLoading: any = $ref(false)
let treeData: any = $ref([])
let bookList: any = $ref([])
const userStore = useUserStore()
let isHxn = $computed(() => {
  return userStore.userInfo?.studentDetail?.schoolId == 44
})
const navBarRef = $ref<any>(null)
async function getPhaseSelect() {
  try {
    const res = await getPhaseSelectApi()
    phaseList = res.map((item: any) => {
      return {
        ...item,
        text: item.title,
        value: item.id,
      }
    })
    currentPhase = phaseList.filter((item: any) => item.selected)[0]?.value
  } catch (e) {
    console.error(e)
  }
}
/**
 * @param {*} item 章节节点
 * @param {*} page 页码
 */
function onParentChange({ item }) {
  if (currentSubject == 2 && isHxn) {
    treeData.map((treeItem, index) => {
      if (treeItem?.id != item.id && index != 0) {
        treeItem.expand = false
      }
    })
  } else {
    treeData.map((treeItem) => {
      if (treeItem?.id != item.id) {
        treeItem.expand = false
      }
    })
  }
  if (item.expand) {
    initList(item)
  }
}

/**
 * @param {*} item 章节节点
 * @param {*} page 页码
 */
function initList(item) {
  currentChapter = item
  getChildren(item)
}
// 获取子节点
/**
 * @param {*} item 章节节点
 * @param {*} update 是否是更新 更新表示忽略已加载判断 不显示 loading
 */
async function getChildren(treeItem, update = false) {
  if (!currentChapter?.sys_textbooks_catalog_id) return
  try {
    const setActiveChild = () => {
      active.value = false
    }
    // 已加载
    if (treeItem.loaded && !update) {
      setActiveChild()
      return
    }

    if (treeItem.loading && !update) return
    if (!update) treeItem.loading = true
    const res = await getChildrenApi({
      sysTextbooksCatalogId: currentChapter?.sys_textbooks_catalog_id,
    })
    // 找到对应章节
    const index = treeData.findIndex((e) => e?.id === treeItem?.id)
    treeData[index].loading = false
    treeData[index].loaded = true
    treeData[index].children = res
      .map((item) => {
        return {
          ...item,
          id: item?.sys_textbooks_catalog_id,
          jk_new_chapter_name: item.sys_textbooks_catalog_name_alias,
        }
      })
      .filter((item) => ![1, 3].includes(item?.type))
    setActiveChild()
  } catch (err) {
    console.log(err)
  }
}
/**
 * @param {*} treeItem 章节节点
 * @param {*} item 子节点
 */
const onChildChange: any = ({ treeItem, item }) => {
  if (treeItem?.id != currentChapter?.id) {
    currentChapter = treeItem
  }
}
// 获取章节列表
const getLeftChapterList = async () => {
  try {
    treeLoading = true
    const book = currentBook
    const res = await getLeftChapterListApi({
      sysTextbooksId: currentBook,
    })
    treeLoading = false
    if (book != currentBook) {
      return
    }
    res.map((item, index) => {
      item.index = index
      item.expand = index == 0 ? true : false
      item.page = index + 1
      item.loaded = false
      item.leading = true
      item.children = []
      item.list = null
      item['jk_new_chapter_name'] = item.sys_textbooks_catalog_name_alias
      item['id'] = item?.sys_textbooks_catalog_id
    })
    currentChapter = res[0]
    treeData = res
    onParentChange({ item: res[0] })
  } catch (error) {
    treeLoading = false
  }
}
watch(
  () => currentPhase,
  () => {
    subjectList = null
    currentSubject = null
    if (currentPhase) {
      getSubjectList()
    }
  },
)
watch(
  () => currentSubject,
  () => {
    currentTextbook = null
    textbookList = []
    if (currentSubject && currentSubject != '-1') {
      getTextbookEdition()
    }
  },
)
async function getSubjectList() {
  const res = await getSubjectListApi({ periodLevel: currentPhase })
  subjectList = res?.length
    ? [{ sys_subject_name: '全部', jk_subject_id: '-1' }, ...res]
    : []
  currentSubject = res?.[0]?.jk_subject_id ?? ''
  if (!currentSubject) {
    treeLoading = false
  }
}
async function getTextbookEdition() {
  try {
    const res =
      (await getTextbookEditionApi({
        subjectId: currentSubject,
        periodLevel: currentPhase,
        setVersion: true,
      })) || []
    if (!res.length) treeLoading = false

    textbookList = res
      ?.filter(
        (h) =>
          ![296, 288, 298, 305, 456, 436].includes(h.sys_textbook_versions_id),
      )
      .map((item) => {
        return {
          ...item,
          text: item.sys_textbook_versions_name_alias,
          value: item.sys_textbook_versions_id,
        }
      })
    textbookList = res
      ?.filter(
        (h) =>
          ![296, 288, 298, 305, 456, 436].includes(h.sys_textbook_versions_id),
      )
      .map((item) => {
        return {
          ...item,
          text: item.sys_textbook_versions_name_alias,
          value: item.sys_textbook_versions_id,
        }
      })
    const findItem = textbookList.find((item: any) => item.checked)
    currentTextbook = findItem ? findItem?.value : textbookList[0]?.value
    if (!currentTextbook) {
      treeLoading = false
    }
  } catch (e) {
    console.error(e)
  }
}
watch(
  () => currentTextbook,
  () => {
    bookList = []
    currentBook = null
    if (currentTextbook) {
      setTimeout(() => {
        bookList =
          textbookList
            .find((item: any) => {
              return item?.sys_textbook_versions_id == currentTextbook
            })
            ?.children?.map((item) => {
              return {
                ...item,
                text: item?.sys_textbooks_name_alias,
                value: item?.sys_textbooks_id,
              }
            }) || []
        currentBook = bookList[0]?.value
      }, 100)
    }
  },
)
watch(
  () => currentBook,
  () => {
    treeData = []
    if (currentBook) {
      treeLoading = true
      getLeftChapterList()
    }
  },
)
onActivated(() => {
  if (unitStore.active && currentChapter) {
    getChildren(currentChapter, true)
  }
})

//适配折叠屏，因为此页面有缓存，去了其他页面再回此页面，需要加载navbar
function onResize() {
  navBarRef?.changeUpdateKey()
}
onMounted(() => {
  getPhaseSelect()
  window.addEventListener('resize', onResize)
})
onBeforeUnmount(() => {
  window.removeEventListener('resize', onResize)
})
</script>
<style scoped lang="scss">
:deep() {
  .van-tabs {
    margin: 0 29px 0 10px;
  }
  .van-tab:first-child {
    display: none;
  }
  .van-dropdown-menu {
    margin-left: 20px;
    margin-right: 35px;
  }
  .van-dropdown-menu__item {
    max-width: 140px;
  }
  .van-dropdown-menu__bar {
    box-shadow: none;
    justify-content: space-between;
  }
  .van-tabs__line {
    width: 28px;
  }
  .van-dropdown-menu__title:after {
    border-color: transparent transparent #1e2930 #1e2930;
  }
  .van-dropdown-menu__item {
    flex: none;
  }
}
</style>
