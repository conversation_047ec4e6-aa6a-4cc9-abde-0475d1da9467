<template>
  <div class="h-full overflow-auto no-bar pb-14px">
    <div class="text-[#999] text-12px mt-25px">
      <div class="text-22px font-600 text-[#190101]">
        Hello,{{ userInfo.studentName }}
      </div>
      <div class="text-13px text-[#170601] mt-2px">
        启鸣好学 · 中高考提分专家
      </div>
    </div>
    <img
      class="mt-15px mx-auto w-full"
      src="@/assets/img/home/<USER>"
      @click="
        $router.push({
          name: 'UnitPass',
          query: {
            type: 'HXN',
          },
        })
      "
    />
    <div class="mt-20px">
      <div class="flex items-center">
        <img :src="$g.tool.getFileUrl('home/AI.png')" class="w-21px h-15px" />
        <span class="ml-2px text-16px AlimamaShuHeiTi">升学</span>
      </div>
      <div class="grid grid-cols-4 justify-between mt-16px br-[8px]">
        <div
          v-for="item in hxnIcon"
          :key="item.id"
          class="van-haptics-feedback flex-col justify-center items-center"
          @click="androidJump({ data: item })"
        >
          <img :src="item.image" class="w-[36px] h-[36px] m-auto" />
          <div class="text-14px text-center mt-5px">{{ item.title }}</div>
        </div>
      </div>
      <StraightTrain></StraightTrain>
      <div class="pt-13px">
        <img
          :src="$g.tool.getFileUrl('home/bottom-help.png')"
          class="w-130px h-14px m-auto"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from '@/stores/modules/user'
import StraightTrain from './StraightTrain.vue'
import androidJump from '../androidJump'

const userStore: any = useUserStore()
const props = defineProps({
  userInfo: {
    type: Object,
    default: () => {},
  },
})
const router = useRouter()
const hxnIcon: any = [
  {
    id: 1,
    title: '目标成绩',
    image: $g.tool.getFileUrl('home/ai-target.png'),
    redirect_url: '/zxs/aiStudentPartner/targetScore',
  },
  {
    id: 2,
    title: '智推院校',
    image: $g.tool.getFileUrl('home/ai-school.png'),
    redirect_url: '/zxs/aiStudentPartner/recommendSchool',
  },
  {
    id: 3,
    title: '学习力测评',
    image: $g.tool.getFileUrl('home/ai-study.png'),
    redirect_url: `${import.meta.env.VITE_YX_APP_URL}/#/career/main`,
    landscape: false,
    updateData: false,
  },
  {
    id: 4,
    title: '智推专业',
    image: $g.tool.getFileUrl('home/ai-professional.png'),
    redirect_url: '/zxs/aiStudentPartner/recommendMajor',
  },
]
</script>

<style lang="scss" scoped>
.no-bar {
  &::-webkit-scrollbar {
    display: none;
  }
}
</style>
