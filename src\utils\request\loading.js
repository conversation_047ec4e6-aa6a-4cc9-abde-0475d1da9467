const loading = {
  loading: false, // Loading 实例
  loadingCount: 0,
  open: function () {
    this.loadingCount++

    if (!this.loading) {
      $g.showLoadingToast({
        duration: 0,
        message: '加载中...',
        forbidClick: true,
      })
      this.loading = true
    }
  },
  close: function () {
    this.loadingCount--
    if (this.loadingCount < 0) this.loadingCount = 0
    if (this.loadingCount === 0) {
      $g.closeToast()
      this.loading = false
    }
  },
}

export default loading
