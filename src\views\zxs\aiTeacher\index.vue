<template>
  <div>
    <g-navbar customTitle="AI讲解" customRight class="header">
      <template #csr>
        <div class="flex items-center">
          <div class="mr-16px flex items-center">
            <img
              v-if="!isAudio"
              class="w-17px h-auto van-haptics-feedback"
              alt="audio-close"
              :src="$g.tool.getFileUrl('aiTeacher/audio-close.png')"
              @click="changeAudio(true)"
            />
            <img
              v-else
              class="w-17px h-auto van-haptics-feedback"
              alt="play-black"
              :src="$g.tool.getFileUrl('aiTeacher/play-black.png')"
              @click="changeAudio(false)"
            />
          </div>
          <div class="mr-14px van-haptics-feedback" @click="clearMessage">
            <img
              src="@/assets/img/aiTeacher/brash.png"
              alt="brash"
              class="w-16px h-16px"
            />
          </div>
        </div>
      </template>
    </g-navbar>
    <div
      :style="{
        height: `calc(100vh - ${Math.ceil($g.navBarTotalHeight)}px)`,
      }"
      class="flex flex-col"
    >
      <div
        v-if="$g.tool.isTrue(questionData)"
        class="py-7px bg-white flex justify-center flex-shrink-0"
      >
        <div
          class="px-11px py-7px bg-[#F4F4F4] text-center text-14px br-[8px] flex items-center van-haptics-feedback"
          @click="noteShow = true"
        >
          <img
            src="@/assets/img/aiTeacher/note.png"
            class="w-15px h-15px mr-5px"
          />
          <div>查看笔记</div>
        </div>
        <div
          class="px-11px py-7px bg-[#F4F4F4] text-center text-14px br-[8px] flex items-center mx-18px van-haptics-feedback"
          @click="jumpDetail"
        >
          <img
            src="@/assets/img/aiTeacher/question.png"
            class="w-15px h-15px mr-5px"
          />
          <span>查看原题</span>
        </div>
        <div
          class="px-11px py-7px bg-[#F4F4F4] text-center text-14px br-[8px] flex items-center van-haptics-feedback"
          @click="openChangePop"
        >
          <img
            src="@/assets/img/aiTeacher/change-ai.png"
            class="w-15px h-15px mr-5px"
          />
          <span>切换老师</span>
        </div>
      </div>
      <g-loading v-if="showLoading" class="h-200px"></g-loading>
      <template v-else>
        <g-empty v-if="!$g.tool.isTrue(questionData)"></g-empty>
        <template v-else>
          <div
            v-if="questionData?.subQuestions?.length > 1"
            class="w-full pt-16px flex text-12px text-[#141414FF] flex-shrink-0"
          >
            <span class="mr-10px ml-16px text-16px font-600 flex-shrink-0"
              >选择题目</span
            >
            <div class="flex flex-1 overflow-auto no-bar">
              <div
                v-for="(item, index) in questionData.subQuestions"
                :key="item.subQuestionId"
                class="px-10px h-24px flex-cc border border-[#FFC61380] bg-[#FFF7DFED] rounded-[12px] flex-shrink-0 mr-10px van-haptics-feedback"
                :class="{
                  '!border-[transparent] !bg-[#FFC613FF] font-500':
                    subQuestionId === item.subQuestionId,
                }"
                @click="setCurrent(item.subQuestionId)"
              >
                第({{ index + 1 }})小问
              </div>
            </div>
          </div>
          <div class="min-h-[0px] flex-grow">
            <MingTeacher
              v-if="teacherID == 1"
              ref="teacherRef"
              :sub-question-id="subQuestionId"
              :question-id="questionData?.questionId"
              :input-info="inputInfo"
              :delete-note-id="deleteNoteId"
              :is-audio="isAudio"
              @save-note="getNoteListApi"
              @ming-change="mingChange"
            />
            <QiTeacher
              v-else
              ref="teacherRef"
              :sub-question-id="subQuestionId"
              :question-id="questionData?.questionId"
              :delete-note-id="deleteNoteId"
              :input-info="inputInfo"
              :is-audio="isAudio"
              @save-note="getNoteListApi"
              @qi-change="qiChange"
            />
          </div>
        </template>
      </template>
    </div>
    <TeacherDialog
      v-model:show="showPop"
      :select-i-d="selectID"
      @confirm="confirm"
      @change-id="changeId"
    ></TeacherDialog>
    <NoteList
      v-model:show="noteShow"
      :noteList="noteList"
      :isDeleteNoteFlag="isDeleteNoteFlag"
      @delete-note-api="deleteNoteApi"
    ></NoteList>
  </div>
</template>

<script setup lang="ts" name="AiTeacherMain">
import { showConfirmDialog } from 'vant'
import MingTeacher from './components/MingTeacher.vue'
import QiTeacher from './components/QiTeacher.vue'
import TeacherDialog from './components/TeacherDialog.vue'
import NoteList from './components/NoteList.vue'
import {
  getQuestionDetail,
  getStudyNode,
  getAiTeacher,
  accountInfo,
  changeAiTeacher,
  deleteNote,
} from '@/api/aiTeacher'
import { useSpeakerStore } from '@/stores/modules/speaker'

let speakerStore = useSpeakerStore()

const route = useRoute()
const router = useRouter()
const noteBox = ref<any>()
const teacherRef = $ref<any>()

let questionData = $ref<any>(null)
let showLoading = $ref(true)
let inputInfo: any = $ref({}) //用户是否能自由对话信息
let subQuestionId = $ref(0) //选中子题id
let showPop = $ref(false) //弹框是否展示
let teacherID = $ref(0) //当前老师id
let selectID = $ref(0) //弹框选中的老师id
let mingLoading = $ref(false) //小鸣老师说话加载中
let qiLoading = $ref(false) //小启老师说话加载中
let noteList = $ref<any[]>([]) //笔记
let deleteNoteId: any = $ref('') //删除的笔记id
let noteShow = $ref(false)
let isAudio = $ref(false)

const isTutorial = $computed(() => {
  return Number(route.query.errorType) == 2
})

function changeId(id) {
  selectID = id
}

function setCurrent(id) {
  if (subQuestionId == id) return
  if ((mingLoading && teacherID == 1) || (qiLoading && teacherID == 2)) {
    return $g.showToast('当前对话进行中，请稍后重试！')
  }
  speakerStore.reset()
  noteList = []
  subQuestionId = id
}

//获取用户是否能自由对话
async function accountInfoApi() {
  try {
    const data = await accountInfo()
    inputInfo = data
  } catch (error) {
    console.log('error => ', error)
  }
}

//获取默认老师
async function getAiTeacherApi() {
  try {
    const data = await getAiTeacher({
      type: 'AI_APP_DEFAULT',
      accountId: inputInfo.accountId,
    })
    teacherID = data.chatRoomType
    selectID = teacherID
  } catch (error) {
    console.log('error => ', error)
  }
}

//获取试题信息
async function getQuestionDetailApi() {
  if (!route.query.questionId) return
  try {
    showLoading = true
    const data = await getQuestionDetail({
      questionId: route.query.questionId,
      questionIdFrom: isTutorial ? 7 : null,
    })
    questionData = data || null
    subQuestionId = data?.subQuestions?.[0]?.subQuestionId || 0
    questionData?.subQuestions?.forEach((v) => {
      if ([1, 2].includes(v.subQuestionType)) {
        v.options = Object.keys(v)
          .filter(
            (key) => key.includes('option') && v[key] && key !== 'optionNumber',
          )
          .map((realKey) => {
            return {
              name: realKey.charAt(realKey.length - 1).toLocaleUpperCase(),
              title: v[realKey],
            }
          })
      } else if (v.subQuestionType == 3) {
        v.options = [
          {
            id: 1,
            name: '√',
            title: null,
          },
          {
            id: 2,
            name: '×',
            title: null,
          },
        ]
      }
    })
  } catch (e) {
    showLoading = false
    console.log('e => ', e)
  } finally {
    showLoading = false
  }
}

function mingChange(val) {
  mingLoading = val
}

function qiChange(val) {
  qiLoading = val
}

function openChangePop() {
  if ((mingLoading && teacherID == 1) || (qiLoading && teacherID == 2)) {
    return $g.showToast('当前对话进行中，请稍后重试！')
  }
  selectID = teacherID
  showPop = true
}

//弹框确认
function confirm() {
  showPop = false
  if (selectID !== teacherID) {
    teacherID = selectID
    if (teacherID == 1) {
      mingLoading = false
    } else {
      qiLoading = false
    }
    speakerStore.reset()
    noteList = []
    accountInfoApi()
    changeAiTeacher({
      type: 'AI_APP_DEFAULT',
      config: { chatRoomType: selectID },
      accountId: inputInfo.accountId,
    })
  }
}

//获取笔记
async function getNoteListApi(params) {
  deleteNoteId = ''
  const data = await getStudyNode({ ...params, accountId: inputInfo.accountId })
  noteList = data
    .map((v) => ({
      ...v,
      content: v.content,
    }))
    .filter((v) => v.content && v.content !== 'null')
}

// 标记是否是删除笔记，如果删除笔记，面板不滚动到底部
let isDeleteNoteFlag = false

//删除笔记
function deleteNoteApi(data) {
  isDeleteNoteFlag = true
  showConfirmDialog({
    title: '删除笔记',
    message: '删除笔记后不可恢复，是否删除？',
  })
    .then(async () => {
      await deleteNote({ fastGptStudyNoteId: data.fastGptStudyNoteId })
      deleteNoteId = data.fastGptChatRecordId
      let index = noteList.findIndex(
        (item) => item.fastGptStudyNoteId == data.fastGptStudyNoteId,
      )
      noteList.splice(index, 1)
    })
    .catch((err) => {
      console.log('删除笔记失败', err)
    })
    .finally(() => {
      setTimeout(() => {
        isDeleteNoteFlag = false
      }, 100)
    })
}

//获取是否开启音频
async function getIsAudio() {
  const data = await getAiTeacher({
    type: 'QUESTION_TTS_SWITCH',
    accountId: inputInfo.accountId,
  })
  isAudio = data.status
}

//修改音频状态
async function changeAudio(status) {
  speakerStore.reset()
  const data = await changeAiTeacher({
    type: 'QUESTION_TTS_SWITCH',
    accountId: inputInfo.accountId,
    config: { status },
  })
  isAudio = status
}

onBeforeUnmount(() => {
  document.removeEventListener('visibilitychange', pageVisibilityChange)
  speakerStore.reset()
})

//清除对话
function clearMessage() {
  speakerStore.reset()
  teacherRef?.clearMessage()
}

function jumpDetail() {
  teacherRef?.jumpDetail()
}

//当前页面是否可见，判断用户是否隐藏应用或熄屏
function pageVisibilityChange() {
  if (document?.visibilityState === 'hidden') {
    speakerStore.audio && speakerStore.pause()
  } else if (document?.visibilityState === 'visible') {
    speakerStore.audio && speakerStore.play()
  }
}

onBeforeMount(async () => {
  document.addEventListener('visibilitychange', pageVisibilityChange)
  await accountInfoApi()
  getIsAudio()
  getAiTeacherApi()
  getQuestionDetailApi()
})
</script>

<style lang="scss" scoped>
.header {
  :deep() {
    .van-nav-bar__right {
      opacity: 1 !important;
    }
  }
}
</style>
