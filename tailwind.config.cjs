/** @type {import('tailwindcss').Config} */
const plugin = require('tailwindcss/plugin')
const spacing = Array.from({ length: 1000 }, (_, index) => index + 1).reduce(
  (acc, curr) => ({ ...acc, [`${curr}px`]: `${curr}px` }),
  {},
)

module.exports = {
  important: '#app',
  darkMode: 'class',
  content: ['./index.html', './src/**/*.{vue,js,ts,jsx,tsx}'],
  theme: {
    colors: {
      theme: {
        primary: 'var(--van-blue)',
        success: 'var(--van-green)',
        warning: 'var(--van-orange)',
        danger: 'var(--van-red)',
        error: 'var(--van-red)',
      },
      blue: {
        lightest: 'var(--t-colors-blue-lightest)',
        default: 'var(--van-blue)',
        dark: 'var(--t-colors-blue-dark)',
      },
      green: {
        light: 'var(--t-colors-green-light)',
        default: 'var(--van-green)',
        dark: 'var(--t-colors-green-dark)',
      },
      yellow: {
        light: 'var(--t-colors-yellow-light)',
        default: 'var(--van-orange)',
        dark: 'var(--t-colors-yellow-dark)',
      },
      red: {
        lightest: 'var(--t-colors-red-lightest',
        default: 'var(--van-red)',
        dark: 'var(--t-colors-red-dark)',
      },
      gray: {
        lightest: 'var(--t-colors-gray-lightest)',
        light: 'var(--t-colors-gray-light)',
        default: 'var(--t-colors-gray-default)',
        dark: 'var(--t-colors-gray-dark)',
        darkest: 'var(--t-colors-gray-darkest)',
      },
      white: 'var(--t-colors-white)',
      black: 'var(--t-colors-black)',
      transparent: 'var(--transparent)', // 透明色
    },
    extend: {
      spacing,
    },
    fontSize: {
      ...spacing,
    },
    maxHeight: {
      ...spacing,
    },
    minHeight: {
      ...spacing,
    },
    maxWidth: {
      ...spacing,
    },
    minWidth: {
      ...spacing,
    },
    fontWeight: {
      400: '400',
      500: '500',
      600: '600',
      700: '700',
      800: '800',
      900: '900',
    },
  },
  plugins: [
    plugin(function ({ addComponents, matchUtilities, theme }) {
      matchUtilities(
        {
          lh: (value) => ({
            'line-height': value,
          }),
        },
        { values: theme('lh') },
      )

      matchUtilities(
        {
          br: (value) => ({
            'border-radius': value,
          }),
        },
        { values: theme('br') },
      )

      matchUtilities(
        {
          fw: (value) => ({
            'font-weight': value,
          }),
        },
        { values: theme('fw') },
      )

      addComponents({})
    }),
  ],
  corePlugins: {
    backgroundOpacity: false,
    textOpacity: false,
  },
}
