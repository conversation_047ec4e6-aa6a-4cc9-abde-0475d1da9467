<template>
  <div
    v-if="item.id"
    class="px-16px"
    :class="[item?.buttonList?.length ? 'pb-14px' : 'pb-20px']"
  >
    <div v-if="item.ai" class="mr-12px flex-shrink-0 flex items-center">
      <img
        :src="$g.tool.getFileUrl('aiTeacher/teacher.png')"
        class="w-32px h-32px"
        fit="contain"
      />
      <span class="text-14px ml-8px">{{
        role == 'qi' ? '小启老师' : '小鸣老师'
      }}</span>
      <span class="text-12px ml-4px text-[#999]">{{
        role == 'qi' ? '精准回答步骤问题' : '逐步引导解题思路'
      }}</span>
    </div>
    <div
      class="w-full flex overflow-hidden"
      :class="item.ai ? 'mt-10px' : 'justify-end'"
    >
      <div class="max-w-[100%]">
        <div
          class="note-box bg-white w-fit max-w-[100%] overflow-x-auto p-3px br-[12px] min-h-[36px]"
          :class="
            item.ai
              ? '!rounded-tl-[0px]'
              : '!rounded-br-[0px] !bg-[#FFC613FF] font-500'
          "
        >
          <div class="w-full overflow-x-auto overflow-y-hidden">
            <g-markdown
              v-show="!item?.loading"
              :key="item.id"
              :text="item.content || '数据错误！'"
              :cursor="item.speakLoading"
              mode="stream"
            ></g-markdown>
            <div v-show="item?.loading" class="h-34px flex-cc">
              <div class="qm-chat-cursor"></div>
            </div>
          </div>
          <div
            v-if="item.ai && !item.speakLoading && isAudio && item.content"
            class="pt-8px border-t-[1px] mt-8px mb-4px border-[#eee] flex items-center justify-between"
          >
            <div
              class="br-[6px] w-24px h-24px van-haptics-feedback flex-cc bg-[#FF74001A] cursor-pointer"
              @click="playAudio(item)"
            >
              <span v-if="getPlaybackStatus(item) == 'loading'">
                <g-lottie :options="lottieOptions" @anim-created="animCreated">
                </g-lottie>
              </span>
              <img
                v-else-if="getPlaybackStatus(item) == 'start'"
                class="w-14px h-14px"
                src="@/assets/img/aiTeacher/pause.png"
                alt="start"
              />
              <img
                v-else
                class="w-16px h-12px"
                src="@/assets/img/aiTeacher/play.png"
                alt="play"
              />
            </div>
            <div
              v-if="item.questionTtsId"
              class="flex text-14px text-[#FFA313FF]"
            >
              <div
                class="px-5px h-24px bg-[#FF74001A] mr-24px flex-cc br-[6px] cursor-pointer"
                @click="feedbackState(2, item)"
              >
                <img
                  class="w-14px h-14px mr-3px"
                  :src="
                    item.likeClick
                      ? $g.tool.getFileUrl('aiTeacher/like-click.png')
                      : $g.tool.getFileUrl('aiTeacher/like.png')
                  "
                />
                <span class="leading-[24px]">音频</span>
              </div>
              <div
                class="px-5px h-24px bg-[#FF74001A] flex-cc br-[6px] cursor-pointer"
                @click="feedbackState(3, item)"
              >
                <img
                  class="w-14px h-14px mr-3px"
                  :src="
                    item.dislikeClick
                      ? $g.tool.getFileUrl('aiTeacher/dislike-click.png')
                      : $g.tool.getFileUrl('aiTeacher/dislike.png')
                  "
                />
                <span class="leading-[24px]">音频</span>
              </div>
            </div>
          </div>
        </div>
        <div
          v-if="item?.buttonList?.length && item.content"
          class="mt-10px flex flex-wrap"
        >
          <div
            v-for="(v, vIndex) in item.buttonList"
            :key="vIndex"
            class="mr-12px mb-6px text-14px"
            @click="emit('buttonClick', v, vIndex)"
          >
            <button
              v-if="v.type == 1"
              class="br-[4px] aiButton flex-cc leading-[24px] flex-cc"
              :class="className(v)"
            >
              <img
                :src="
                  v.isClick
                    ? $g.tool.getFileUrl('aiTeacher/save-yes.png')
                    : $g.tool.getFileUrl('aiTeacher/save.png')
                "
                class="w-14px h-14px mr-5px"
              />
              {{ v.isClick ? '笔记已保存' : v.name }}
            </button>
            <button
              v-else
              class="br-[4px] aiButton flex-cc leading-[24px]"
              :class="className(v)"
            >
              {{ v.name }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useSpeakerStore } from '@/stores/modules/speaker'
import hourglass from './hourglass.json'

const lottieOptions = {
  animationData: hourglass,
  loop: true,
  renderer: 'svg',
  autoplay: true,
  speed: 20,
}

function animCreated(anim) {
  anim.setSpeed(1)
}

let speakerStore = useSpeakerStore()
let { getIdByType, isPlay, getQuestionTtsId } = $(
  storeToRefs(useSpeakerStore()),
)
const props = defineProps({
  item: {
    type: Object,
    default: () => {},
  },
  role: {
    type: String,
    default: 'qi',
  },
  isAudio: {
    type: Boolean,
    default: false,
  },
  accountId: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['buttonClick'])

watch(
  () => getQuestionTtsId,
  (val) => {
    if (val) {
      let id = props.item?.subQuestionParseId || props.item.id
      if (id == getIdByType && props.item?.questionTtsId != val) {
        props.item.questionTtsId = getQuestionTtsId
        speakerStore.userFeedback(1, getQuestionTtsId)
      }
    }
  },
)

function feedbackState(state, item) {
  if (!speakerStore.accountId) speakerStore.accountId = props.accountId
  //取消点赞或取消差评
  if ((state == 2 && item.likeClick) || (state == 3 && item.dislikeClick)) {
    state == 2 ? (item.likeClick = false) : (item.dislikeClick = false)
    speakerStore.userFeedback(1, item.questionTtsId)
    return
  }
  if (state == 2 && !item.likeClick) {
    item.likeClick = true
    item.dislikeClick = false
  }
  if (state == 3 && !item.dislikeClick) {
    item.dislikeClick = true
    item.likeClick = false
  }
  speakerStore.userFeedback(state, item.questionTtsId)
}

/* 音频播放 */
async function playAudio(data) {
  if (data?.subQuestionParseId) {
    speakerStore.getAudioList(5, data)
  } else {
    speakerStore.getAudioListByMing(data)
  }
  if (!speakerStore.accountId) speakerStore.accountId = props.accountId
}
/* 音频状态 */
function getPlaybackStatus(item) {
  let id = item.subQuestionParseId || item.id
  if (id == getIdByType) {
    return isPlay
  } else {
    return 'pause'
  }
}

function className(v) {
  if (v.isClick) {
    return ' bg-[#FFC613] text-[#141414] font-500 border border-[transparent]'
  } else {
    return 'van-haptics-feedback bg-[white] text-[#FCBC00] border border-[#FFC613]'
  }
}
</script>

<style lang="scss" scoped>
.aiButton {
  min-width: 72px;
  height: 24px;
  text-align: center;
  font-size: 12px;
  padding: 0 12px;
}
.qm-chat-cursor {
  background-image: url(@/assets/img/aiTeacher/aiLoading.gif);
  background-size: cover;
  display: inline-block;
  height: 16px;
  margin-left: 4px;
  position: relative;
  top: 1px;
  width: 16px;
}
</style>
