<template>
  <div class="h-[calc(100vh)]">
    <g-navbar />
    <div>
      <g-loading v-if="showLoading" class="h-200px"></g-loading>
      <template v-else>
        <iframe
          v-if="pdfUrl"
          id="viewIframe"
          :src="pdfUrl"
          frameborder="0"
          width="100%"
          noresize="noresize"
          class="px-[12px] mt-10px"
          :style="{
            height: `calc(100vh - ${useSettingStore().navigationHeight}px - 70px) !important`,
          }"
        ></iframe>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getReportDetail } from '@/api/evaluation'
import { useSettingStore } from '@/stores/modules/setting'
const route = useRoute()
let showLoading = $ref(true)
let pdfUrl = $ref<any>('')
getReport()
async function getReport() {
  try {
    showLoading = true
    let res = await getReportDetail({
      student_report_id: route.query?.student_report_id,
    })
    showLoading = false
    console.log(res)
    pdfUrl = `//vip.ow365.cn/?i=28777&ssl=1&furl=${res.pdfUrl}`
  } catch (err) {
    console.log(err)
    showLoading = false
  }
}
</script>

<style lang="scss" scoped></style>
