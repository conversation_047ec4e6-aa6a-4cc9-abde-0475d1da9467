<template>
  <div>
    <g-navbar customTitle="启鸣错题本" />
    <div
      class="bg-white"
      :style="{
        minHeight: `calc(100vh - ${$g.navBarTotalHeight}px)`,
      }"
    >
      <van-dropdown-menu>
        <van-dropdown-item
          v-model="value1"
          :options="sourceList"
          title-class="border br-[3px] py-4px border-[#aaaaaa]"
          @change="sourceChange"
        >
          <template #title>
            <span :class="{ 'text-[#ababab]': value1 === '' }">
              {{ sourceTitle }}
            </span>
          </template>
        </van-dropdown-item>
        <van-dropdown-item
          ref="dateRef"
          v-model="value2"
          :options="dateList"
          title-class="border br-[3px] py-4px border-[#aaaaaa]"
          @change="change"
        >
          <template #title>
            <span :class="{ 'text-[#ababab]': value2 === '' }">
              {{ dateTitle }}
            </span>
          </template>
          <div
            class="py-10px px-12px flex justify-between items-center"
            :class="value2 == 3 ? 'text-[#3398f7]' : ''"
            @click="open"
          >
            <span>自定义</span>
            <span>图标</span>
          </div>
        </van-dropdown-item>
      </van-dropdown-menu>
    </div>
    <van-popup
      v-model:show="showPop"
      :style="{ padding: '10px' }"
      position="bottom"
      :close-on-click-overlay="false"
    >
      <van-picker-group
        v-model:active-tab="tab"
        :title="dateStr"
        :tabs="['开始日期', '结束日期']"
        @confirm="onConfirm"
        @cancel="cancel"
      >
        <van-date-picker
          v-model="startDate"
          :min-date="new Date(new Date().getFullYear() - 5, 0, 1)"
          :max-date="new Date(new Date().getFullYear() + 5, 11, 31)"
        />
        <van-date-picker
          v-model="endDate"
          :min-date="new Date(new Date().getFullYear() - 5, 0, 1)"
          :max-date="new Date(new Date().getFullYear() + 5, 11, 31)"
        />
      </van-picker-group>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
let sourceTitle: any = $ref('按来源筛选')
let dateTitle: any = $ref('按时间筛选')
let value1 = $ref('')
let value2: any = $ref('')
let startDate: any = $ref([])
let endDate: any = $ref([])
let showPop = $ref(false)
const dateRef: any = $ref(null)
let tab = $ref(0)

const dateStr = $computed(() => {
  return startDate.join('-') + ' 至 ' + endDate.join('-')
})

const sourceList = [
  { text: '全部', value: 0 },
  { text: '省级联考', value: 1 },
  { text: '市级联考', value: 2 },
  { text: '区县级联考', value: 3 },
  { text: '期末', value: 4 },
  { text: '期中', value: 5 },
  { text: '月考', value: 6 },
  { text: '周考', value: 7 },
  { text: '其他', value: 10 },
  { text: '混合联考', value: 11 },
]
const dateList = [
  { text: '近一年', value: 0 },
  { text: '近半年', value: 1 },
  { text: '近三月', value: 2 },
  { text: '近一月', value: 3 },
]

//时间下拉选择
function change(data) {
  let item = dateList.find((item) => item.value == data)
  dateTitle = item?.text
}

//来源选择
function sourceChange(data) {
  let item = sourceList.find((item) => item.value == data)
  sourceTitle = item?.text
}

//打开时间选择弹框
function open() {
  dateRef.toggle(false)
  tab = 0
  showPop = true
}

//日期选择确认
function onConfirm() {
  let start = $g.dayjs(startDate.join('-'))
  let end = $g.dayjs(endDate.join('-'))
  if (start.diff(end) > 0) {
    $g.showToast('结束日期不能小于开始日期！')
    return
  }
  dateTitle = String(dateStr)
  value2 = 3
  showPop = false
}

//取消
function cancel() {
  showPop = false
  //还原时间
  if (dateTitle.includes('至')) {
    let arr = dateTitle.split(' 至 ')
    startDate = arr[0].split('-')
    endDate = arr[1].split('-')
  } else {
    initDate()
  }
}

//初始化默认选中时间
function initDate() {
  startDate = $g.dayjs().format('YYYY-MM-DD').split('-')
  endDate = $g.dayjs().format('YYYY-MM-DD').split('-')
}

onMounted(() => {
  initDate()
})
</script>

<style lang="scss" scoped>
:deep() {
  .van-dropdown-menu__title {
    display: block;
    width: 90%;
    padding-right: 12px;
    &::after {
      right: 8px;
    }
  }
  .van-dropdown-menu__title--active {
    border-color: #1989fa !important;
  }
  .van-picker__title {
    max-width: 70%;
  }
  .van-dropdown-item {
    width: 100%;
    right: 0;
    left: 0;
  }
}
</style>
