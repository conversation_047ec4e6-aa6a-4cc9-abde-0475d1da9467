<template>
  <div class="list bg-[#f9f9f9]">
    <g-navbar
      ref="navBarRef"
      :customRight="true"
      :customTitle="route.query.title ? route.query.title : '智推院校'"
    >
      <template v-if="!route.query.noInternational" #csr>
        <div class="text-[13px] text-[#3496FA]" @click="onInterClick">
          查看国际院校
        </div>
      </template>
      <div class="py-[12px] w-full bg-[#F9F9F9]">
        <van-tabs
          v-model:active="tabNum"
          title-active-color="#333333"
          title-inactive-color="#333333"
          background="#EEEEEE"
          type="card"
        >
          <van-tab
            v-for="(tabItem, tabIndex) in tabList"
            :key="tabIndex"
            :title="tabItem?.label"
            :name="tabItem?.value"
          ></van-tab>
        </van-tabs>
      </div>
    </g-navbar>
    <div
      :style="{
        height: `calc(100vh - ${Math.ceil(settingStore.navigationHeight)}px - 100px)`,
      }"
    >
      <div class="mx-[10px]">
        <g-loading v-if="loading" class="h-200px"></g-loading>
        <g-list
          v-show="!loading"
          id="scroll"
          v-model:data="dataList"
          :page-option="pageOption"
          url="/v2/family/learn/getUniversityVolunteer"
          @pulldown="pulldown"
          @pullup="pullup"
        >
          <div
            v-for="(item, index) in dataList"
            :key="index"
            class="bg-[#FFFFFF] px-[7px] mb-[16px] w-full br-[12px] py-[14px]"
          >
            <div
              :style="{
                background:
                  'linear-gradient( 180deg, #EAF5FF 0%, #F4F9FF 100%)',
              }"
              class="pb-[16px] w-full br-[8px]"
            >
              <div class="flex relative" @click="onSchoolClick(item)">
                <div>
                  <div class="absolute left-[-5px] top-[9px]">
                    <img
                      :src="imgList?.[item?.probability]?.url"
                      class="w-[82px] h-[51px]"
                    />
                  </div>
                  <div
                    :style="{ letterSpacing: '-1.2px' }"
                    class="absolute flex justify-center w-[77px] left-[-5px] top-[12px] text-[18px] text-[#FFFFFF] font-700"
                  >
                    {{ item?.cwbStr }}&nbsp;·&nbsp;<span class="font-500"
                      >{{ item?.rate || 0
                      }}<span class="text-13px font-500"> %</span></span
                    >
                  </div>
                  <div
                    :style="{ color: imgList?.[item?.probability]?.color }"
                    class="absolute left-[7px] top-[43px] text-[#84A5FF] text-[12px] font-400"
                  >
                    {{ item?.probability }}
                  </div>
                </div>
                <div class="ml-[94px] relative flex flex-1 items-end mt-[9px]">
                  <div class="pb-[2px]">
                    <div
                      class="line-1 text-[16px] font-600"
                      :style="{ width: width }"
                    >
                      {{ item?.universityName }}
                    </div>
                    <div
                      :class="
                        item?.majorCount > 0 ? 'max-w-[160px]' : 'max-w-[240px]'
                      "
                      :style="{ letterSpacing: '-1.2px' }"
                      class="mt-[10px] text-[12px] text-[#333333]"
                    >
                      代码：{{ item?.recruitCode }}
                      <span class="ml-[10px]">
                        {{
                          `${item?.newYear}计划：${item?.lastPlanNum || 0}人`
                        }}
                      </span>
                    </div>
                  </div>
                  <van-button
                    v-if="item?.majorCount > 0"
                    type="primary"
                    class="min-w-[52px] absolute top-[30px] right-[12px] br-[13px] h-[22px] text-[12px]"
                    @click.stop="showEnrollPop(item)"
                    >专业{{ item?.majorCount }}</van-button
                  >
                </div>
              </div>
              <div
                v-if="item?.bonusRecognition || item?.tags?.length"
                class="px-[10px]"
              >
                <div class="bg-[#FFFFFF] br-[8px] mt-[11px] p-[10px] w-full">
                  <div
                    v-if="item?.bonusRecognition"
                    class="text-[14px] text-[#333333] font-400"
                  >
                    {{ item?.bonusRecognition }}
                  </div>
                  <div
                    v-if="item?.tags?.split('|')?.length"
                    class="text-[12px] text-[#999999] font-400 mt-[5px]"
                  >
                    {{ item?.tags?.split('|')?.join(' | ') }}
                  </div>
                </div>
              </div>
              <div class="w-full pl-[8px] pr-[12px] mt-[10px]">
                <g-table
                  :header-style="enrollHeaderStyle"
                  :row-style="{
                    fontWeight: '400',
                    fontSize: '13px',
                    color: '#333333',
                    height: '30px',
                  }"
                  :stripe="false"
                  :table-option="{
                    ...tableOption,
                    data: item?.hlist?.length
                      ? item?.hlist.map((item0) => {
                          return {
                            ...item0,
                            types: subjectObj[item?.types],
                          }
                        })
                      : [],
                  }"
                  class="enroll-table"
                  description="暂无数据"
                >
                  <template #recruitNum="{ row }">
                    {{ row.recruitNum ? row.recruitNum + '人' : '0人' }}
                  </template>
                </g-table>
              </div>
            </div>
          </div>
        </g-list>
      </div>
    </div>
    <van-popup
      v-model:show="showEnroll"
      class="bg-[#ffffff] p-20px max-h-[90%]"
      closeable
      position="bottom"
      round
      teleport="#app"
    >
      <div
        class="text-18px font-500 text-[#333333] lh-[40px] mb-15px flex flex-col items-center"
      >
        {{ currentInfo.universityName }}
      </div>
      <g-loading v-if="!loaded"></g-loading>
      <div v-for="(item, index) in dataEnroll" v-else :key="index">
        <EnrollItem :info="item"></EnrollItem>
      </div>
    </van-popup>
  </div>
</template>
<script lang="ts" setup>
import EnrollItem from '../components/EnrollItem.vue'
import { useSettingStore } from '@/stores/modules/setting'
import { getUniversityVolunteer, getMajorList } from '@/api/aiStudentPartner'
const router = useRouter()
let showEnroll = $ref<boolean>(false)
const route = useRoute()
let logo_host = $ref<any>('')
let loaded = $ref(false)
let tabNum = $ref<any>(0)
let currentInfo = $ref<any>(null)
let dataList = $ref<any>([])
let dataEnroll = ref<any>(null)
let loading = $ref<any>(false)
let width = $ref<any>('168px')
function onResize() {
  width = window.innerWidth > 412 ? '300px' : '168px'
}
onMounted(async () => {
  window.addEventListener('resize', onResize)
})
onBeforeUnmount(() => {
  window.removeEventListener('resize', onResize)
})
watch(
  () => window.innerWidth,
  () => {
    width = window.innerWidth > 412 ? '300px' : '168px'
  },
  {
    immediate: true,
  },
)
const settingStore = useSettingStore()
const subjectObj = {
  0: '文科',
  1: '理科',
  2: '历史类',
  3: '物理类',
  4: '综合类',
}
async function showEnrollPop(item) {
  try {
    currentInfo = item
    showEnroll = true
    loaded = false
    const res = await getMajorList({
      university_id: item?.universityId,
      recruit_code: item?.recruitCode,
      type: tabNum,
      score_type: 2,
      majorGroupCode: item?.majorGroupCode ?? '',
      majorFirst: item?.majorFirst ?? '',
      majorCode: item?.majorCode ?? '',
    })
    delete res.logo_host
    loaded = true
    dataEnroll = res || []
  } catch (err) {
    loaded = true
  }
}
let tabList = $ref<any>([
  {
    label: '卓越目标·冲0',
    value: 0,
  },
  {
    label: '冲刺目标·稳0',
    value: 1,
  },
  {
    label: '基础目标·保0',
    value: 2,
  },
])
const enrollHeaderStyle = {
  background: '#FFFFFF',
  color: '#666666',
  'font-size': '13px',
  height: '30px',
}
const pageOption = reactive({
  page: 1,
  page_size: 10,
  total: 0,
})
watch(
  () => tabNum,
  () => {
    pageOption.page = 1
    pageOption.total = 0
    loading = true
    dataList = []
    nextTick(() => {
      document.getElementById('scroll')?.scrollIntoView({
        behavior: 'smooth',
      })
    })
    if (tabNum !== null && !dataList?.length) {
      getDataList()
    }
  },
  {
    immediate: true,
  },
)
function onSchoolClick(school) {
  $g.flutter('nativeRoute', {
    name: '/collegeDetail',
    backCallJsRefresh: false, //返回后要不要刷新,
    params: {
      collegeId: school?.universityId, //注意这里是String
      collegeName: school?.universityName,
    },
  })
}
function onInterClick() {
  $g.flutter('nativeRoute', {
    name: '/foreignCollege',
    backCallJsRefresh: false, //返回后要不要刷新
  })
}
/* 获取列表 */
async function getDataList() {
  try {
    const currentNum = tabNum
    const res = await getUniversityVolunteer({
      ...pageOption,
      score_type: 2,
      type: tabNum,
    })
    if (currentNum != tabNum) {
      return
    }
    logo_host = res?.logo_host
    const resTotal = {
      0: res?.csize || 0,
      1: res?.wsize || 0,
      2: res?.bsize || 0,
    }
    if (res) {
      tabList[2].label = `基础目标·保(${res?.bsize || 0})`
      tabList[1].label = `冲刺目标·稳(${res?.wsize || 0})`
      tabList[0].label = `卓越目标·冲(${res?.csize || 0})`
    }
    const array = res?.plans?.length ? res?.plans : []
    dataList = [...dataList, ...array]
    pageOption.total = resTotal[tabNum]
    loading = false
  } catch (err) {
    loading = false
  }
}
async function pulldown() {
  pageOption.page = 1
  pageOption.total = 0
  dataList = []
  if (tabNum !== null) {
    getDataList()
  }
}
async function pullup() {
  pageOption.page += 1
  if (tabNum !== null) {
    getDataList()
  }
}
let tableOption = reactive<Record<string, any>>({
  data: [],
  column: [
    {
      prop: 'year',
      label: '年份',
      minWidth: '60px',
    },
    {
      prop: 'types',
      label: '科类',
      minWidth: '60px',
    },
    {
      prop: 'recruitNum',
      label: '录取',
      minWidth: '60px',
    },
    {
      prop: 'minScore',
      label: '最低分',
      minWidth: '60px',
    },
    {
      prop: 'minRank',
      label: '最低位次',
      minWidth: '60px',
    },
  ],
})
let imgList = $ref({
  风险极小: {
    url: $g.tool.getFileUrl('aiStudentPartner/riskSmall.png'),
    color: '#4DE352',
  },
  风险适中: {
    url: $g.tool.getFileUrl('aiStudentPartner/riskMiddle.png'),
    color: '#6EB9FF',
  },
  风险极大: {
    url: $g.tool.getFileUrl('aiStudentPartner/riskLarge.png'),
    color: '#F2B93E ',
  },
  风险大: {
    url: $g.tool.getFileUrl('aiStudentPartner/hongchong.png'),
    color: '#FF9A80',
  },
  风险小: {
    url: $g.tool.getFileUrl('aiStudentPartner/lvbao.png'),
    color: '#17E9AF',
  },
})
</script>
<style scoped lang="scss">
:deep() {
  .van-tabs__nav--card {
    border: none;
    padding: 3px;
    border-radius: 8px;
  }
  .van-tab--card {
    border-right: none;
    font-size: 12px;
    border-radius: 6px;
  }
  .van-tab--card.van-tab--active {
    background-color: #ffffff;
    font-weight: 600;
    font-size: 12px;
  }
}
.list {
  overflow-y: auto;
  -ms-overflow-style: none;
  scrollbar-width: none !important;
  &::-webkit-scrollbar {
    width: 0 !important;
  }
}
.title::before {
  display: block;
  content: '';
  width: 4px;
  height: 14px;
  background: #708dfa;
  margin-right: 8px;
  border-radius: 30%;
}
.enroll-table {
  :deep() {
    table thead tr th:first-child {
      border-radius: 26px 0 0 26px;
    }

    table thead tr th:last-child {
      border-radius: 0 26px 26px 0;
    }
  }
}
</style>
