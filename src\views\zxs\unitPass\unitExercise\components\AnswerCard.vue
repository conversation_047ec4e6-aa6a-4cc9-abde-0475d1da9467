<template>
  <div>
    <div class="mr-20px text-center leading-[14px]" @click="showPop = true">
      <g-icon name="svg-unitPass-pen" size="20" />
      <span class="text-10px">答题卡</span>
    </div>
    <van-popup
      v-model:show="showPop"
      position="bottom"
      round
      :close-icon="$g.tool.getFileUrl('unitPass/close.png')"
      closeable
      class="myPop"
      safe-area-inset-bottom
      :lock-scroll="false"
    >
      <div class="px-16px pb-10px">
        <div
          class="flex mt-16px font-600 text-16px mb-24px lh-[22px] h-22px justify-center"
        >
          答题卡
        </div>
        <div class="flex flex-wrap gap-y-[18px] gap-x-[23px]">
          <div
            v-for="item in myQuestionList"
            :key="item.questionId"
            class="w-38px h-38px myNumber flex-cc"
            :class="{
              isSelected: item.isSelected,
              isChecked: curIndex == item.questionNumber - 1,
            }"
            @click="$emit('changeQuestion', item.questionNumber - 1)"
          >
            {{ item.questionNumber }}
          </div>
        </div>
        <van-button
          color="#217DFB"
          class="w-full mt-32px br-[20px]"
          @click="$emit('submit')"
          >提交</van-button
        >
      </div>
    </van-popup>
  </div>
</template>
<script setup lang="ts">
let showPop = $ref(false)

const props = defineProps({
  questionList: {
    type: Array<any>,
    required: true,
  },
  curIndex: {
    type: Number,
    default: 0,
  },
})

const myQuestionList = $computed(() =>
  props.questionList.map((v) => {
    return {
      ...v,
      questionId: v.paper_question_id ?? v.paperQuestionId,
      questionNumber: v.question_number ?? v.questionNumber,
    }
  }),
)
</script>
<style scoped lang="scss">
.myNumber {
  border-radius: 50%;
  border: 1px solid rgba(153, 153, 153, 0.65);
  color: #999999;
}

.isSelected {
  border: 1px solid #217dfb;
  color: #217dfb;
}

.isChecked {
  background-color: #217dfb;
  border: 1px solid #217dfb;
  color: #fff;
}
:deep() {
  .myPop {
    .van-icon__image {
      width: 14px;
      height: 14px;
    }
  }
}
</style>
