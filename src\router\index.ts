import { createRouter, createWebHashHistory } from 'vue-router'
import { setupPermissions } from './permissions'
import tool from '@/utils/tool'
import type { AppRouteRecordRaw } from '/#/router'
import Layout from '@/layout/index.vue'

export const routes: AppRouteRecordRaw[] = [
  {
    path: '/',
    name: 'Root',
    component: Layout,
    redirect: tool.isPCTest() ? '/debugging' : '',
    meta: { title: '根路由' },
    children: [
      {
        path: 'debugging',
        name: 'Debugging',
        component: () => import('@/views/debugging/index.vue'),
        meta: { title: '开发调试页面', public: true },
      },
      {
        path: '/preview',
        name: 'PreviewDoc',
        component: () => import('@/views/preview/index.vue'),
        meta: {
          title: '文档预览',
          debugQuery: {
            url: 'https://qm-cloud.oss-cn-chengdu.aliyuncs.com/resource/chapter/file/202209/20220915/d534a6a0ce11fcf1e9b21a879b037913.docx',
          },
        },
      },
      {
        path: '/demo',
        name: 'Demo',
        component: () => import('@/views/demo/index.vue'),
        meta: { title: 'Demo页面', public: true, keepAliveArr: ['PreviewDoc'] },
      },
      {
        path: '/refuelingBag',
        name: 'RefuelingBag',
        component: () => import('@/views/refuelingBag/index.vue'),
        meta: {
          title: '家长助考加油包页面',
          public: true,
        },
      },
    ],
  },
]

const modules = import.meta.glob('./modules/**', { eager: true })

Object.keys(modules).forEach((key) => {
  const item: any = modules[key]
  if (Array.isArray(item.default)) {
    routes.push(...item.default)
  } else {
    routes.push(item.default)
  }
})

const router = createRouter({
  history: createWebHashHistory('/v3/'),
  routes: [
    ...routes,
    {
      path: '/:catchAll(.*)',
      component: () => import('@/views/error/404.vue'),
      meta: {
        public: true,
      },
    },
  ],
})

export async function setupRouter(app: any) {
  setupPermissions(router)
  app.use(router)
  await router.isReady()
}

export default router
