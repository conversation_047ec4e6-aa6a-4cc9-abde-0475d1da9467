//提交做题的公共函数
//1-下划线版，2-驼峰法版，默认下划线

//获取提交大题的is_jump,参数为子题数组
export const getIsJump = (items, type = 1) => {
  if (type == 2) {
    return items.some((h) => h.isJump === 2) ? 2 : 1
  }
  return items.some((h) => h.is_jump === 2) ? 2 : 1
}

//获取提交大题的is_correct,参数为子题数组
export const getIsCorrect = (subItems, type = 1) => {
  return subItems.every((item) => getIsCorrectSub(item, type) === 2) ? 2 : 1
}

function ifChoice(k, type = 1) {
  if (type == 2) return [1, 2, 3].includes(k.jkNewQuestionTypeId)
  return [1, 2, 3].includes(k.jk_new_question_type_id)
}
//获取提交大题的子题is_correct
export const getIsCorrectSub = (k, type = 1) => {
  if (!getSubIsDone(k, type)) return 1
  if (type == 2) {
    if (ifChoice(k, type)) {
      return k.isJump === 2
        ? 1
        : JSON.stringify(k.questionAnswer.split('').sort()) ===
            JSON.stringify(k.answers.sort())
          ? 2
          : 1
    }
    return k.isCorrect ? k.isCorrect : 1
  }
  if (ifChoice(k, type)) {
    return k.is_jump === 2
      ? 1
      : JSON.stringify(k.question_answer.split('').sort()) ===
          JSON.stringify(k.answers.sort())
        ? 2
        : 1
  }
  return k.is_correct ? k.is_correct : 1
}

//获取提交大题下子题的学生答案
export const getSubAnswer = (k, type = 1) => {
  if (!getSubIsDone(k, type)) return ''
  if (type == 2) {
    if ([1, 2, 3].includes(k.jkNewQuestionTypeId)) {
      return (k.answers.length && k.answers.join()) || '我不会'
    }
    return k.isJump === 2 ? '我不会' : '线下作答'
  }
  if ([1, 2, 3].includes(k.jk_new_question_type_id)) {
    return (k.answers.length && k.answers.join()) || '我不会'
  }
  return k.is_jump === 2 ? '我不会' : '线下作答'
}

//获取小题是否做过的状态
export function getSubIsDone(k, type = 1) {
  if (type == 2) {
    if ([1, 2, 3].includes(k.jkNewQuestionTypeId)) {
      return k.answers.length || k.isJump === 2
    }
    return k.isJump === 2 || [1, 2].includes(k.isCorrect)
  }

  if ([1, 2, 3].includes(k.jk_new_question_type_id)) {
    return k.answers.length || k.is_jump === 2
  }
  return k.is_jump === 2 || [1, 2].includes(k.is_correct)
}
