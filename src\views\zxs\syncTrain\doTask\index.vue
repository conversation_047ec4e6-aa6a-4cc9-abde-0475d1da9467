<template>
  <div ref="myContainer">
    <g-navbar ref="navBarRef" customTitle="同步训练">
      <div v-if="curQuestion">
        <div
          class="flex-cc h-30px font-600 text-22px text-[#217DFB]"
          style="
            background: linear-gradient(
              270deg,
              #f4f9ff 0%,
              #d6e8fc 50%,
              #f3f8ff 100%
            );
          "
        >
          <span> {{ minute >= 10 ? minute : '0' + minute }}:</span>
          <span> {{ second >= 10 ? second : '0' + second }}</span>
        </div>
        <div class="py-17px bg-white pl-16px pr-14px flex-cc">
          <van-progress
            :percentage="percentage"
            stroke-width="12px"
            class="flex-1"
            :showPivot="false"
            style="background: #f3f3f3"
          />
          <div class="flex-shrink-0 ml-16px text-14px">
            <span class="text-[#333333]">{{ hasSelectedNum }}/</span>
            <span class="text-[#999999]">{{ questionList.length }}</span>
          </div>
        </div>
      </div></g-navbar
    >

    <g-loading v-if="loading"></g-loading>
    <div
      v-else-if="curQuestion"
      class="px-16px pt-14px"
      style="
        background: linear-gradient(
          180deg,
          #e3eeff 0%,
          #ffffff 4%,
          #ffffff 100%
        );
      "
    >
      <QuestionItem
        :question-item="curQuestion"
        showAiTeacher
        :isLastQuestion="curIndex == questionList.length - 1"
        @update-checked="handleUpdateChecked"
        @next-question="handleNextQuestion"
      >
        <template #bottomAction>
          <AnswerCard
            :question-list="questionList"
            :curIndex="curIndex"
            @change-question="(num) => (curIndex = num)"
            @submit="beforeSubmit"
          ></AnswerCard>
        </template>
      </QuestionItem>
    </div>

    <g-empty v-else></g-empty>
  </div>
</template>
<script setup lang="ts" name="DoTask">
import { getQuestionList, submitPaper, submitAnswer } from '@/api/syncTrain'
import { addNote } from '@/api/unitPass'
import {
  getIsJump,
  getIsCorrect,
  getIsCorrectSub,
  getSubAnswer,
  getSubIsDone,
} from '@/views/zxs/components/QuestionItem/submit'
import QuestionItem from '@/views/zxs/components/QuestionItem/Camel/QuestionItem.vue'
import AnswerCard from '@/views/zxs/unitPass/unitExercise/components/AnswerCard.vue'
let curIndex = $ref(-1)
let questionList = $ref<any>([])
let curQuestion = $ref<any>(null)
let homeworkTaskId = $ref<null>()
let minute = $ref(0)
let second = $ref(0)
let testTime = $ref(0)
let timer = $ref<any>()
let loading = $ref(true)
const route = useRoute()
const router = useRouter()
const navBarRef = $ref<any>(null)
const myContainer = $ref<any>(null)
let ownTestRecordId = route.query.ownTestRecordId ?? 6430
function interval() {
  timer = setTimeout(() => {
    second = second + 1
    if (second == 60) {
      second = 0
      minute += 1
    }
    interval()
  }, 1000)
}
const hasSelectedNum = $computed(
  () => questionList.filter((h) => h.isSelected).length,
)

const notCompleted = $computed(() => {
  if (!questionList.length) return true
  return questionList.some((item) => !item.isSelected)
})

function beforeSubmit() {
  if (notCompleted) {
    $g.showConfirmDialog({
      title: '提示',
      message:
        '您尚未完成所有试题，交卷后未作答的试题视为不会，是否确定提前交卷。',
    })
      .then(() => {
        onSubmitClick(1)
      })
      .catch(() => {})
  } else {
    onSubmitClick(1)
  }
}

function onSubmitClick(type = 1) {
  submitPaper({
    ownTestRecordId,
    testTime: minute * 60 + second,
    isUserSubmit: type == 1 ? 'YES' : 'NO',
    homeworkTaskId,
  })
    .then(() => {
      if (type == 1) {
        router.replace({
          name: 'TaskReport',
          query: {
            ...route.query,
            finish: 1,
            homeworkTaskId,
          },
        })
      }
    })
    .catch(async () => {
      clearTimer()
      getQuestion()
    })
}
const percentage = $computed(() =>
  $g
    .math(hasSelectedNum)
    .divide(questionList.length)
    .multiply(100)
    .toFixed(0)
    .value(),
)
async function handleUpdateChecked(item) {
  let params = {
    ownTestRecordId,
    paperQuestionId: item.paperQuestionId,
    isJump: getIsJump(item.subQuestionList, 2),
    isCorrect: getIsCorrect(item.subQuestionList, 2),
    subList: item.subQuestionList.map((h) => {
      let isDone = getSubIsDone(h, 2)
      return {
        paperSubQuestionId: h.paperSubQuestionId,
        isJump: isDone ? h.isJump : 2,
        isCorrect: getIsCorrectSub(h, 2),
        studentAnswer: getSubAnswer(h, 2),
        isResponse: isDone ? 2 : 1,
      }
    }),
  }
  await submitAnswer(params)
  savePhoto(item)
}

function handleNextQuestion() {
  if (curIndex == questionList.length - 1) {
    onSubmitClick()
    return
  }
  curIndex++
}

watch(
  () => curIndex,
  async () => {
    if (!questionList.length || curIndex >= questionList.length) {
      curQuestion = null
      return
    }
    curQuestion = questionList[curIndex]
    await nextTick()
    myContainer?.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    })
    $g.tool.renderMathjax()
  },
)

// 题目是否是选择题
function ifChoice(item) {
  return [1, 2, 3].includes(item.jkNewQuestionTypeId)
}

function ifSelected(item) {
  if (ifChoice(item)) {
    return item.answers.length || item.isJump === 2
  }
  return item.isJump === 2 || [1, 2].includes(item.isCorrect)
}
async function getQuestion() {
  loading = true
  try {
    let res = await getQuestionList({
      ownTestRecordId,
      homeworkTaskPaperId: route.query.homeworkTaskPaperId ?? 1844,
    })
    testTime = res.testTime ?? 0
    minute = Math.floor(res.testTime / 60)
    second = res.testTime % 60
    interval()
    homeworkTaskId = res.homeworkTaskId
    res.questionList?.forEach((item, index) => {
      item.index = index + 1
      item.answers = []
      item.isJump = 1
      item.subQuestionList?.forEach((item0) => {
        item0.answers =
          [1, 2, 3].includes(item0.jkNewQuestionTypeId) &&
          item0.studentAnswer &&
          !['我不会', '线下作答'].includes(item0.studentAnswer)
            ? item0.studentAnswer.split(',')
            : []

        item0.hasChecked = item0.answers.length > 0 || !!item0.studentAnswer
        item0.isJump = item0.isJump ?? 1
        item0.isCorrect = item0.isJump == 2 ? null : item0.isCorrect
        item0.options =
          item0.jkNewQuestionTypeId == 3
            ? [
                {
                  id: 1,
                  name: '√',
                  content: null,
                  selectId: item0.paperSubQuestionId + '1',
                },
                {
                  id: 2,
                  name: '×',
                  content: null,
                  selectId: item0.paperSubQuestionId + '1',
                },
              ]
            : Object.keys(item0)
                .filter(
                  (key) => key.includes('option') && key !== 'optionNumbers',
                )
                .map((realKey) => {
                  return {
                    name: realKey.replace('option', ''),
                    content: item0[realKey],
                  }
                })
                .filter((v) => v.content)
                .slice(0, item0.optionNumbers)
      })
      item.isSelected = item.subQuestionList?.every((subItem) =>
        ifSelected(subItem),
      )
    })
    questionList = res.questionList
    curIndex = 0
    await nextTick()
    navBarRef?.changeUpdateKey()
  } catch {
    questionList = []
    curIndex = -1
  } finally {
    loading = false
  }
}

onBeforeMount(() => {
  getQuestion()
  window.addEventListener('beforeunload', handleBeforeUnload)
})

function clearTimer() {
  clearTimeout(timer)
  minute = Math.floor(testTime / 60)
  second = testTime % 60
}
onBeforeRouteLeave(async (to, from, next) => {
  if (to.query.finish || to.name == 'AiTeacherMain') {
    window.addEventListener('beforeunload', handleBeforeUnload)
    next()
  } else {
    let confirmationMessage
    confirmationMessage = '本测验还未完成，确定退出？'
    if (window.confirm(confirmationMessage)) {
      onSubmitClick(2)
      next()
    } else {
      next(false)
    }
  }
})
onBeforeUnmount(() => {
  window.removeEventListener('beforeunload', handleBeforeUnload)
  clearTimer()
})

function handleBeforeUnload(event: any) {
  onSubmitClick(2)
  let confirmationMessage
  confirmationMessage = '本测验还未完成，确定退出？'
  event.returnValue = confirmationMessage
  return confirmationMessage
}

async function savePhoto(currentQues) {
  if (!currentQues.noteList || !currentQues.noteList.length) return
  let notes = [
    {
      questionId: currentQues.paper_question_id,
      noteUrls: currentQues.noteList.map((v) => v.noteUrl),
    },
  ]
  let params = {
    exerciseId: route.query.ownTestRecordId,
    type: 5,
    notes,
  }
  await addNote(params)
}
</script>
